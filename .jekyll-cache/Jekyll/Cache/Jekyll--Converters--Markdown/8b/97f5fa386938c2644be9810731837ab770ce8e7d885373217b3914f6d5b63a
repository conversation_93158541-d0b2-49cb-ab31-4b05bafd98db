I"k<h1 id="agent-orchestration--supervision-architecture">Agent Orchestration &amp; Supervision Architecture</h1>
<h2 id="foundation-patterns-guide">Foundation Patterns Guide</h2>

<blockquote>
  <p><strong>Canonical Reference</strong>: See <code class="language-plaintext highlighter-rouge">tech-framework.md</code> for authoritative technology stack specifications</p>
</blockquote>

<h2 id="executive-summary">Executive Summary</h2>

<p>This document defines foundational agent orchestration and supervision patterns using <PERSON><PERSON>’s actor model with Tokio runtime. Focus is on basic supervision trees, agent lifecycle management, and simple coordination patterns suitable for learning distributed systems concepts.</p>

<h2 id="1-basic-agent-architecture">1. Basic Agent Architecture</h2>

<h3 id="11-agent-types">1.1 Agent Types</h3>

<pre><code class="language-pseudocode">ENUM AgentType {
    SUPERVISOR,    // Manages other agents
    WORKER,        // Performs tasks
    COORDINATOR,   // Coordinates workflows
    MONITOR,       // Observes system state
    PLANNER,       // Decomposes goals into tasks
    EXECUTOR,      // Carries out atomic actions
    CRITIC,        // Validates outcomes
    ROUTER,        // Assigns tasks to agents
    MEMORY         // Stores and retrieves knowledge
}

INTERFACE Agent {
    FUNCTION start() -&gt; Result
    FUNCTION stop() -&gt; Result
    FUNCTION handleMessage(message: Message) -&gt; Result
    FUNCTION getStatus() -&gt; AgentStatus
}
</code></pre>

<h3 id="12-core-agent-role-taxonomies">1.2 Core Agent Role Taxonomies</h3>

<h4 id="planner-agent">Planner Agent</h4>
<ul>
  <li><strong>Purpose</strong>: Decomposes high-level goals into concrete subtasks</li>
  <li><strong>Interface Pattern</strong>:
```rust
trait Planner {
  async fn create_plan(&amp;self, goal: Goal) -&gt; Result&lt;TaskList, Error&gt;;
  async fn refine_plan(&amp;self, feedback: CriticFeedback) -&gt; Result&lt;TaskList, Error&gt;;
}</li>
</ul>

<p>struct TaskList {
    tasks: Vec<Task>,
    dependencies: HashMap&lt;TaskId, Vec<TaskId>&gt;,
    priority_order: Vec<TaskId>,
}</TaskId></TaskId></Task></p>
<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>
#### Executor Agent
- **Purpose**: Carries out atomic actions and subtasks
- **Interface Pattern**:
```rust
trait Executor {
    async fn execute_task(&amp;self, task: Task) -&gt; Result&lt;TaskOutput, Error&gt;;
    fn can_execute(&amp;self, task_type: &amp;TaskType) -&gt; bool;
}

enum TaskOutput {
    Success(Value),
    PartialResult(Value, Vec&lt;SubTask&gt;),
    Failed(Error),
}
</code></pre></div></div>

<h4 id="critic-agent">Critic Agent</h4>
<ul>
  <li><strong>Purpose</strong>: Validates outcomes against goals and quality criteria</li>
  <li><strong>Interface Pattern</strong>:
```rust
trait Critic {
  async fn evaluate(&amp;self, output: TaskOutput, criteria: QualityCriteria) -&gt; CriticFeedback;
  async fn validate_plan(&amp;self, plan: TaskList) -&gt; ValidationResult;
}</li>
</ul>

<p>struct CriticFeedback {
    score: f32,
    issues: Vec<Issue>,
    suggestions: Vec<Improvement>,
}</Improvement></Issue></p>
<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>
#### Router Agent
- **Purpose**: Assigns tasks to appropriate specialized agents
- **Interface Pattern**:
```rust
trait Router {
    async fn route_task(&amp;self, task: Task) -&gt; AgentId;
    async fn get_agent_capabilities(&amp;self, agent_id: AgentId) -&gt; Vec&lt;Capability&gt;;
    async fn balance_load(&amp;self, tasks: Vec&lt;Task&gt;) -&gt; HashMap&lt;AgentId, Vec&lt;Task&gt;&gt;;
}
</code></pre></div></div>

<h4 id="memory-agent">Memory Agent</h4>
<ul>
  <li><strong>Purpose</strong>: Stores and retrieves shared knowledge</li>
  <li><strong>Interface Pattern</strong>:
    <div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">trait</span> <span class="n">Memory</span> <span class="p">{</span>
  <span class="k">async</span> <span class="k">fn</span> <span class="nf">store</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">Value</span><span class="p">,</span> <span class="n">metadata</span><span class="p">:</span> <span class="n">Metadata</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">(),</span> <span class="n">Error</span><span class="o">&gt;</span><span class="p">;</span>
  <span class="k">async</span> <span class="k">fn</span> <span class="nf">retrieve</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="nb">String</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="p">(</span><span class="n">Value</span><span class="p">,</span> <span class="n">Metadata</span><span class="p">)</span><span class="o">&gt;</span><span class="p">;</span>
  <span class="k">async</span> <span class="k">fn</span> <span class="nf">query</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">pattern</span><span class="p">:</span> <span class="n">QueryPattern</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="p">(</span><span class="nb">String</span><span class="p">,</span> <span class="n">Value</span><span class="p">)</span><span class="o">&gt;</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div>    </div>
  </li>
</ul>

<h3 id="13-agent-lifecycle-state-machine">1.3 Agent Lifecycle State Machine</h3>

<h4 id="131-agent-state-schema">1.3.1 Agent State Schema</h4>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://json-schema.org/draft/2020-12/schema"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"$id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/agent-state"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Agent State Definition"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"current_state"</span><span class="p">,</span><span class="w"> </span><span class="s2">"previous_state"</span><span class="p">,</span><span class="w"> </span><span class="s2">"transition_timestamp"</span><span class="p">,</span><span class="w"> </span><span class="s2">"state_data"</span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"current_state"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"INITIALIZING"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RUNNING"</span><span class="p">,</span><span class="w"> </span><span class="s2">"PAUSED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"STOPPING"</span><span class="p">,</span><span class="w"> </span><span class="s2">"TERMINATED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ERROR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RESTARTING"</span><span class="p">],</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Current agent state"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"previous_state"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"INITIALIZING"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RUNNING"</span><span class="p">,</span><span class="w"> </span><span class="s2">"PAUSED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"STOPPING"</span><span class="p">,</span><span class="w"> </span><span class="s2">"TERMINATED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ERROR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RESTARTING"</span><span class="p">,</span><span class="w"> </span><span class="kc">null</span><span class="p">],</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Previous agent state (null for initial state)"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"transition_timestamp"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"When the state transition occurred"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"state_data"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"State-specific metadata"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"initialization_progress"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
          </span><span class="nl">"maximum"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Initialization completion percentage"</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"pause_reason"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"MANUAL"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RESOURCE_CONSTRAINT"</span><span class="p">,</span><span class="w"> </span><span class="s2">"DEPENDENCY_WAIT"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ERROR_RECOVERY"</span><span class="p">]</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"termination_reason"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"MANUAL"</span><span class="p">,</span><span class="w"> </span><span class="s2">"COMPLETED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ERROR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"TIMEOUT"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RESOURCE_EXHAUSTED"</span><span class="p">]</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"error_details"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"error_code"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"error_message"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"recovery_attempted"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"boolean"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"retry_count"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="p">}</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"restart_count"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Number of restarts for this agent instance"</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"transition_history"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"from_state"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"to_state"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"trigger"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"duration_ms"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="p">}</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"from_state"</span><span class="p">,</span><span class="w"> </span><span class="s2">"to_state"</span><span class="p">,</span><span class="w"> </span><span class="s2">"timestamp"</span><span class="p">]</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="nl">"maxItems"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Recent state transition history"</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h4 id="132-state-transition-rules-schema">1.3.2 State Transition Rules Schema</h4>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://json-schema.org/draft/2020-12/schema"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"$id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/state-transitions"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Agent State Transition Rules"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Valid state transitions and their constraints"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"INITIALIZING"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"allowed_transitions"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"RUNNING"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ERROR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"TERMINATED"</span><span class="p">]</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"uniqueItems"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"timeout_seconds"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="mi">30</span><span class="w"> </span><span class="p">},</span><span class="w">
        </span><span class="nl">"required_conditions"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"CONFIGURATION_LOADED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RESOURCES_ALLOCATED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"DEPENDENCIES_READY"</span><span class="p">]</span><span class="w"> </span><span class="p">}</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"RUNNING"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"allowed_transitions"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"PAUSED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"STOPPING"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ERROR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RESTARTING"</span><span class="p">]</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"uniqueItems"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"health_check_interval_seconds"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="mi">30</span><span class="w"> </span><span class="p">},</span><span class="w">
        </span><span class="nl">"max_idle_seconds"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="mi">300</span><span class="w"> </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"PAUSED"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"allowed_transitions"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"RUNNING"</span><span class="p">,</span><span class="w"> </span><span class="s2">"STOPPING"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ERROR"</span><span class="p">]</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"uniqueItems"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"max_pause_duration_seconds"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="mi">3600</span><span class="w"> </span><span class="p">},</span><span class="w">
        </span><span class="nl">"auto_resume_conditions"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"RESOURCE_AVAILABLE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"DEPENDENCY_RESOLVED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"MANUAL_TRIGGER"</span><span class="p">]</span><span class="w"> </span><span class="p">}</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"STOPPING"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"allowed_transitions"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"TERMINATED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ERROR"</span><span class="p">]</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"uniqueItems"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"graceful_shutdown_timeout_seconds"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="p">},</span><span class="w">
        </span><span class="nl">"force_kill_after_timeout"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"boolean"</span><span class="p">,</span><span class="w"> </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w"> </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"ERROR"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"allowed_transitions"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"RESTARTING"</span><span class="p">,</span><span class="w"> </span><span class="s2">"TERMINATED"</span><span class="p">]</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"uniqueItems"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"auto_restart_conditions"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"RECOVERABLE_ERROR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RETRY_LIMIT_NOT_EXCEEDED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"SUPERVISOR_POLICY"</span><span class="p">]</span><span class="w"> </span><span class="p">}</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"max_restart_attempts"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="w"> </span><span class="p">},</span><span class="w">
        </span><span class="nl">"restart_backoff_seconds"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="w"> </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"RESTARTING"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"allowed_transitions"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"INITIALIZING"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ERROR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"TERMINATED"</span><span class="p">]</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"uniqueItems"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"restart_timeout_seconds"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="w"> </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"TERMINATED"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"allowed_transitions"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">[],</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Terminal state - no transitions allowed"</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"cleanup_timeout_seconds"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="mi">30</span><span class="w"> </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h4 id="133-agent-lifecycle-management">1.3.3 Agent Lifecycle Management</h4>

<pre><code class="language-pseudocode">CLASS AgentLifecycle {
    PRIVATE state: AgentState
    PRIVATE transitionRules: StateTransitionRules
    
    FUNCTION transition(newState: AgentState, trigger: String) -&gt; Result {
        currentRule = transitionRules[state.current_state]
        
        IF NOT currentRule.allowed_transitions.contains(newState) THEN
            RETURN Failure("Invalid transition from " + state.current_state + " to " + newState)
        END IF
        
        IF NOT checkTransitionConditions(state.current_state, newState) THEN
            RETURN Failure("Transition conditions not met")
        END IF
        
        previousState = state.current_state
        state.previous_state = previousState
        state.current_state = newState
        state.transition_timestamp = NOW()
        
        recordTransitionHistory(previousState, newState, trigger)
        notifyObservers(state)
        
        RETURN Success()
    }
    
    FUNCTION checkTransitionConditions(fromState: String, toState: String) -&gt; Boolean {
        // Implementation-specific condition checking
        RETURN validateRequiredConditions(fromState, toState)
    }
}
</code></pre>

<h2 id="2-supervision-patterns">2. Supervision Patterns</h2>

<h3 id="21-hub-and-spoke-supervisor-pattern">2.1 Hub-and-Spoke Supervisor Pattern</h3>

<p>Central routing logic with domain-specific delegation:</p>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">trait</span> <span class="n">Supervisor</span> <span class="p">{</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">route_task</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">task</span><span class="p">:</span> <span class="n">Task</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="n">AgentId</span> <span class="p">{</span>
        <span class="c1">// Central routing logic</span>
        <span class="k">match</span> <span class="n">task</span><span class="py">.task_type</span> <span class="p">{</span>
            <span class="nn">TaskType</span><span class="p">::</span><span class="n">Research</span> <span class="k">=&gt;</span> <span class="k">self</span><span class="nf">.find_agent</span><span class="p">(</span><span class="s">"researcher"</span><span class="p">),</span>
            <span class="nn">TaskType</span><span class="p">::</span><span class="n">Code</span> <span class="k">=&gt;</span> <span class="k">self</span><span class="nf">.find_agent</span><span class="p">(</span><span class="s">"coder"</span><span class="p">),</span>
            <span class="n">_</span> <span class="k">=&gt;</span> <span class="k">self</span><span class="nf">.default_agent</span><span class="p">()</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="c1">// Anti-pattern: Monolithic supervisor</span>
<span class="c1">// ❌ Single supervisor managing all agents directly</span>
<span class="c1">// ✅ Hierarchical supervisors with domain-specific delegation</span>
</code></pre></div></div>

<h3 id="22-event-driven-message-bus-pattern">2.2 Event-Driven Message Bus Pattern</h3>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">struct</span> <span class="n">MessageBus</span> <span class="p">{</span>
    <span class="n">channels</span><span class="p">:</span> <span class="n">HashMap</span><span class="o">&lt;</span><span class="n">AgentId</span><span class="p">,</span> <span class="nn">mpsc</span><span class="p">::</span><span class="n">Sender</span><span class="o">&lt;</span><span class="n">Message</span><span class="o">&gt;&gt;</span><span class="p">,</span>
    <span class="n">event_loop</span><span class="p">:</span> <span class="nn">tokio</span><span class="p">::</span><span class="nn">task</span><span class="p">::</span><span class="n">JoinHandle</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">MessageBus</span> <span class="p">{</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">publish</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">msg</span><span class="p">:</span> <span class="n">Message</span><span class="p">)</span> <span class="p">{</span>
        <span class="k">match</span> <span class="n">msg</span><span class="py">.routing</span> <span class="p">{</span>
            <span class="nn">Routing</span><span class="p">::</span><span class="n">Broadcast</span> <span class="k">=&gt;</span> <span class="k">self</span><span class="nf">.broadcast_all</span><span class="p">(</span><span class="n">msg</span><span class="p">)</span><span class="k">.await</span><span class="p">,</span>
            <span class="nn">Routing</span><span class="p">::</span><span class="nf">Target</span><span class="p">(</span><span class="n">id</span><span class="p">)</span> <span class="k">=&gt;</span> <span class="k">self</span><span class="nf">.send_to</span><span class="p">(</span><span class="n">id</span><span class="p">,</span> <span class="n">msg</span><span class="p">)</span><span class="k">.await</span><span class="p">,</span>
            <span class="nn">Routing</span><span class="p">::</span><span class="n">RoundRobin</span> <span class="k">=&gt;</span> <span class="k">self</span><span class="nf">.next_agent</span><span class="p">(</span><span class="n">msg</span><span class="p">)</span><span class="k">.await</span><span class="p">,</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="c1">// Extension hook: Custom routing strategies</span>
<span class="k">trait</span> <span class="n">RoutingStrategy</span> <span class="p">{</span>
    <span class="k">fn</span> <span class="nf">select_recipient</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">msg</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Message</span><span class="p">,</span> <span class="n">agents</span><span class="p">:</span> <span class="o">&amp;</span><span class="p">[</span><span class="n">AgentId</span><span class="p">])</span> <span class="k">-&gt;</span> <span class="n">AgentId</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div></div>

<h3 id="23-basic-supervision-tree">2.3 Basic Supervision Tree</h3>

<pre><code class="language-pseudocode">CLASS Supervisor {
    PRIVATE children: Map&lt;String, Agent&gt;
    PRIVATE strategy: SupervisionStrategy
    
    FUNCTION supervise(child: Agent) {
        children.put(child.id, child)
        monitor(child)
    }
    
    FUNCTION handleChildFailure(childId: String, error: Error) {
        strategy.handle(childId, error, children)
    }
}

ENUM RestartStrategy {
    ONE_FOR_ONE,      // Restart only failed agent
    ALL_FOR_ONE,      // Restart all agents
    REST_FOR_ONE      // Restart failed and subsequent agents
}
</code></pre>

<h3 id="24-simple-restart-logic">2.4 Simple Restart Logic</h3>

<pre><code class="language-pseudocode">CLASS RestartPolicy {
    PRIVATE maxRestarts: Integer
    PRIVATE timeWindow: Duration
    PRIVATE restartCounts: Map&lt;String, List&lt;Timestamp&gt;&gt;
    
    FUNCTION shouldRestart(agentId: String) -&gt; Boolean {
        recentRestarts = countRecentRestarts(agentId, timeWindow)
        RETURN recentRestarts &lt; maxRestarts
    }
    
    FUNCTION recordRestart(agentId: String) {
        restartCounts[agentId].add(NOW())
    }
}
</code></pre>

<h2 id="3-message-passing-and-communication-patterns">3. Message Passing and Communication Patterns</h2>

<h3 id="31-direct-rpc-pattern">3.1 Direct RPC Pattern</h3>

<p>Point-to-point communication for immediate responses:</p>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">struct</span> <span class="n">DirectChannel</span> <span class="p">{</span>
    <span class="n">target_endpoint</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="n">timeout</span><span class="p">:</span> <span class="n">Duration</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">DirectChannel</span> <span class="p">{</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">call</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Response</span><span class="p">,</span> <span class="n">Error</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="c1">// Direct HTTP/gRPC call</span>
        <span class="k">let</span> <span class="n">client</span> <span class="o">=</span> <span class="nn">reqwest</span><span class="p">::</span><span class="nn">Client</span><span class="p">::</span><span class="nf">builder</span><span class="p">()</span>
            <span class="nf">.timeout</span><span class="p">(</span><span class="k">self</span><span class="py">.timeout</span><span class="p">)</span>
            <span class="nf">.build</span><span class="p">()</span><span class="o">?</span><span class="p">;</span>
        
        <span class="k">let</span> <span class="n">response</span> <span class="o">=</span> <span class="n">client</span>
            <span class="nf">.post</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.target_endpoint</span><span class="p">)</span>
            <span class="nf">.json</span><span class="p">(</span><span class="o">&amp;</span><span class="n">request</span><span class="p">)</span>
            <span class="nf">.send</span><span class="p">()</span>
            <span class="k">.await</span><span class="o">?</span><span class="p">;</span>
            
        <span class="nf">Ok</span><span class="p">(</span><span class="n">response</span><span class="nf">.json</span><span class="p">()</span><span class="k">.await</span><span class="o">?</span><span class="p">)</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h3 id="32-publishsubscribe-pattern">3.2 Publish/Subscribe Pattern</h3>

<p>Topic-based message distribution:</p>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">struct</span> <span class="n">PubSubBus</span> <span class="p">{</span>
    <span class="n">broker_url</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="n">subscriptions</span><span class="p">:</span> <span class="n">HashMap</span><span class="o">&lt;</span><span class="n">Topic</span><span class="p">,</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">CallbackFn</span><span class="o">&gt;&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">PubSubBus</span> <span class="p">{</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">publish</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">topic</span><span class="p">:</span> <span class="n">Topic</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="n">Message</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">(),</span> <span class="n">Error</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="c1">// Publish to broker (e.g., NATS)</span>
        <span class="k">let</span> <span class="n">nc</span> <span class="o">=</span> <span class="nn">nats</span><span class="p">::</span><span class="nf">connect</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.broker_url</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
        <span class="n">nc</span><span class="nf">.publish</span><span class="p">(</span><span class="o">&amp;</span><span class="n">topic</span><span class="nf">.as_str</span><span class="p">(),</span> <span class="o">&amp;</span><span class="n">message</span><span class="nf">.serialize</span><span class="p">()</span><span class="o">?</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>
    
    <span class="k">async</span> <span class="k">fn</span> <span class="n">subscribe</span><span class="o">&lt;</span><span class="n">F</span><span class="o">&gt;</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span> <span class="n">topic</span><span class="p">:</span> <span class="n">Topic</span><span class="p">,</span> <span class="n">callback</span><span class="p">:</span> <span class="n">F</span><span class="p">)</span> 
    <span class="k">where</span> <span class="n">F</span><span class="p">:</span> <span class="nf">Fn</span><span class="p">(</span><span class="n">Message</span><span class="p">)</span> <span class="o">+</span> <span class="nb">Send</span> <span class="o">+</span> <span class="k">'static</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">nc</span> <span class="o">=</span> <span class="nn">nats</span><span class="p">::</span><span class="nf">connect</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.broker_url</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
        <span class="k">let</span> <span class="n">sub</span> <span class="o">=</span> <span class="n">nc</span><span class="nf">.subscribe</span><span class="p">(</span><span class="o">&amp;</span><span class="n">topic</span><span class="nf">.as_str</span><span class="p">())</span><span class="o">?</span><span class="p">;</span>
        
        <span class="nn">tokio</span><span class="p">::</span><span class="nf">spawn</span><span class="p">(</span><span class="k">async</span> <span class="k">move</span> <span class="p">{</span>
            <span class="k">for</span> <span class="n">msg</span> <span class="k">in</span> <span class="n">sub</span><span class="nf">.messages</span><span class="p">()</span> <span class="p">{</span>
                <span class="nf">callback</span><span class="p">(</span><span class="nn">Message</span><span class="p">::</span><span class="nf">deserialize</span><span class="p">(</span><span class="o">&amp;</span><span class="n">msg</span><span class="py">.data</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">());</span>
            <span class="p">}</span>
        <span class="p">});</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h3 id="33-blackboard-pattern">3.3 Blackboard Pattern</h3>

<p>Shared memory coordination:</p>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">struct</span> <span class="n">Blackboard</span> <span class="p">{</span>
    <span class="n">store</span><span class="p">:</span> <span class="nb">Arc</span><span class="o">&lt;</span><span class="n">RwLock</span><span class="o">&lt;</span><span class="n">HashMap</span><span class="o">&lt;</span><span class="nb">String</span><span class="p">,</span> <span class="n">BlackboardEntry</span><span class="o">&gt;&gt;&gt;</span><span class="p">,</span>
    <span class="n">watchers</span><span class="p">:</span> <span class="nb">Arc</span><span class="o">&lt;</span><span class="n">RwLock</span><span class="o">&lt;</span><span class="n">HashMap</span><span class="o">&lt;</span><span class="n">Pattern</span><span class="p">,</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">WatcherFn</span><span class="o">&gt;&gt;&gt;&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">struct</span> <span class="n">BlackboardEntry</span> <span class="p">{</span>
    <span class="n">value</span><span class="p">:</span> <span class="n">Value</span><span class="p">,</span>
    <span class="n">timestamp</span><span class="p">:</span> <span class="n">Instant</span><span class="p">,</span>
    <span class="n">author</span><span class="p">:</span> <span class="n">AgentId</span><span class="p">,</span>
    <span class="n">version</span><span class="p">:</span> <span class="nb">u64</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">Blackboard</span> <span class="p">{</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">write</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">Value</span><span class="p">,</span> <span class="n">agent_id</span><span class="p">:</span> <span class="n">AgentId</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">(),</span> <span class="n">Error</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">store</span> <span class="o">=</span> <span class="k">self</span><span class="py">.store</span><span class="nf">.write</span><span class="p">()</span><span class="k">.await</span><span class="p">;</span>
        <span class="k">let</span> <span class="n">version</span> <span class="o">=</span> <span class="n">store</span><span class="nf">.get</span><span class="p">(</span><span class="o">&amp;</span><span class="n">key</span><span class="p">)</span><span class="nf">.map</span><span class="p">(|</span><span class="n">e</span><span class="p">|</span> <span class="n">e</span><span class="py">.version</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span><span class="nf">.unwrap_or</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span>
        
        <span class="n">store</span><span class="nf">.insert</span><span class="p">(</span><span class="n">key</span><span class="nf">.clone</span><span class="p">(),</span> <span class="n">BlackboardEntry</span> <span class="p">{</span>
            <span class="n">value</span><span class="p">:</span> <span class="n">value</span><span class="nf">.clone</span><span class="p">(),</span>
            <span class="n">timestamp</span><span class="p">:</span> <span class="nn">Instant</span><span class="p">::</span><span class="nf">now</span><span class="p">(),</span>
            <span class="n">author</span><span class="p">:</span> <span class="n">agent_id</span><span class="p">,</span>
            <span class="n">version</span><span class="p">,</span>
        <span class="p">});</span>
        
        <span class="c1">// Notify watchers</span>
        <span class="k">self</span><span class="nf">.notify_watchers</span><span class="p">(</span><span class="o">&amp;</span><span class="n">key</span><span class="p">,</span> <span class="o">&amp;</span><span class="n">value</span><span class="p">)</span><span class="k">.await</span><span class="p">;</span>
        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>
    
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">read</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">BlackboardEntry</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">self</span><span class="py">.store</span><span class="nf">.read</span><span class="p">()</span><span class="k">.await</span><span class="nf">.get</span><span class="p">(</span><span class="n">key</span><span class="p">)</span><span class="nf">.cloned</span><span class="p">()</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h3 id="34-complete-message-schema-definitions">3.4 Complete Message Schema Definitions</h3>

<h4 id="341-base-message-schema">3.4.1 Base Message Schema</h4>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://json-schema.org/draft/2020-12/schema"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"$id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/base-message"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Base Agent Message"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Core message structure for all agent communications"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"id"</span><span class="p">,</span><span class="w"> </span><span class="s2">"type"</span><span class="p">,</span><span class="w"> </span><span class="s2">"sender"</span><span class="p">,</span><span class="w"> </span><span class="s2">"timestamp"</span><span class="p">,</span><span class="w"> </span><span class="s2">"routing"</span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Unique message identifier"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Message type discriminator"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"payload"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Message-specific data"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"additionalProperties"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"sender"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"$ref"</span><span class="p">:</span><span class="w"> </span><span class="s2">"#/$defs/AgentId"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Agent ID of message sender"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"ISO 8601 timestamp of message creation"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"routing"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"$ref"</span><span class="p">:</span><span class="w"> </span><span class="s2">"#/$defs/RoutingInfo"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Message routing configuration"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"correlation_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Optional correlation ID for request/response tracking"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"ttl"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Time-to-live in seconds (0 = no expiration)"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="mi">300</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"priority"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
      </span><span class="nl">"maximum"</span><span class="p">:</span><span class="w"> </span><span class="mi">9</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Message priority (0=lowest, 9=highest)"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"$defs"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"AgentId"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^agent-[a-zA-Z0-9]{8}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{12}$"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Unique agent identifier"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"RoutingInfo"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"oneOf"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
        </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"const"</span><span class="p">:</span><span class="w"> </span><span class="s2">"broadcast"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"exclude"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
              </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
              </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"$ref"</span><span class="p">:</span><span class="w"> </span><span class="s2">"#/$defs/AgentId"</span><span class="w"> </span><span class="p">},</span><span class="w">
              </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Agents to exclude from broadcast"</span><span class="w">
            </span><span class="p">}</span><span class="w">
          </span><span class="p">},</span><span class="w">
          </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"type"</span><span class="p">]</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"const"</span><span class="p">:</span><span class="w"> </span><span class="s2">"target"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"target"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"$ref"</span><span class="p">:</span><span class="w"> </span><span class="s2">"#/$defs/AgentId"</span><span class="w"> </span><span class="p">}</span><span class="w">
          </span><span class="p">},</span><span class="w">
          </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"type"</span><span class="p">,</span><span class="w"> </span><span class="s2">"target"</span><span class="p">]</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"const"</span><span class="p">:</span><span class="w"> </span><span class="s2">"round_robin"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"pool"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
              </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
              </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"$ref"</span><span class="p">:</span><span class="w"> </span><span class="s2">"#/$defs/AgentId"</span><span class="w"> </span><span class="p">},</span><span class="w">
              </span><span class="nl">"minItems"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="w">
            </span><span class="p">}</span><span class="w">
          </span><span class="p">},</span><span class="w">
          </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"type"</span><span class="p">,</span><span class="w"> </span><span class="s2">"pool"</span><span class="p">]</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">]</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h4 id="342-system-messages-schema">3.4.2 System Messages Schema</h4>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://json-schema.org/draft/2020-12/schema"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"$id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/system-message"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"System Control Message"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"allOf"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
    </span><span class="p">{</span><span class="w"> </span><span class="nl">"$ref"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/base-message"</span><span class="w"> </span><span class="p">}</span><span class="w">
  </span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"START"</span><span class="p">,</span><span class="w"> </span><span class="s2">"STOP"</span><span class="p">,</span><span class="w"> </span><span class="s2">"PAUSE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RESUME"</span><span class="p">,</span><span class="w"> </span><span class="s2">"HEALTH_CHECK"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RESTART"</span><span class="p">,</span><span class="w"> </span><span class="s2">"SHUTDOWN"</span><span class="p">]</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"payload"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"reason"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Human-readable reason for the system command"</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"force"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"boolean"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Whether to force the action (bypass graceful shutdown)"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"timeout"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Timeout in seconds for the operation"</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h4 id="343-task-messages-schema">3.4.3 Task Messages Schema</h4>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://json-schema.org/draft/2020-12/schema"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"$id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/task-message"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Task Assignment and Execution Message"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"allOf"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
    </span><span class="p">{</span><span class="w"> </span><span class="nl">"$ref"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/base-message"</span><span class="w"> </span><span class="p">}</span><span class="w">
  </span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"TASK_ASSIGN"</span><span class="p">,</span><span class="w"> </span><span class="s2">"TASK_PROGRESS"</span><span class="p">,</span><span class="w"> </span><span class="s2">"TASK_COMPLETE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"TASK_FAILED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"TASK_CANCEL"</span><span class="p">]</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"payload"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"task_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Unique task identifier"</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"task_type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"RESEARCH"</span><span class="p">,</span><span class="w"> </span><span class="s2">"CODE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ANALYZE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"REVIEW"</span><span class="p">,</span><span class="w"> </span><span class="s2">"DEPLOY"</span><span class="p">,</span><span class="w"> </span><span class="s2">"MONITOR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"CUSTOM"</span><span class="p">]</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"maxLength"</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span><span class="p">,</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Human-readable task description"</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"requirements"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"capabilities"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
              </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
              </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
              </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Required agent capabilities"</span><span class="w">
            </span><span class="p">},</span><span class="w">
            </span><span class="nl">"resources"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
              </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
              </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
                </span><span class="nl">"memory_mb"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="p">},</span><span class="w">
                </span><span class="nl">"cpu_cores"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="p">},</span><span class="w">
                </span><span class="nl">"storage_mb"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="p">}</span><span class="w">
              </span><span class="p">}</span><span class="w">
            </span><span class="p">},</span><span class="w">
            </span><span class="nl">"deadline"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
              </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
              </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="p">,</span><span class="w">
              </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Task completion deadline"</span><span class="w">
            </span><span class="p">}</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"dependencies"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="w">
          </span><span class="p">},</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Task IDs that must complete before this task"</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"progress"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
          </span><span class="nl">"maximum"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Task completion percentage"</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"result"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Task execution result (for completion messages)"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"additionalProperties"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"error"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"details"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"recoverable"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"boolean"</span><span class="w"> </span><span class="p">}</span><span class="w">
          </span><span class="p">},</span><span class="w">
          </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"code"</span><span class="p">,</span><span class="w"> </span><span class="s2">"message"</span><span class="p">]</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"task_id"</span><span class="p">,</span><span class="w"> </span><span class="s2">"task_type"</span><span class="p">]</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h4 id="344-agent-communication-messages-schema">3.4.4 Agent Communication Messages Schema</h4>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://json-schema.org/draft/2020-12/schema"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"$id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/agent-communication"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Inter-Agent Communication Message"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"allOf"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
    </span><span class="p">{</span><span class="w"> </span><span class="nl">"$ref"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/base-message"</span><span class="w"> </span><span class="p">}</span><span class="w">
  </span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"REQUEST"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RESPONSE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"NOTIFICATION"</span><span class="p">,</span><span class="w"> </span><span class="s2">"COLLABORATION"</span><span class="p">]</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"payload"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"method"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Method or action being requested"</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"parameters"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Method parameters"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"additionalProperties"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"response_data"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Response payload (for RESPONSE type)"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"additionalProperties"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"status_code"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"HTTP-style status code for responses"</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"notification_type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"INFO"</span><span class="p">,</span><span class="w"> </span><span class="s2">"WARNING"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ERROR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"SUCCESS"</span><span class="p">],</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Type of notification"</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"collaboration_mode"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"PEER_REVIEW"</span><span class="p">,</span><span class="w"> </span><span class="s2">"PAIR_WORK"</span><span class="p">,</span><span class="w"> </span><span class="s2">"KNOWLEDGE_SHARE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"DELEGATION"</span><span class="p">]</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h4 id="345-supervision-messages-schema">3.4.5 Supervision Messages Schema</h4>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://json-schema.org/draft/2020-12/schema"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"$id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/supervision-message"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Agent Supervision and Lifecycle Message"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"allOf"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
    </span><span class="p">{</span><span class="w"> </span><span class="nl">"$ref"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/base-message"</span><span class="w"> </span><span class="p">}</span><span class="w">
  </span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"SPAWN"</span><span class="p">,</span><span class="w"> </span><span class="s2">"TERMINATE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RESTART"</span><span class="p">,</span><span class="w"> </span><span class="s2">"HEALTH_CHECK"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RESOURCE_UPDATE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"CAPABILITY_CHANGE"</span><span class="p">]</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"payload"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"agent_spec"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"agent_type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
              </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"SUPERVISOR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"WORKER"</span><span class="p">,</span><span class="w"> </span><span class="s2">"COORDINATOR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"MONITOR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"PLANNER"</span><span class="p">,</span><span class="w"> </span><span class="s2">"EXECUTOR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"CRITIC"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ROUTER"</span><span class="p">,</span><span class="w"> </span><span class="s2">"MEMORY"</span><span class="p">]</span><span class="w">
            </span><span class="p">},</span><span class="w">
            </span><span class="nl">"capabilities"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
              </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
              </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">}</span><span class="w">
            </span><span class="p">},</span><span class="w">
            </span><span class="nl">"configuration"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
              </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
              </span><span class="nl">"additionalProperties"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
            </span><span class="p">}</span><span class="w">
          </span><span class="p">},</span><span class="w">
          </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"agent_type"</span><span class="p">]</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"health_status"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"HEALTHY"</span><span class="p">,</span><span class="w"> </span><span class="s2">"DEGRADED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"UNHEALTHY"</span><span class="p">,</span><span class="w"> </span><span class="s2">"UNKNOWN"</span><span class="p">]</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"metrics"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"cpu_usage"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="nl">"maximum"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"memory_usage"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="nl">"maximum"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"active_tasks"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"messages_processed"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"error_rate"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="nl">"maximum"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="p">}</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"restart_reason"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"TIMEOUT"</span><span class="p">,</span><span class="w"> </span><span class="s2">"RESOURCE_EXHAUSTED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"FATAL_ERROR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"CONFIGURATION_CHANGE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"MANUAL"</span><span class="p">]</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h4 id="346-hook-integration-messages-schema">3.4.6 Hook Integration Messages Schema</h4>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://json-schema.org/draft/2020-12/schema"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"$id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/hook-message"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Claude-CLI Hook Integration Message"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"allOf"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
    </span><span class="p">{</span><span class="w"> </span><span class="nl">"$ref"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/base-message"</span><span class="w"> </span><span class="p">}</span><span class="w">
  </span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"PRE_TASK"</span><span class="p">,</span><span class="w"> </span><span class="s2">"POST_TASK"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ON_ERROR"</span><span class="p">,</span><span class="w"> </span><span class="s2">"ON_FILE_CHANGE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"STARTUP"</span><span class="p">]</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"payload"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"hook_context"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"cwd"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"project_config"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"environment"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
              </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
              </span><span class="nl">"additionalProperties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">}</span><span class="w">
            </span><span class="p">}</span><span class="w">
          </span><span class="p">},</span><span class="w">
          </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"cwd"</span><span class="p">]</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"task_info"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"task_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"agent_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"$ref"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/base-message#/$defs/AgentId"</span><span class="w"> </span><span class="p">}</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"file_changes"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
              </span><span class="nl">"path"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
              </span><span class="nl">"change_type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"CREATED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"MODIFIED"</span><span class="p">,</span><span class="w"> </span><span class="s2">"DELETED"</span><span class="p">]</span><span class="w"> </span><span class="p">},</span><span class="w">
              </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="w"> </span><span class="p">}</span><span class="w">
            </span><span class="p">},</span><span class="w">
            </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"path"</span><span class="p">,</span><span class="w"> </span><span class="s2">"change_type"</span><span class="p">]</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">},</span><span class="w">
        </span><span class="nl">"error_info"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"error_type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"stack_trace"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"context"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="w"> </span><span class="p">}</span><span class="w">
          </span><span class="p">},</span><span class="w">
          </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"error_type"</span><span class="p">,</span><span class="w"> </span><span class="s2">"message"</span><span class="p">]</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h3 id="35-enhanced-message-validation--processing">3.5 Enhanced Message Validation &amp; Processing</h3>

<h4 id="351-runtime-message-validation">3.5.1 Runtime Message Validation</h4>

<pre><code class="language-pseudocode">STRUCT MessageValidator {
    schemas: HashMap&lt;String, MessageSchema&gt;,
    validation_cache: Arc&lt;RwLock&lt;LruCache&lt;String, ValidationResult&gt;&gt;&gt;,
    custom_rules: Vec&lt;Box&lt;dyn ValidationRule&gt;&gt;,
    metrics: ValidationMetrics
}

TRAIT ValidationRule {
    FUNCTION rule_name(&amp;self) -&gt; &amp;str
    ASYNC FUNCTION validate(&amp;self, message: &amp;Message) -&gt; ValidationResult
    FUNCTION severity(&amp;self) -&gt; ValidationSeverity
}

IMPL MessageValidator {
    #[tracing::instrument(skip(self, message))]
    ASYNC FUNCTION validate_message(&amp;self, message: &amp;Message) -&gt; ValidationResult {
        // Check cache first
        cache_key = format!("{}-{}", message.message_type, message.content_hash())
        IF LET Some(cached_result) = self.validation_cache.read().await.get(&amp;cache_key) {
            RETURN cached_result.clone()
        }
        
        // Get schema for message type
        schema = self.schemas.get(&amp;message.message_type)
            .ok_or(ValidationError::UnknownMessageType(message.message_type.clone()))?
        
        validation_errors = Vec::new()
        
        // Validate required fields
        FOR field IN &amp;schema.required_fields {
            IF !message.has_field(&amp;field.name) {
                validation_errors.push(ValidationError::MissingField(field.name.clone()))
            } ELSE {
                field_value = message.get_field(&amp;field.name)
                IF !field.validates(field_value) {
                    validation_errors.push(ValidationError::InvalidField {
                        field: field.name.clone(),
                        expected: field.field_type.clone(),
                        actual: field_value.type_name()
                    })
                }
            }
        }
        
        // Apply custom validation rules
        FOR rule IN &amp;self.custom_rules {
            rule_result = rule.validate(message).await
            IF rule_result.is_invalid() {
                validation_errors.extend(rule_result.errors)
            }
        }
        
        result = IF validation_errors.is_empty() {
            ValidationResult::Valid
        } ELSE {
            ValidationResult::Invalid {
                errors: validation_errors,
                message_id: message.id.clone()
            }
        }
        
        // Cache result
        self.validation_cache.write().await.insert(cache_key, result.clone())
        self.metrics.record_validation(&amp;message.message_type, &amp;result)
        
        result
    }
}

// Built-in validation rules
STRUCT AgentCapabilityRule {
    capability_registry: Arc&lt;CapabilityRegistry&gt;
}

IMPL ValidationRule FOR AgentCapabilityRule {
    ASYNC FUNCTION validate(&amp;self, message: &amp;Message) -&gt; ValidationResult {
        // Validate sender has required capabilities for message type
        required_capabilities = self.get_required_capabilities(&amp;message.message_type)
        agent_capabilities = self.capability_registry.get_agent_capabilities(&amp;message.sender).await?
        
        missing_capabilities = required_capabilities.iter()
            .filter(|cap| !agent_capabilities.contains(cap))
            .cloned()
            .collect::&lt;Vec&lt;_&gt;&gt;()
        
        IF !missing_capabilities.is_empty() {
            ValidationResult::Invalid {
                errors: vec![ValidationError::InsufficientCapabilities {
                    agent_id: message.sender.clone(),
                    required: required_capabilities,
                    missing: missing_capabilities
                }],
                message_id: message.id.clone()
            }
        } ELSE {
            ValidationResult::Valid
        }
    }
}
</code></pre>

<h4 id="352-priority-based-mailbox-with-backpressure">3.5.2 Priority-Based Mailbox with Backpressure</h4>

<pre><code class="language-pseudocode">STRUCT AgentMailbox {
    priority_queues: [VecDeque&lt;Message&gt;; 5], // One queue per priority level
    capacity_per_priority: [usize; 5],
    total_capacity: usize,
    current_size: AtomicUsize,
    backpressure_strategy: BackpressureStrategy,
    message_validator: MessageValidator,
    metrics: MailboxMetrics,
    notification_channel: mpsc::Sender&lt;MailboxEvent&gt;
}

ENUM BackpressureStrategy {
    Block,                    // Block sender until space available
    Drop(MessagePriority),    // Drop messages below specified priority
    Overflow(usize),          // Allow temporary overflow up to limit
    Reject,                   // Reject new messages immediately
    SpillToSecondary(String)  // Spill to secondary storage
}

ENUM MailboxEvent {
    MessageEnqueued { message_id: String, priority: MessagePriority },
    MessageDequeued { message_id: String, wait_time: Duration },
    BackpressureActivated { strategy: String, queue_size: usize },
    MessageDropped { message_id: String, reason: String },
    QueueFull { priority: MessagePriority, size: usize }
}

IMPL AgentMailbox {
    #[tracing::instrument(skip(self, message))]
    ASYNC FUNCTION send(&amp;self, message: Message) -&gt; Result&lt;SendResult, MailboxError&gt; {
        // Validate message first
        validation_result = self.message_validator.validate_message(&amp;message).await
        IF !validation_result.is_valid() {
            self.metrics.record_validation_failure(&amp;message.message_type)
            RETURN Err(MailboxError::ValidationFailed(validation_result))
        }
        
        priority_index = message.priority as usize
        current_size = self.current_size.load(Ordering::Acquire)
        
        // Check if message has expired
        IF message.is_expired() {
            self.metrics.record_expired_message(&amp;message.message_type)
            RETURN Err(MailboxError::MessageExpired)
        }
        
        // Handle capacity constraints
        IF current_size &gt;= self.total_capacity {
            RETURN self.handle_capacity_exceeded(message).await
        }
        
        // Check priority-specific capacity
        priority_queue_size = self.priority_queues[priority_index].len()
        IF priority_queue_size &gt;= self.capacity_per_priority[priority_index] {
            RETURN self.handle_priority_queue_full(message, priority_index).await
        }
        
        // Enqueue message
        self.priority_queues[priority_index].push_back(message.clone())
        new_size = self.current_size.fetch_add(1, Ordering::Release) + 1
        
        // Send notification
        mailbox_event = MailboxEvent::MessageEnqueued {
            message_id: message.id.clone(),
            priority: message.priority
        }
        self.notification_channel.try_send(mailbox_event).ok() // Non-blocking
        
        // Record metrics
        self.metrics.record_message_enqueued(&amp;message.message_type, message.priority)
        
        tracing::debug!(\n            message_id = %message.id,\n            message_type = %message.message_type,\n            priority = ?message.priority,\n            queue_size = new_size,\n            \"Message enqueued successfully\"\n        )
        
        Ok(SendResult::Enqueued { queue_position: self.estimate_queue_position(&amp;message) })
    }
    
    #[tracing::instrument(skip(self))]
    ASYNC FUNCTION receive(&amp;self, timeout: Option&lt;Duration&gt;) -&gt; Result&lt;Message, MailboxError&gt; {\n        deadline = timeout.map(|t| Instant::now() + t)\n        start_time = Instant::now()\n        \n        LOOP {\n            // Check priority queues in order (highest priority first)\n            FOR priority_index IN 0..5 {\n                IF LET Some(message) = self.priority_queues[priority_index].pop_front() {\n                    self.current_size.fetch_sub(1, Ordering::Release)\n                    wait_time = start_time.elapsed()\n                    \n                    // Send notification\n                    mailbox_event = MailboxEvent::MessageDequeued {\n                        message_id: message.id.clone(),\n                        wait_time\n                    }\n                    self.notification_channel.try_send(mailbox_event).ok()\n                    \n                    // Record metrics\n                    self.metrics.record_message_dequeued(&amp;message.message_type, message.priority, wait_time)\n                    \n                    tracing::debug!(\n                        message_id = %message.id,\n                        message_type = %message.message_type,\n                        priority = ?message.priority,\n                        wait_time = ?wait_time,\n                        \"Message dequeued\"\n                    )\n                    \n                    RETURN Ok(message)\n                }\n            }\n            \n            // No messages available, check timeout\n            IF LET Some(deadline) = deadline {\n                IF Instant::now() &gt;= deadline {\n                    RETURN Err(MailboxError::ReceiveTimeout)\n                }\n            }\n            \n            // Wait for new messages with exponential backoff\n            wait_duration = Duration::from_millis(1 &lt;&lt; min(5, start_time.elapsed().as_millis() / 100))\n            tokio::time::sleep(wait_duration).await\n        }\n    }\n    \n    ASYNC FUNCTION handle_capacity_exceeded(&amp;self, message: Message) -&gt; Result&lt;SendResult, MailboxError&gt; {\n        MATCH &amp;self.backpressure_strategy {\n            BackpressureStrategy::Block =&gt; {\n                // Wait for space with exponential backoff\n                backoff = ExponentialBackoff::new(Duration::from_millis(1), Duration::from_secs(1))\n                \n                WHILE self.current_size.load(Ordering::Acquire) &gt;= self.total_capacity {\n                    tokio::time::sleep(backoff.next_delay()).await\n                }\n                \n                self.send(message).await\n            },\n            BackpressureStrategy::Drop(min_priority) =&gt; {\n                IF message.priority &gt;= *min_priority {\n                    // Try to make space by dropping lower priority messages\n                    dropped_count = self.drop_lower_priority_messages(message.priority)\n                    IF dropped_count &gt; 0 {\n                        self.send(message).await\n                    } ELSE {\n                        self.metrics.record_message_dropped(&amp;message.message_type, \"no_space_after_drop\")\n                        Err(MailboxError::Dropped(\"Unable to make space\".to_string()))\n                    }\n                } ELSE {\n                    self.metrics.record_message_dropped(&amp;message.message_type, \"low_priority\")\n                    Err(MailboxError::Dropped(\"Message priority too low\".to_string()))\n                }\n            },\n            BackpressureStrategy::Overflow(max_overflow) =&gt; {\n                current_overflow = self.current_size.load(Ordering::Acquire) - self.total_capacity\n                IF current_overflow &lt; *max_overflow {\n                    // Allow temporary overflow\n                    self.priority_queues[message.priority as usize].push_back(message.clone())\n                    self.current_size.fetch_add(1, Ordering::Release)\n                    \n                    // Schedule cleanup task\n                    self.schedule_overflow_cleanup().await\n                    \n                    Ok(SendResult::Overflowed { overflow_count: current_overflow + 1 })\n                } ELSE {\n                    Err(MailboxError::OverflowLimitExceeded)\n                }\n            },\n            BackpressureStrategy::Reject =&gt; {\n                self.metrics.record_message_rejected(&amp;message.message_type)\n                Err(MailboxError::Rejected(\"Mailbox at capacity\".to_string()))\n            },\n            BackpressureStrategy::SpillToSecondary(storage_path) =&gt; {\n                // Spill to secondary storage\n                spill_result = self.spill_to_storage(&amp;message, storage_path).await\n                MATCH spill_result {\n                    Ok(spill_id) =&gt; Ok(SendResult::Spilled { spill_id }),\n                    Err(e) =&gt; Err(MailboxError::SpillFailed(e))\n                }\n            }\n        }\n    }\n}

### 3.6 Agent State Machine Management

#### 3.6.1 State Persistence with Event Sourcing

```pseudocode
// Event sourcing for agent state management
STRUCT AgentStateManager {
    event_store: EventStore,
    current_states: Arc&lt;RwLock&lt;HashMap&lt;AgentId, AgentState&gt;&gt;&gt;,
    state_cache: Arc&lt;RwLock&lt;LruCache&lt;AgentId, CachedState&gt;&gt;&gt;,
    snapshot_store: SnapshotStore,
    state_validators: Vec&lt;Box&lt;dyn StateValidator&gt;&gt;
}

TRAIT StateEvent {
    FUNCTION event_type(&amp;self) -&gt; &amp;str
    FUNCTION agent_id(&amp;self) -&gt; &amp;AgentId
    FUNCTION apply_to_state(&amp;self, state: &amp;mut AgentState) -&gt; Result&lt;()&gt;
    FUNCTION timestamp(&amp;self) -&gt; DateTime&lt;Utc&gt;
    FUNCTION version(&amp;self) -&gt; u64
}

ENUM AgentStateEvent {
    AgentSpawned {
        agent_id: AgentId,
        agent_type: String,
        configuration: AgentConfig,
        supervisor_id: Option&lt;AgentId&gt;,
        timestamp: DateTime&lt;Utc&gt;
    },
    StateTransition {
        agent_id: AgentId,
        from_state: AgentLifecycleState,
        to_state: AgentLifecycleState,
        reason: String,
        timestamp: DateTime&lt;Utc&gt;
    },
    TaskAssigned {
        agent_id: AgentId,
        task_id: TaskId,
        task_spec: TaskSpecification,
        timestamp: DateTime&lt;Utc&gt;
    },
    TaskCompleted {
        agent_id: AgentId,
        task_id: TaskId,
        result: TaskResult,
        execution_time: Duration,
        timestamp: DateTime&lt;Utc&gt;
    },
    CapabilityUpdated {
        agent_id: AgentId,
        capability: String,
        operation: CapabilityOperation,
        timestamp: DateTime&lt;Utc&gt;
    },
    ConfigurationChanged {
        agent_id: AgentId,
        config_changes: HashMap&lt;String, Value&gt;,
        timestamp: DateTime&lt;Utc&gt;
    }
}

IMPL AgentStateManager {
    #[tracing::instrument(skip(self, event))]
    ASYNC FUNCTION persist_state_event(&amp;self, event: Box&lt;dyn StateEvent&gt;) -&gt; Result&lt;()&gt; {
        // Validate event
        FOR validator IN &amp;self.state_validators {
            validator.validate_event(&amp;*event)?
        }
        
        // Store event in event store
        event_id = self.event_store.append_event(event.clone()).await?
        
        // Apply event to current state
        current_states = self.current_states.write().await
        state = current_states.entry(event.agent_id().clone())
            .or_insert_with(|| AgentState::new(event.agent_id().clone()))
        
        event.apply_to_state(state)?
        state.last_event_id = Some(event_id)
        state.version += 1
        
        // Update cache
        self.state_cache.write().await.insert(
            event.agent_id().clone(),
            CachedState {
                state: state.clone(),
                cached_at: Instant::now(),
                ttl: Duration::from_secs(300)
            }
        )
        
        // Check if snapshot needed
        IF state.version % SNAPSHOT_INTERVAL == 0 {
            self.create_state_snapshot(event.agent_id().clone()).await?
        }
        
        tracing::info!(
            agent_id = %event.agent_id(),
            event_type = %event.event_type(),
            version = state.version,
            "State event persisted"
        )
        
        Ok(())
    }
    
    #[tracing::instrument(skip(self))]
    ASYNC FUNCTION restore_agent_state(&amp;self, agent_id: &amp;AgentId) -&gt; Result&lt;AgentState&gt; {
        // Check cache first
        IF LET Some(cached) = self.state_cache.read().await.get(agent_id) {
            IF !cached.is_expired() {
                RETURN Ok(cached.state.clone())
            }
        }
        
        // Try to load latest snapshot
        state = IF LET Some(snapshot) = self.snapshot_store.load_latest(agent_id).await? {
            snapshot.state
        } ELSE {
            AgentState::new(agent_id.clone())
        }
        
        // Apply events since snapshot
        last_event_id = state.last_event_id.clone()
        events = self.event_store.load_events_since(agent_id, last_event_id).await?
        
        FOR event IN events {
            event.apply_to_state(&amp;mut state)?
            state.version += 1
        }
        
        // Cache restored state
        self.state_cache.write().await.insert(
            agent_id.clone(),
            CachedState {
                state: state.clone(),
                cached_at: Instant::now(),
                ttl: Duration::from_secs(300)
            }
        )
        
        // Update current states
        self.current_states.write().await.insert(agent_id.clone(), state.clone())
        
        Ok(state)
    }
    
    ASYNC FUNCTION create_state_snapshot(&amp;self, agent_id: AgentId) -&gt; Result&lt;()&gt; {
        current_states = self.current_states.read().await
        IF LET Some(state) = current_states.get(&amp;agent_id) {
            snapshot = StateSnapshot {
                agent_id: agent_id.clone(),
                state: state.clone(),
                created_at: Utc::now(),
                version: state.version
            }
            
            self.snapshot_store.save_snapshot(snapshot).await?
            tracing::info!(agent_id = %agent_id, version = state.version, "State snapshot created")
        }
        
        Ok(())
    }
}
</code></pre>

<h4 id="362-state-machine-with-supervision-integration">3.6.2 State Machine with Supervision Integration</h4>

<pre><code class="language-pseudocode">STRUCT AgentStateMachine {
    current_state: Arc&lt;RwLock&lt;AgentLifecycleState&gt;&gt;,
    allowed_transitions: HashMap&lt;AgentLifecycleState, Vec&lt;AgentLifecycleState&gt;&gt;,
    state_handlers: HashMap&lt;AgentLifecycleState, Box&lt;dyn StateHandler&gt;&gt;,
    transition_guards: HashMap&lt;(AgentLifecycleState, AgentLifecycleState), Box&lt;dyn TransitionGuard&gt;&gt;,
    state_manager: AgentStateManager,
    metrics: StateMachineMetrics
}

ENUM AgentLifecycleState {
    Initializing,
    Idle,
    Busy(TaskId),
    Paused,
    Error(ErrorInfo),
    Restarting,
    Terminating,
    Terminated
}

TRAIT StateHandler {
    ASYNC FUNCTION on_enter(&amp;self, agent_id: &amp;AgentId, previous_state: Option&lt;AgentLifecycleState&gt;) -&gt; Result&lt;()&gt;
    ASYNC FUNCTION on_exit(&amp;self, agent_id: &amp;AgentId, next_state: AgentLifecycleState) -&gt; Result&lt;()&gt;
    ASYNC FUNCTION handle_message(&amp;self, agent_id: &amp;AgentId, message: Message) -&gt; Result&lt;StateHandlerResult&gt;
}

TRAIT TransitionGuard {
    ASYNC FUNCTION can_transition(&amp;self, agent_id: &amp;AgentId, from: &amp;AgentLifecycleState, to: &amp;AgentLifecycleState) -&gt; bool
    FUNCTION guard_name(&amp;self) -&gt; &amp;str
}

ENUM StateHandlerResult {
    Handled,
    TransitionTo(AgentLifecycleState),
    Forward(Message),
    Error(String)
}

IMPL AgentStateMachine {
    #[tracing::instrument(skip(self))]
    ASYNC FUNCTION transition_to(
        &amp;self, 
        agent_id: &amp;AgentId, 
        new_state: AgentLifecycleState,
        reason: String
    ) -&gt; Result&lt;(), StateMachineError&gt; {
        current_state_guard = self.current_state.read().await
        current_state = current_state_guard.clone()
        drop(current_state_guard)
        
        // Check if transition is allowed
        allowed_states = self.allowed_transitions.get(&amp;current_state)
            .ok_or(StateMachineError::InvalidCurrentState(current_state.clone()))?
        
        IF !allowed_states.contains(&amp;new_state) {
            RETURN Err(StateMachineError::TransitionNotAllowed {
                from: current_state,
                to: new_state,
                allowed: allowed_states.clone()
            })
        }
        
        // Check transition guards
        guard_key = (current_state.clone(), new_state.clone())
        IF LET Some(guard) = self.transition_guards.get(&amp;guard_key) {
            can_transition = guard.can_transition(agent_id, &amp;current_state, &amp;new_state).await
            IF !can_transition {
                RETURN Err(StateMachineError::TransitionBlocked {
                    guard: guard.guard_name().to_string(),
                    reason: format!("Transition from {:?} to {:?} blocked", current_state, new_state)
                })
            }
        }
        
        // Execute state exit handler
        IF LET Some(current_handler) = self.state_handlers.get(&amp;current_state) {
            current_handler.on_exit(agent_id, new_state.clone()).await?
        }
        
        // Perform the transition
        {
            current_state_guard = self.current_state.write().await
            *current_state_guard = new_state.clone()
        }
        
        // Persist state change event
        state_event = AgentStateEvent::StateTransition {
            agent_id: agent_id.clone(),
            from_state: current_state.clone(),
            to_state: new_state.clone(),
            reason,
            timestamp: Utc::now()
        }
        
        self.state_manager.persist_state_event(Box::new(state_event)).await?
        
        // Execute state enter handler
        IF LET Some(new_handler) = self.state_handlers.get(&amp;new_state) {
            new_handler.on_enter(agent_id, Some(current_state.clone())).await?
        }
        
        // Record metrics
        self.metrics.record_state_transition(&amp;current_state, &amp;new_state)
        
        tracing::info!(
            agent_id = %agent_id,
            from_state = ?current_state,
            to_state = ?new_state,
            "State transition completed"
        )
        
        Ok(())
    }
    
    #[tracing::instrument(skip(self, message))]
    ASYNC FUNCTION handle_message(&amp;self, agent_id: &amp;AgentId, message: Message) -&gt; Result&lt;()&gt; {
        current_state = self.current_state.read().await.clone()
        
        IF LET Some(handler) = self.state_handlers.get(&amp;current_state) {
            result = handler.handle_message(agent_id, message.clone()).await?
            
            MATCH result {
                StateHandlerResult::Handled =&gt; {
                    tracing::debug!(agent_id = %agent_id, "Message handled in current state")
                },
                StateHandlerResult::TransitionTo(new_state) =&gt; {
                    self.transition_to(agent_id, new_state, "Message-triggered transition".to_string()).await?
                },
                StateHandlerResult::Forward(forwarded_message) =&gt; {
                    // Forward message to supervisor or other agents
                    self.forward_message(agent_id, forwarded_message).await?
                },
                StateHandlerResult::Error(error_msg) =&gt; {
                    error_state = AgentLifecycleState::Error(ErrorInfo {
                        error_type: "MessageHandlingError".to_string(),
                        message: error_msg,
                        timestamp: Utc::now(),
                        recoverable: true
                    })
                    self.transition_to(agent_id, error_state, "Message handling error".to_string()).await?
                }
            }
        } ELSE {
            tracing::warn!(
                agent_id = %agent_id,
                state = ?current_state,
                "No handler for current state"
            )
        }
        
        Ok(())
    }
}

// Built-in state handlers
STRUCT IdleStateHandler;

IMPL StateHandler FOR IdleStateHandler {
    ASYNC FUNCTION on_enter(&amp;self, agent_id: &amp;AgentId, _previous_state: Option&lt;AgentLifecycleState&gt;) -&gt; Result&lt;()&gt; {
        tracing::info!(agent_id = %agent_id, "Agent entered idle state")
        Ok(())
    }
    
    ASYNC FUNCTION handle_message(&amp;self, _agent_id: &amp;AgentId, message: Message) -&gt; Result&lt;StateHandlerResult&gt; {
        MATCH message.message_type.as_str() {
            "TASK_ASSIGN" =&gt; {
                task_id = message.payload.get("task_id").unwrap().as_str().unwrap()
                Ok(StateHandlerResult::TransitionTo(AgentLifecycleState::Busy(task_id.to_string())))
            },
            "PAUSE" =&gt; {
                Ok(StateHandlerResult::TransitionTo(AgentLifecycleState::Paused))
            },
            "TERMINATE" =&gt; {
                Ok(StateHandlerResult::TransitionTo(AgentLifecycleState::Terminating))
            },
            _ =&gt; Ok(StateHandlerResult::Handled)
        }
    }
}
</code></pre>

<h2 id="4-task-distribution">4. Task Distribution</h2>

<h3 id="41-work-queue-pattern">4.1 Work Queue Pattern</h3>

<pre><code class="language-pseudocode">CLASS TaskDistributor {
    PRIVATE workers: List&lt;WorkerAgent&gt;
    PRIVATE taskQueue: Queue&lt;Task&gt;
    
    FUNCTION distribute(task: Task) {
        // Find available worker
        worker = findAvailableWorker()
        
        IF worker != NULL THEN
            worker.assign(task)
        ELSE
            taskQueue.enqueue(task)
        END IF
    }
    
    FUNCTION findAvailableWorker() -&gt; WorkerAgent? {
        FOR worker IN workers {
            IF worker.isAvailable() THEN
                RETURN worker
            END IF
        }
        RETURN NULL
    }
}
</code></pre>

<h3 id="42-load-balancing">4.2 Load Balancing</h3>

<pre><code class="language-pseudocode">CLASS LoadBalancer {
    PRIVATE agents: List&lt;Agent&gt;
    PRIVATE currentIndex: Integer = 0
    
    FUNCTION selectAgent() -&gt; Agent {
        // Round-robin selection
        agent = agents[currentIndex]
        currentIndex = (currentIndex + 1) % agents.size()
        RETURN agent
    }
    
    FUNCTION selectLeastLoaded() -&gt; Agent {
        // Select agent with fewest active tasks
        RETURN agents.minBy(agent =&gt; agent.getActiveTaskCount())
    }
}
</code></pre>

<h2 id="5-coordination-patterns">5. Coordination Patterns</h2>

<h3 id="51-request-response-pattern">5.1 Request-Response Pattern</h3>

<pre><code class="language-pseudocode">CLASS RequestHandler {
    PRIVATE pendingRequests: Map&lt;UUID, ResponseCallback&gt;
    
    FUNCTION sendRequest(target: Agent, request: Request) -&gt; Future&lt;Response&gt; {
        requestId = generateUUID()
        message = RequestMessage(requestId, request)
        
        future = Future&lt;Response&gt;()
        pendingRequests[requestId] = future.callback
        
        target.send(message)
        RETURN future
    }
    
    FUNCTION handleResponse(response: Response) {
        callback = pendingRequests.remove(response.requestId)
        IF callback != NULL THEN
            callback(response)
        END IF
    }
}
</code></pre>

<h3 id="52-publish-subscribe-pattern">5.2 Publish-Subscribe Pattern</h3>

<pre><code class="language-pseudocode">CLASS EventBus {
    PRIVATE subscribers: Map&lt;String, List&lt;Agent&gt;&gt;
    
    FUNCTION subscribe(topic: String, agent: Agent) {
        IF NOT subscribers.contains(topic) THEN
            subscribers[topic] = []
        END IF
        subscribers[topic].add(agent)
    }
    
    FUNCTION publish(topic: String, event: Event) {
        agents = subscribers.get(topic, [])
        FOR agent IN agents {
            agent.send(EventMessage(topic, event))
        }
    }
}
</code></pre>

<h2 id="6-agent-discovery">6. Agent Discovery</h2>

<h3 id="61-registry-pattern">6.1 Registry Pattern</h3>

<pre><code class="language-pseudocode">CLASS AgentRegistry {
    PRIVATE agents: Map&lt;String, AgentInfo&gt;
    
    FUNCTION register(agent: Agent) {
        info = AgentInfo{
            id: agent.id,
            type: agent.type,
            capabilities: agent.getCapabilities(),
            address: agent.getAddress()
        }
        agents[agent.id] = info
    }
    
    FUNCTION discover(criteria: SearchCriteria) -&gt; List&lt;AgentInfo&gt; {
        RETURN agents.values()
            .filter(info =&gt; criteria.matches(info))
    }
}
</code></pre>

<h3 id="62-health-monitoring">6.2 Health Monitoring</h3>

<pre><code class="language-pseudocode">CLASS HealthMonitor {
    PRIVATE agents: Map&lt;String, HealthStatus&gt;
    PRIVATE checkInterval: Duration
    
    FUNCTION monitorHealth() {
        EVERY checkInterval {
            FOR agent IN agents.keys() {
                status = checkAgentHealth(agent)
                agents[agent] = status
                
                IF status == UNHEALTHY THEN
                    notifySupervisor(agent)
                END IF
            }
        }
    }
    
    FUNCTION checkAgentHealth(agentId: String) -&gt; HealthStatus {
        TRY {
            response = sendHealthCheck(agentId)
            RETURN response.status
        } CATCH (timeout) {
            RETURN UNHEALTHY
        }
    }
}
</code></pre>

<h2 id="7-simple-workflow-orchestration">7. Simple Workflow Orchestration</h2>

<h3 id="71-sequential-workflow">7.1 Sequential Workflow</h3>

<pre><code class="language-pseudocode">CLASS SequentialWorkflow {
    PRIVATE steps: List&lt;WorkflowStep&gt;
    
    FUNCTION execute(context: WorkflowContext) -&gt; Result {
        FOR step IN steps {
            result = step.execute(context)
            
            IF result.isFailure() THEN
                RETURN result
            END IF
            
            context.updateWith(result.output)
        }
        
        RETURN Success(context)
    }
}
</code></pre>

<h3 id="72-parallel-workflow">7.2 Parallel Workflow</h3>

<pre><code class="language-pseudocode">CLASS ParallelWorkflow {
    PRIVATE tasks: List&lt;Task&gt;
    
    FUNCTION execute() -&gt; Result&lt;List&lt;TaskResult&gt;&gt; {
        futures = []
        
        FOR task IN tasks {
            future = async {
                agent = selectAgent(task.requirements)
                RETURN agent.execute(task)
            }
            futures.add(future)
        }
        
        // Wait for all tasks to complete
        results = awaitAll(futures)
        RETURN Success(results)
    }
}
</code></pre>

<h2 id="8-error-handling">8. Error Handling</h2>

<h3 id="81-basic-error-recovery">8.1 Basic Error Recovery</h3>

<pre><code class="language-pseudocode">CLASS ErrorHandler {
    FUNCTION handleAgentError(agent: Agent, error: Error) {
        SWITCH error.type {
            CASE TIMEOUT:
                restartAgent(agent)
            CASE RESOURCE_EXHAUSTED:
                pauseAgent(agent)
                scheduleRetry(agent, delay: 30.seconds)
            CASE FATAL:
                terminateAgent(agent)
                notifySupervisor(agent, error)
            DEFAULT:
                logError(agent, error)
        }
    }
}
</code></pre>

<h3 id="82-circuit-breaker-pattern">8.2 Circuit Breaker Pattern</h3>

<pre><code class="language-pseudocode">CLASS CircuitBreaker {
    PRIVATE state: BreakerState = CLOSED
    PRIVATE failureCount: Integer = 0
    PRIVATE threshold: Integer = 5
    PRIVATE timeout: Duration = 60.seconds
    
    FUNCTION call(operation: Function) -&gt; Result {
        IF state == OPEN THEN
            IF timeoutExpired() THEN
                state = HALF_OPEN
            ELSE
                RETURN Failure("Circuit breaker open")
            END IF
        END IF
        
        TRY {
            result = operation()
            IF state == HALF_OPEN THEN
                state = CLOSED
                failureCount = 0
            END IF
            RETURN result
        } CATCH (error) {
            failureCount += 1
            IF failureCount &gt;= threshold THEN
                state = OPEN
                scheduleTimeout()
            END IF
            THROW error
        }
    }
}
</code></pre>

<h2 id="9-basic-metrics">9. Basic Metrics</h2>

<h3 id="91-agent-metrics">9.1 Agent Metrics</h3>

<pre><code class="language-pseudocode">CLASS AgentMetrics {
    PRIVATE messageCount: Counter
    PRIVATE taskCompletionTime: Histogram
    PRIVATE errorRate: Gauge
    
    FUNCTION recordMessage() {
        messageCount.increment()
    }
    
    FUNCTION recordTaskCompletion(duration: Duration) {
        taskCompletionTime.observe(duration)
    }
    
    FUNCTION updateErrorRate(rate: Float) {
        errorRate.set(rate)
    }
}
</code></pre>

<h2 id="10-spawn-and-resource-management-patterns">10. Spawn and Resource Management Patterns</h2>

<h3 id="101-role-based-spawning">10.1 Role-Based Spawning</h3>

<p>Dynamic team composition based on project requirements:</p>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">enum</span> <span class="n">AgentRole</span> <span class="p">{</span>
    <span class="n">ProductManager</span> <span class="p">{</span> <span class="n">sop</span><span class="p">:</span> <span class="n">StandardProcedure</span> <span class="p">},</span>
    <span class="n">Architect</span> <span class="p">{</span> <span class="n">design_patterns</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">Pattern</span><span class="o">&gt;</span> <span class="p">},</span>
    <span class="n">Engineer</span> <span class="p">{</span> <span class="n">toolchain</span><span class="p">:</span> <span class="n">ToolSet</span> <span class="p">},</span>
<span class="p">}</span>

<span class="k">struct</span> <span class="n">RoleSpawner</span> <span class="p">{</span>
    <span class="n">role_registry</span><span class="p">:</span> <span class="n">HashMap</span><span class="o">&lt;</span><span class="nb">String</span><span class="p">,</span> <span class="n">AgentRole</span><span class="o">&gt;</span><span class="p">,</span>
    
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">spawn_team</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">project</span><span class="p">:</span> <span class="n">ProjectSpec</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="n">Team</span> <span class="p">{</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">agents</span> <span class="o">=</span> <span class="nd">vec!</span><span class="p">[];</span>
        
        <span class="c1">// Spawn based on project needs</span>
        <span class="k">for</span> <span class="n">role</span> <span class="k">in</span> <span class="n">project</span><span class="nf">.required_roles</span><span class="p">()</span> <span class="p">{</span>
            <span class="n">agents</span><span class="nf">.push</span><span class="p">(</span><span class="k">self</span><span class="nf">.spawn_role</span><span class="p">(</span><span class="n">role</span><span class="p">)</span><span class="k">.await</span><span class="p">);</span>
        <span class="p">}</span>
        
        <span class="nn">Team</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">agents</span><span class="p">,</span> <span class="n">project</span><span class="nf">.coordination_mode</span><span class="p">())</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="c1">// Anti-pattern: Static role assignment</span>
<span class="c1">// ❌ Fixed teams for all projects</span>
<span class="c1">// ✅ Dynamic team composition based on task analysis</span>
</code></pre></div></div>

<h3 id="102-resource-bounded-spawning">10.2 Resource-Bounded Spawning</h3>

<p>Prevent uncontrolled agent proliferation:</p>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// ❌ BAD: Unlimited spawning</span>
<span class="k">async</span> <span class="k">fn</span> <span class="nf">handle_task</span><span class="p">(</span><span class="n">task</span><span class="p">:</span> <span class="n">Task</span><span class="p">)</span> <span class="p">{</span>
    <span class="k">for</span> <span class="n">subtask</span> <span class="k">in</span> <span class="n">task</span><span class="nf">.decompose</span><span class="p">()</span> <span class="p">{</span>
        <span class="nf">spawn_agent</span><span class="p">(</span><span class="n">subtask</span><span class="p">);</span> <span class="c1">// No limits!</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="c1">// ✅ GOOD: Resource-bounded spawning</span>
<span class="k">struct</span> <span class="n">SpawnController</span> <span class="p">{</span>
    <span class="n">max_agents</span><span class="p">:</span> <span class="nb">usize</span><span class="p">,</span>
    <span class="n">active</span><span class="p">:</span> <span class="nb">Arc</span><span class="o">&lt;</span><span class="n">AtomicUsize</span><span class="o">&gt;</span><span class="p">,</span>
    
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">spawn_bounded</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">role</span><span class="p">:</span> <span class="n">AgentRole</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Agent</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">if</span> <span class="k">self</span><span class="py">.active</span><span class="nf">.load</span><span class="p">(</span><span class="nn">Ordering</span><span class="p">::</span><span class="n">SeqCst</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="k">self</span><span class="py">.max_agents</span> <span class="p">{</span>
            <span class="k">return</span> <span class="nf">Err</span><span class="p">(</span><span class="s">"Agent limit reached"</span><span class="p">);</span>
        <span class="p">}</span>
        <span class="c1">// Spawn with cleanup on drop</span>
        <span class="nf">Ok</span><span class="p">(</span><span class="nn">BoundedAgent</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">role</span><span class="p">,</span> <span class="k">self</span><span class="py">.active</span><span class="nf">.clone</span><span class="p">()))</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h3 id="103-context-management">10.3 Context Management</h3>

<p>Prevent memory overflow with windowed context:</p>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// ❌ BAD: Accumulating unlimited context</span>
<span class="k">struct</span> <span class="n">NaiveAgent</span> <span class="p">{</span>
    <span class="n">context</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">Message</span><span class="o">&gt;</span><span class="p">,</span> <span class="c1">// Grows forever</span>
<span class="p">}</span>

<span class="c1">// ✅ GOOD: Windowed context with summarization</span>
<span class="k">struct</span> <span class="n">SmartAgent</span> <span class="p">{</span>
    <span class="n">recent_context</span><span class="p">:</span> <span class="n">VecDeque</span><span class="o">&lt;</span><span class="n">Message</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="n">context_summary</span><span class="p">:</span> <span class="n">Summary</span><span class="p">,</span>
    <span class="n">max_context_size</span><span class="p">:</span> <span class="nb">usize</span><span class="p">,</span>

    <span class="k">fn</span> <span class="nf">add_context</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span> <span class="n">msg</span><span class="p">:</span> <span class="n">Message</span><span class="p">)</span> <span class="p">{</span>
        <span class="k">self</span><span class="py">.recent_context</span><span class="nf">.push_back</span><span class="p">(</span><span class="n">msg</span><span class="p">);</span>
        <span class="k">if</span> <span class="k">self</span><span class="py">.recent_context</span><span class="nf">.len</span><span class="p">()</span> <span class="o">&gt;</span> <span class="k">self</span><span class="py">.max_context_size</span> <span class="p">{</span>
            <span class="k">self</span><span class="nf">.summarize_old_context</span><span class="p">();</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h3 id="104-claude-cli-parallel-execution-integration">10.4 Claude-CLI Parallel Execution Integration</h3>

<p>Support for Claude-CLI’s built-in parallel execution capabilities through Task tool coordination:</p>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// Enhanced Claude-CLI parallel execution pattern</span>
<span class="k">struct</span> <span class="n">ClaudeTaskOutputParser</span> <span class="p">{</span>
    <span class="n">task_regex</span><span class="p">:</span> <span class="n">Regex</span><span class="p">,</span>
    <span class="n">nats_client</span><span class="p">:</span> <span class="nn">async_nats</span><span class="p">::</span><span class="n">Client</span><span class="p">,</span>
    <span class="n">metrics</span><span class="p">:</span> <span class="n">ParallelExecutionMetrics</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">ClaudeTaskOutputParser</span> <span class="p">{</span>
    <span class="k">fn</span> <span class="nf">new</span><span class="p">(</span><span class="n">nats_client</span><span class="p">:</span> <span class="nn">async_nats</span><span class="p">::</span><span class="n">Client</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="k">Self</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">task_regex</span> <span class="o">=</span> <span class="nn">Regex</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="s">r"● Task\((?:Patch Agent )?(\d+|[^)]+)\)"</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">();</span>

        <span class="k">Self</span> <span class="p">{</span>
            <span class="n">task_regex</span><span class="p">,</span>
            <span class="n">nats_client</span><span class="p">,</span>
            <span class="n">metrics</span><span class="p">:</span> <span class="nn">ParallelExecutionMetrics</span><span class="p">::</span><span class="nf">new</span><span class="p">(),</span>
        <span class="p">}</span>
    <span class="p">}</span>

    <span class="c1">// Parse multiple Claude-CLI task output formats</span>
    <span class="k">fn</span> <span class="nf">parse_task_output</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">line</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">TaskInfo</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">caps</span><span class="p">)</span> <span class="o">=</span> <span class="k">self</span><span class="py">.task_regex</span><span class="nf">.captures</span><span class="p">(</span><span class="n">line</span><span class="p">)</span> <span class="p">{</span>
            <span class="k">let</span> <span class="n">task_identifier</span> <span class="o">=</span> <span class="n">caps</span><span class="nf">.get</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">()</span><span class="nf">.as_str</span><span class="p">();</span>

            <span class="c1">// Handle both numeric agent IDs and task descriptions</span>
            <span class="k">if</span> <span class="k">let</span> <span class="nf">Ok</span><span class="p">(</span><span class="n">agent_id</span><span class="p">)</span> <span class="o">=</span> <span class="n">task_identifier</span><span class="py">.parse</span><span class="p">::</span><span class="o">&lt;</span><span class="nb">u32</span><span class="o">&gt;</span><span class="p">()</span> <span class="p">{</span>
                <span class="nf">Some</span><span class="p">(</span><span class="nn">TaskInfo</span><span class="p">::</span><span class="nf">AgentId</span><span class="p">(</span><span class="n">agent_id</span><span class="p">))</span>
            <span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
                <span class="nf">Some</span><span class="p">(</span><span class="nn">TaskInfo</span><span class="p">::</span><span class="nf">Description</span><span class="p">(</span><span class="n">task_identifier</span><span class="nf">.to_string</span><span class="p">()))</span>
            <span class="p">}</span>
        <span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
            <span class="nb">None</span>
        <span class="p">}</span>
    <span class="p">}</span>

    <span class="c1">// Route task output to appropriate NATS subjects</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">route_task_output</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">task_info</span><span class="p">:</span> <span class="n">TaskInfo</span><span class="p">,</span> <span class="n">line</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">(),</span> <span class="n">RoutingError</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">subject</span> <span class="o">=</span> <span class="k">match</span> <span class="n">task_info</span> <span class="p">{</span>
            <span class="nn">TaskInfo</span><span class="p">::</span><span class="nf">AgentId</span><span class="p">(</span><span class="n">id</span><span class="p">)</span> <span class="k">=&gt;</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"agents.{}.output"</span><span class="p">,</span> <span class="n">id</span><span class="p">),</span>
            <span class="nn">TaskInfo</span><span class="p">::</span><span class="nf">Description</span><span class="p">(</span><span class="n">desc</span><span class="p">)</span> <span class="k">=&gt;</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"tasks.{}.output"</span><span class="p">,</span> <span class="n">desc</span><span class="nf">.replace</span><span class="p">(</span><span class="s">" "</span><span class="p">,</span> <span class="s">"_"</span><span class="p">)),</span>
        <span class="p">};</span>

        <span class="k">let</span> <span class="n">event</span> <span class="o">=</span> <span class="n">TaskOutputEvent</span> <span class="p">{</span>
            <span class="n">task_info</span><span class="p">,</span>
            <span class="n">output_line</span><span class="p">:</span> <span class="n">line</span><span class="nf">.to_string</span><span class="p">(),</span>
            <span class="n">timestamp</span><span class="p">:</span> <span class="nn">Utc</span><span class="p">::</span><span class="nf">now</span><span class="p">(),</span>
        <span class="p">};</span>

        <span class="k">self</span><span class="py">.nats_client</span><span class="nf">.publish</span><span class="p">(</span><span class="n">subject</span><span class="p">,</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nf">to_vec</span><span class="p">(</span><span class="o">&amp;</span><span class="n">event</span><span class="p">)</span><span class="o">?</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>
        <span class="k">self</span><span class="py">.metrics</span><span class="nf">.increment_routed_messages</span><span class="p">();</span>

        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="k">enum</span> <span class="n">TaskInfo</span> <span class="p">{</span>
    <span class="nf">AgentId</span><span class="p">(</span><span class="nb">u32</span><span class="p">),</span>
    <span class="nf">Description</span><span class="p">(</span><span class="nb">String</span><span class="p">),</span>
<span class="p">}</span>

<span class="k">struct</span> <span class="n">TaskOutputEvent</span> <span class="p">{</span>
    <span class="n">task_info</span><span class="p">:</span> <span class="n">TaskInfo</span><span class="p">,</span>
    <span class="n">output_line</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="n">timestamp</span><span class="p">:</span> <span class="n">DateTime</span><span class="o">&lt;</span><span class="n">Utc</span><span class="o">&gt;</span><span class="p">,</span>
<span class="p">}</span>
</code></pre></div></div>

<p><strong>Parallel Coordination Patterns:</strong></p>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// Multi-agent coordination using Claude-CLI Task tool</span>
<span class="k">impl</span> <span class="n">AgentOrchestrator</span> <span class="p">{</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">coordinate_parallel_claude_tasks</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span> <span class="n">tasks</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">TaskRequest</span><span class="o">&gt;</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nb">Vec</span><span class="o">&lt;</span><span class="n">TaskResult</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">CoordinationError</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="c1">// Build parallel task prompt for Claude-CLI</span>
        <span class="k">let</span> <span class="n">parallel_prompt</span> <span class="o">=</span> <span class="k">self</span><span class="nf">.build_parallel_task_prompt</span><span class="p">(</span><span class="o">&amp;</span><span class="n">tasks</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Spawn Claude-CLI agent with task coordination</span>
        <span class="k">let</span> <span class="n">claude_agent_id</span> <span class="o">=</span> <span class="k">self</span><span class="nf">.spawn_claude_cli_agent</span><span class="p">(</span><span class="n">SpawnRequest</span> <span class="p">{</span>
            <span class="n">prompt</span><span class="p">:</span> <span class="n">parallel_prompt</span><span class="p">,</span>
            <span class="n">max_concurrent_tasks</span><span class="p">:</span> <span class="n">tasks</span><span class="nf">.len</span><span class="p">(),</span>
            <span class="n">coordination_mode</span><span class="p">:</span> <span class="nn">CoordinationMode</span><span class="p">::</span><span class="n">Parallel</span><span class="p">,</span>
        <span class="p">})</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Monitor parallel task execution</span>
        <span class="k">let</span> <span class="n">task_results</span> <span class="o">=</span> <span class="k">self</span><span class="nf">.monitor_parallel_execution</span><span class="p">(</span><span class="n">claude_agent_id</span><span class="p">,</span> <span class="n">tasks</span><span class="nf">.len</span><span class="p">())</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>

        <span class="nf">Ok</span><span class="p">(</span><span class="n">task_results</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="k">fn</span> <span class="nf">build_parallel_task_prompt</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">tasks</span><span class="p">:</span> <span class="o">&amp;</span><span class="p">[</span><span class="n">TaskRequest</span><span class="p">])</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nb">String</span><span class="p">,</span> <span class="n">PromptError</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">task_descriptions</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span> <span class="o">=</span> <span class="n">tasks</span><span class="nf">.iter</span><span class="p">()</span>
            <span class="nf">.enumerate</span><span class="p">()</span>
            <span class="nf">.map</span><span class="p">(|(</span><span class="n">i</span><span class="p">,</span> <span class="n">task</span><span class="p">)|</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"Task {}: {}"</span><span class="p">,</span> <span class="n">i</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span> <span class="n">task</span><span class="py">.description</span><span class="p">))</span>
            <span class="nf">.collect</span><span class="p">();</span>

        <span class="nf">Ok</span><span class="p">(</span><span class="nd">format!</span><span class="p">(</span>
            <span class="s">"Execute these {} tasks in parallel using the Task tool:</span><span class="se">\n</span><span class="s">{}"</span><span class="p">,</span>
            <span class="n">tasks</span><span class="nf">.len</span><span class="p">(),</span>
            <span class="n">task_descriptions</span><span class="nf">.join</span><span class="p">(</span><span class="s">"</span><span class="se">\n</span><span class="s">"</span><span class="p">)</span>
        <span class="p">))</span>
    <span class="p">}</span>

    <span class="k">async</span> <span class="k">fn</span> <span class="nf">monitor_parallel_execution</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">claude_agent_id</span><span class="p">:</span> <span class="n">AgentId</span><span class="p">,</span> <span class="n">expected_tasks</span><span class="p">:</span> <span class="nb">usize</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nb">Vec</span><span class="o">&lt;</span><span class="n">TaskResult</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">MonitoringError</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">task_results</span> <span class="o">=</span> <span class="nn">Vec</span><span class="p">::</span><span class="nf">new</span><span class="p">();</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">completed_tasks</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>

        <span class="c1">// Subscribe to task output subjects</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">task_subscriber</span> <span class="o">=</span> <span class="k">self</span><span class="py">.nats_client</span><span class="nf">.subscribe</span><span class="p">(</span><span class="s">"tasks.*.output"</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Monitor until all tasks complete</span>
        <span class="k">while</span> <span class="n">completed_tasks</span> <span class="o">&lt;</span> <span class="n">expected_tasks</span> <span class="p">{</span>
            <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">message</span><span class="p">)</span> <span class="o">=</span> <span class="n">task_subscriber</span><span class="nf">.next</span><span class="p">()</span><span class="k">.await</span> <span class="p">{</span>
                <span class="k">let</span> <span class="n">event</span><span class="p">:</span> <span class="n">TaskOutputEvent</span> <span class="o">=</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nf">from_slice</span><span class="p">(</span><span class="o">&amp;</span><span class="n">message</span><span class="py">.payload</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

                <span class="c1">// Check for task completion indicators</span>
                <span class="k">if</span> <span class="k">self</span><span class="nf">.is_task_complete</span><span class="p">(</span><span class="o">&amp;</span><span class="n">event</span><span class="py">.output_line</span><span class="p">)</span> <span class="p">{</span>
                    <span class="n">task_results</span><span class="nf">.push</span><span class="p">(</span><span class="nn">TaskResult</span><span class="p">::</span><span class="nf">from_output_event</span><span class="p">(</span><span class="n">event</span><span class="p">));</span>
                    <span class="n">completed_tasks</span> <span class="o">+=</span> <span class="mi">1</span><span class="p">;</span>
                <span class="p">}</span>
            <span class="p">}</span>
        <span class="p">}</span>

        <span class="nf">Ok</span><span class="p">(</span><span class="n">task_results</span><span class="p">)</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<p><strong>Key Integration Points:</strong></p>
<ul>
  <li><strong>Task Tool Integration</strong>: Leverages Claude-CLI’s built-in Task tool for parallel execution</li>
  <li><strong>Output Pattern Recognition</strong>: Handles both <code class="language-plaintext highlighter-rouge">Task(Patch Agent &lt;n&gt;)</code> and <code class="language-plaintext highlighter-rouge">Task(Description)</code> formats</li>
  <li><strong>NATS Subject Routing</strong>: Routes to <code class="language-plaintext highlighter-rouge">agents.{id}.output</code> and <code class="language-plaintext highlighter-rouge">tasks.{name}.output</code> subjects</li>
  <li><strong>Supervision Compatibility</strong>: Integrates with existing Tokio supervision trees</li>
  <li><strong>Resource Management</strong>: Coordinates with agent pool limits (25-30 concurrent agents)</li>
  <li><strong>Memory Persistence</strong>: Compatible with existing Postgres/JetStream KV storage</li>
</ul>

<h2 id="11-tool-bus-integration-patterns">11. Tool-Bus Integration Patterns</h2>

<h3 id="111-shared-tool-registry">11.1 Shared Tool Registry</h3>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">struct</span> <span class="n">ToolBus</span> <span class="p">{</span>
    <span class="n">tools</span><span class="p">:</span> <span class="nb">Arc</span><span class="o">&lt;</span><span class="n">RwLock</span><span class="o">&lt;</span><span class="n">HashMap</span><span class="o">&lt;</span><span class="n">ToolId</span><span class="p">,</span> <span class="nb">Box</span><span class="o">&lt;</span><span class="k">dyn</span> <span class="n">Tool</span><span class="o">&gt;&gt;&gt;&gt;</span><span class="p">,</span>
    <span class="n">permissions</span><span class="p">:</span> <span class="n">HashMap</span><span class="o">&lt;</span><span class="n">AgentId</span><span class="p">,</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">ToolId</span><span class="o">&gt;&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">trait</span> <span class="n">Tool</span><span class="p">:</span> <span class="nb">Send</span> <span class="o">+</span> <span class="nb">Sync</span> <span class="p">{</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">execute</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">params</span><span class="p">:</span> <span class="n">Value</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Value</span><span class="o">&gt;</span><span class="p">;</span>
    <span class="k">fn</span> <span class="nf">schema</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="n">ToolSchema</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// Extension mechanism</span>
<span class="k">impl</span> <span class="n">ToolBus</span> <span class="p">{</span>
    <span class="k">fn</span> <span class="n">register_tool</span><span class="o">&lt;</span><span class="n">T</span><span class="p">:</span> <span class="n">Tool</span> <span class="o">+</span> <span class="k">'static</span><span class="o">&gt;</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span> <span class="n">id</span><span class="p">:</span> <span class="n">ToolId</span><span class="p">,</span> <span class="n">tool</span><span class="p">:</span> <span class="n">T</span><span class="p">)</span> <span class="p">{</span>
        <span class="k">self</span><span class="py">.tools</span><span class="nf">.write</span><span class="p">()</span><span class="nf">.unwrap</span><span class="p">()</span><span class="nf">.insert</span><span class="p">(</span><span class="n">id</span><span class="p">,</span> <span class="nn">Box</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">tool</span><span class="p">));</span>
    <span class="p">}</span>
    
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">call</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">agent_id</span><span class="p">:</span> <span class="n">AgentId</span><span class="p">,</span> <span class="n">tool_id</span><span class="p">:</span> <span class="n">ToolId</span><span class="p">,</span> <span class="n">params</span><span class="p">:</span> <span class="n">Value</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Value</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="c1">// Permission check</span>
        <span class="k">if</span> <span class="o">!</span><span class="k">self</span><span class="nf">.has_permission</span><span class="p">(</span><span class="n">agent_id</span><span class="p">,</span> <span class="n">tool_id</span><span class="p">)</span> <span class="p">{</span>
            <span class="k">return</span> <span class="nf">Err</span><span class="p">(</span><span class="s">"Unauthorized tool access"</span><span class="p">);</span>
        <span class="p">}</span>
        
        <span class="k">let</span> <span class="n">tools</span> <span class="o">=</span> <span class="k">self</span><span class="py">.tools</span><span class="nf">.read</span><span class="p">()</span><span class="nf">.unwrap</span><span class="p">();</span>
        <span class="n">tools</span><span class="nf">.get</span><span class="p">(</span><span class="o">&amp;</span><span class="n">tool_id</span><span class="p">)</span><span class="o">?</span><span class="nf">.execute</span><span class="p">(</span><span class="n">params</span><span class="p">)</span><span class="k">.await</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h3 id="112-agent-as-tool-pattern">11.2 Agent-as-Tool Pattern</h3>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">struct</span> <span class="n">AgentTool</span> <span class="p">{</span>
    <span class="n">agent</span><span class="p">:</span> <span class="nb">Arc</span><span class="o">&lt;</span><span class="k">dyn</span> <span class="n">Agent</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="n">interface</span><span class="p">:</span> <span class="n">ToolInterface</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">Tool</span> <span class="k">for</span> <span class="n">AgentTool</span> <span class="p">{</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">execute</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">params</span><span class="p">:</span> <span class="n">Value</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Value</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="c1">// Convert tool call to agent message</span>
        <span class="k">let</span> <span class="n">msg</span> <span class="o">=</span> <span class="nn">Message</span><span class="p">::</span><span class="nf">from_tool_params</span><span class="p">(</span><span class="n">params</span><span class="p">);</span>
        <span class="k">self</span><span class="py">.agent</span><span class="nf">.process</span><span class="p">(</span><span class="n">msg</span><span class="p">)</span><span class="k">.await</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="c1">// Allows supervisors to treat sub-agents as tools</span>
<span class="k">impl</span> <span class="n">Supervisor</span> <span class="p">{</span>
    <span class="k">fn</span> <span class="nf">register_agent_as_tool</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span> <span class="n">agent</span><span class="p">:</span> <span class="nb">Arc</span><span class="o">&lt;</span><span class="k">dyn</span> <span class="n">Agent</span><span class="o">&gt;</span><span class="p">)</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">tool</span> <span class="o">=</span> <span class="nn">AgentTool</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">agent</span><span class="p">);</span>
        <span class="k">self</span><span class="py">.tool_bus</span><span class="nf">.register</span><span class="p">(</span><span class="n">tool</span><span class="p">);</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h2 id="12-extension-and-middleware-patterns">12. Extension and Middleware Patterns</h2>

<h3 id="121-middleware-pattern">12.1 Middleware Pattern</h3>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">trait</span> <span class="n">AgentMiddleware</span><span class="p">:</span> <span class="nb">Send</span> <span class="o">+</span> <span class="nb">Sync</span> <span class="p">{</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">before_process</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">msg</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Message</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span><span class="p">;</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">after_process</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">msg</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Message</span><span class="p">,</span> <span class="n">result</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Value</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span><span class="p">;</span>
<span class="p">}</span>

<span class="k">struct</span> <span class="n">Agent</span> <span class="p">{</span>
    <span class="n">middleware</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="nb">Box</span><span class="o">&lt;</span><span class="k">dyn</span> <span class="n">AgentMiddleware</span><span class="o">&gt;&gt;</span><span class="p">,</span>
    
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">process</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">msg</span><span class="p">:</span> <span class="n">Message</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Value</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="c1">// Before hooks</span>
        <span class="k">for</span> <span class="n">mw</span> <span class="k">in</span> <span class="o">&amp;</span><span class="k">self</span><span class="py">.middleware</span> <span class="p">{</span>
            <span class="n">mw</span><span class="nf">.before_process</span><span class="p">(</span><span class="o">&amp;</span><span class="n">msg</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>
        <span class="p">}</span>
        
        <span class="k">let</span> <span class="n">result</span> <span class="o">=</span> <span class="k">self</span><span class="nf">.core_process</span><span class="p">(</span><span class="n">msg</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>
        
        <span class="c1">// After hooks</span>
        <span class="k">for</span> <span class="n">mw</span> <span class="k">in</span> <span class="o">&amp;</span><span class="k">self</span><span class="py">.middleware</span> <span class="p">{</span>
            <span class="n">mw</span><span class="nf">.after_process</span><span class="p">(</span><span class="o">&amp;</span><span class="n">msg</span><span class="p">,</span> <span class="o">&amp;</span><span class="n">result</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>
        <span class="p">}</span>
        
        <span class="nf">Ok</span><span class="p">(</span><span class="n">result</span><span class="p">)</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h3 id="122-event-emitter-pattern">12.2 Event Emitter Pattern</h3>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">enum</span> <span class="n">SystemEvent</span> <span class="p">{</span>
    <span class="nf">AgentSpawned</span><span class="p">(</span><span class="n">AgentId</span><span class="p">),</span>
    <span class="nf">TaskCompleted</span><span class="p">(</span><span class="n">TaskId</span><span class="p">,</span> <span class="n">Value</span><span class="p">),</span>
    <span class="nf">ToolCalled</span><span class="p">(</span><span class="n">AgentId</span><span class="p">,</span> <span class="n">ToolId</span><span class="p">),</span>
    <span class="nf">Error</span><span class="p">(</span><span class="n">AgentId</span><span class="p">,</span> <span class="nb">String</span><span class="p">),</span>
<span class="p">}</span>

<span class="k">struct</span> <span class="n">EventBus</span> <span class="p">{</span>
    <span class="n">subscribers</span><span class="p">:</span> <span class="n">HashMap</span><span class="o">&lt;</span><span class="n">TypeId</span><span class="p">,</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="nb">Box</span><span class="o">&lt;</span><span class="k">dyn</span> <span class="n">EventHandler</span><span class="o">&gt;&gt;&gt;</span><span class="p">,</span>
    
    <span class="k">fn</span> <span class="nf">emit</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">event</span><span class="p">:</span> <span class="n">SystemEvent</span><span class="p">)</span> <span class="p">{</span>
        <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">handlers</span><span class="p">)</span> <span class="o">=</span> <span class="k">self</span><span class="py">.subscribers</span><span class="nf">.get</span><span class="p">(</span><span class="o">&amp;</span><span class="n">event</span><span class="nf">.type_id</span><span class="p">())</span> <span class="p">{</span>
            <span class="k">for</span> <span class="n">handler</span> <span class="k">in</span> <span class="n">handlers</span> <span class="p">{</span>
                <span class="n">handler</span><span class="nf">.handle</span><span class="p">(</span><span class="n">event</span><span class="nf">.clone</span><span class="p">());</span>
            <span class="p">}</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h2 id="13-claude-cli-hook-system-integration">13. Claude-CLI Hook System Integration</h2>

<h3 id="131-hook-shim-pattern">13.1 Hook Shim Pattern</h3>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// Hook system integration with NATS bus</span>
<span class="k">struct</span> <span class="n">HookShim</span> <span class="p">{</span>
    <span class="n">nats_client</span><span class="p">:</span> <span class="nn">async_nats</span><span class="p">::</span><span class="n">Client</span><span class="p">,</span>
    <span class="n">hook_dir</span><span class="p">:</span> <span class="n">PathBuf</span><span class="p">,</span>
    <span class="n">agent_id</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">HookShim</span> <span class="p">{</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">handle_hook</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">hook_type</span><span class="p">:</span> <span class="n">HookType</span><span class="p">,</span> <span class="n">payload</span><span class="p">:</span> <span class="n">HookPayload</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">HookResponse</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="c1">// Execute hook script and capture output</span>
        <span class="k">let</span> <span class="n">hook_path</span> <span class="o">=</span> <span class="k">self</span><span class="py">.hook_dir</span><span class="nf">.join</span><span class="p">(</span><span class="n">hook_type</span><span class="nf">.as_str</span><span class="p">());</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">cmd</span> <span class="o">=</span> <span class="nn">Command</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="o">&amp;</span><span class="n">hook_path</span><span class="p">)</span>
            <span class="nf">.stdin</span><span class="p">(</span><span class="nn">Stdio</span><span class="p">::</span><span class="nf">piped</span><span class="p">())</span>
            <span class="nf">.stdout</span><span class="p">(</span><span class="nn">Stdio</span><span class="p">::</span><span class="nf">piped</span><span class="p">())</span>
            <span class="nf">.stderr</span><span class="p">(</span><span class="nn">Stdio</span><span class="p">::</span><span class="nf">piped</span><span class="p">())</span>
            <span class="nf">.spawn</span><span class="p">()</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Send JSON payload to hook via stdin</span>
        <span class="k">let</span> <span class="n">stdin</span> <span class="o">=</span> <span class="n">cmd</span><span class="py">.stdin</span><span class="nf">.as_mut</span><span class="p">()</span><span class="nf">.unwrap</span><span class="p">();</span>
        <span class="n">stdin</span><span class="nf">.write_all</span><span class="p">(</span><span class="o">&amp;</span><span class="nn">serde_json</span><span class="p">::</span><span class="nf">to_vec</span><span class="p">(</span><span class="o">&amp;</span><span class="n">payload</span><span class="p">)</span><span class="o">?</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Capture hook response</span>
        <span class="k">let</span> <span class="n">output</span> <span class="o">=</span> <span class="n">cmd</span><span class="nf">.wait_with_output</span><span class="p">()</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>
        <span class="k">let</span> <span class="n">response</span><span class="p">:</span> <span class="n">HookResponse</span> <span class="o">=</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nf">from_slice</span><span class="p">(</span><span class="o">&amp;</span><span class="n">output</span><span class="py">.stdout</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Publish to NATS for orchestrator processing</span>
        <span class="k">let</span> <span class="n">subject</span> <span class="o">=</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"agent.{}.hook_response"</span><span class="p">,</span> <span class="k">self</span><span class="py">.agent_id</span><span class="p">);</span>
        <span class="k">self</span><span class="py">.nats_client</span><span class="nf">.publish</span><span class="p">(</span><span class="n">subject</span><span class="p">,</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nf">to_vec</span><span class="p">(</span><span class="o">&amp;</span><span class="n">response</span><span class="p">)</span><span class="o">?</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>

        <span class="nf">Ok</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="k">async</span> <span class="k">fn</span> <span class="nf">subscribe_to_hooks</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">subject</span> <span class="o">=</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"agent.{}.pre"</span><span class="p">,</span> <span class="k">self</span><span class="py">.agent_id</span><span class="p">);</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">subscriber</span> <span class="o">=</span> <span class="k">self</span><span class="py">.nats_client</span><span class="nf">.subscribe</span><span class="p">(</span><span class="n">subject</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>

        <span class="k">while</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">msg</span><span class="p">)</span> <span class="o">=</span> <span class="n">subscriber</span><span class="nf">.next</span><span class="p">()</span><span class="k">.await</span> <span class="p">{</span>
            <span class="k">let</span> <span class="n">payload</span><span class="p">:</span> <span class="n">HookPayload</span> <span class="o">=</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nf">from_slice</span><span class="p">(</span><span class="o">&amp;</span><span class="n">msg</span><span class="py">.payload</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

            <span class="k">match</span> <span class="n">payload</span><span class="py">.hook</span><span class="nf">.as_str</span><span class="p">()</span> <span class="p">{</span>
                <span class="s">"pre_task"</span> <span class="k">=&gt;</span> <span class="p">{</span>
                    <span class="k">self</span><span class="nf">.handle_hook</span><span class="p">(</span><span class="nn">HookType</span><span class="p">::</span><span class="n">PreTask</span><span class="p">,</span> <span class="n">payload</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>
                <span class="p">}</span>
                <span class="s">"post_task"</span> <span class="k">=&gt;</span> <span class="p">{</span>
                    <span class="k">self</span><span class="nf">.handle_hook</span><span class="p">(</span><span class="nn">HookType</span><span class="p">::</span><span class="n">PostTask</span><span class="p">,</span> <span class="n">payload</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>
                <span class="p">}</span>
                <span class="s">"on_error"</span> <span class="k">=&gt;</span> <span class="p">{</span>
                    <span class="k">self</span><span class="nf">.handle_hook</span><span class="p">(</span><span class="nn">HookType</span><span class="p">::</span><span class="n">OnError</span><span class="p">,</span> <span class="n">payload</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>
                <span class="p">}</span>
                <span class="n">_</span> <span class="k">=&gt;</span> <span class="p">{</span>
                    <span class="nd">eprintln!</span><span class="p">(</span><span class="s">"Unknown hook type: {}"</span><span class="p">,</span> <span class="n">payload</span><span class="py">.hook</span><span class="p">);</span>
                <span class="p">}</span>
            <span class="p">}</span>
        <span class="p">}</span>

        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="k">enum</span> <span class="n">HookType</span> <span class="p">{</span>
    <span class="n">Startup</span><span class="p">,</span>
    <span class="n">PreTask</span><span class="p">,</span>
    <span class="n">PostTask</span><span class="p">,</span>
    <span class="n">OnError</span><span class="p">,</span>
    <span class="n">OnFileChange</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">HookType</span> <span class="p">{</span>
    <span class="k">fn</span> <span class="nf">as_str</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="o">&amp;</span><span class="k">'static</span> <span class="nb">str</span> <span class="p">{</span>
        <span class="k">match</span> <span class="k">self</span> <span class="p">{</span>
            <span class="nn">HookType</span><span class="p">::</span><span class="n">Startup</span> <span class="k">=&gt;</span> <span class="s">"startup"</span><span class="p">,</span>
            <span class="nn">HookType</span><span class="p">::</span><span class="n">PreTask</span> <span class="k">=&gt;</span> <span class="s">"pre_task"</span><span class="p">,</span>
            <span class="nn">HookType</span><span class="p">::</span><span class="n">PostTask</span> <span class="k">=&gt;</span> <span class="s">"post_task"</span><span class="p">,</span>
            <span class="nn">HookType</span><span class="p">::</span><span class="n">OnError</span> <span class="k">=&gt;</span> <span class="s">"on_error"</span><span class="p">,</span>
            <span class="nn">HookType</span><span class="p">::</span><span class="n">OnFileChange</span> <span class="k">=&gt;</span> <span class="s">"on_file_change"</span><span class="p">,</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="nd">#[derive(Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">struct</span> <span class="n">HookPayload</span> <span class="p">{</span>
    <span class="n">hook</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="n">cwd</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="n">project_config</span><span class="p">:</span> <span class="n">Value</span><span class="p">,</span>
    <span class="n">task</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">TaskInfo</span><span class="o">&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="nd">#[derive(Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">struct</span> <span class="n">HookResponse</span> <span class="p">{</span>
    <span class="n">task</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">TaskInfo</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="n">env</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">HashMap</span><span class="o">&lt;</span><span class="nb">String</span><span class="p">,</span> <span class="nb">String</span><span class="o">&gt;&gt;</span><span class="p">,</span>
<span class="p">}</span>
</code></pre></div></div>

<h3 id="132-async-hook-listener-pattern">13.2 Async Hook Listener Pattern</h3>

<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// Integration with existing agent supervision</span>
<span class="k">impl</span> <span class="n">Supervisor</span> <span class="p">{</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">setup_hook_integration</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="c1">// Subscribe to hook events from Claude-CLI</span>
        <span class="k">let</span> <span class="n">startup_sub</span> <span class="o">=</span> <span class="k">self</span><span class="py">.nats_client</span><span class="nf">.subscribe</span><span class="p">(</span><span class="s">"control.startup"</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>
        <span class="k">let</span> <span class="n">file_change_sub</span> <span class="o">=</span> <span class="k">self</span><span class="py">.nats_client</span><span class="nf">.subscribe</span><span class="p">(</span><span class="s">"ctx.*.file_change"</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Spawn hook processing tasks</span>
        <span class="nn">tokio</span><span class="p">::</span><span class="nf">spawn</span><span class="p">(</span><span class="k">self</span><span class="nf">.process_startup_hooks</span><span class="p">(</span><span class="n">startup_sub</span><span class="p">));</span>
        <span class="nn">tokio</span><span class="p">::</span><span class="nf">spawn</span><span class="p">(</span><span class="k">self</span><span class="nf">.process_file_change_hooks</span><span class="p">(</span><span class="n">file_change_sub</span><span class="p">));</span>

        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>

    <span class="k">async</span> <span class="k">fn</span> <span class="nf">process_startup_hooks</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="k">mut</span> <span class="n">subscriber</span><span class="p">:</span> <span class="n">Subscriber</span><span class="p">)</span> <span class="p">{</span>
        <span class="k">while</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">msg</span><span class="p">)</span> <span class="o">=</span> <span class="n">subscriber</span><span class="nf">.next</span><span class="p">()</span><span class="k">.await</span> <span class="p">{</span>
            <span class="c1">// Record CLI version and capabilities</span>
            <span class="k">let</span> <span class="n">startup_info</span><span class="p">:</span> <span class="n">StartupInfo</span> <span class="o">=</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nf">from_slice</span><span class="p">(</span><span class="o">&amp;</span><span class="n">msg</span><span class="py">.payload</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">();</span>
            <span class="k">self</span><span class="nf">.record_cli_capabilities</span><span class="p">(</span><span class="n">startup_info</span><span class="p">)</span><span class="k">.await</span><span class="p">;</span>
        <span class="p">}</span>
    <span class="p">}</span>

    <span class="k">async</span> <span class="k">fn</span> <span class="nf">process_file_change_hooks</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="k">mut</span> <span class="n">subscriber</span><span class="p">:</span> <span class="n">Subscriber</span><span class="p">)</span> <span class="p">{</span>
        <span class="k">while</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">msg</span><span class="p">)</span> <span class="o">=</span> <span class="n">subscriber</span><span class="nf">.next</span><span class="p">()</span><span class="k">.await</span> <span class="p">{</span>
            <span class="c1">// Trigger code quality agents</span>
            <span class="k">let</span> <span class="n">file_change</span><span class="p">:</span> <span class="n">FileChangeEvent</span> <span class="o">=</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nf">from_slice</span><span class="p">(</span><span class="o">&amp;</span><span class="n">msg</span><span class="py">.payload</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">();</span>
            <span class="k">self</span><span class="nf">.trigger_code_quality_agents</span><span class="p">(</span><span class="n">file_change</span><span class="p">)</span><span class="k">.await</span><span class="p">;</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h2 id="14-database-schemas">14. Database Schemas</h2>

<h3 id="141-core-tables">14.1 Core Tables</h3>

<h4 id="1411-agents-table">14.1.1 Agents Table</h4>

<div class="language-sql highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">CREATE</span> <span class="k">TABLE</span> <span class="n">agents</span> <span class="p">(</span>
    <span class="n">agent_id</span> <span class="n">UUID</span> <span class="k">PRIMARY</span> <span class="k">KEY</span> <span class="k">DEFAULT</span> <span class="n">gen_random_uuid</span><span class="p">(),</span>
    <span class="n">agent_type</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">CHECK</span> <span class="p">(</span><span class="n">agent_type</span> <span class="k">IN</span> <span class="p">(</span>
        <span class="s1">'SUPERVISOR'</span><span class="p">,</span> <span class="s1">'WORKER'</span><span class="p">,</span> <span class="s1">'COORDINATOR'</span><span class="p">,</span> <span class="s1">'MONITOR'</span><span class="p">,</span> 
        <span class="s1">'PLANNER'</span><span class="p">,</span> <span class="s1">'EXECUTOR'</span><span class="p">,</span> <span class="s1">'CRITIC'</span><span class="p">,</span> <span class="s1">'ROUTER'</span><span class="p">,</span> <span class="s1">'MEMORY'</span>
    <span class="p">)),</span>
    <span class="n">current_state</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">CHECK</span> <span class="p">(</span><span class="n">current_state</span> <span class="k">IN</span> <span class="p">(</span>
        <span class="s1">'INITIALIZING'</span><span class="p">,</span> <span class="s1">'RUNNING'</span><span class="p">,</span> <span class="s1">'PAUSED'</span><span class="p">,</span> <span class="s1">'STOPPING'</span><span class="p">,</span> 
        <span class="s1">'TERMINATED'</span><span class="p">,</span> <span class="s1">'ERROR'</span><span class="p">,</span> <span class="s1">'RESTARTING'</span>
    <span class="p">)),</span>
    <span class="n">previous_state</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">20</span><span class="p">),</span>
    <span class="n">created_at</span> <span class="n">TIMESTAMPTZ</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="n">NOW</span><span class="p">(),</span>
    <span class="n">updated_at</span> <span class="n">TIMESTAMPTZ</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="n">NOW</span><span class="p">(),</span>
    <span class="n">last_heartbeat</span> <span class="n">TIMESTAMPTZ</span><span class="p">,</span>
    <span class="n">supervisor_id</span> <span class="n">UUID</span> <span class="k">REFERENCES</span> <span class="n">agents</span><span class="p">(</span><span class="n">agent_id</span><span class="p">),</span>
    <span class="n">capabilities</span> <span class="n">JSONB</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="s1">'[]'</span><span class="p">,</span>
    <span class="n">configuration</span> <span class="n">JSONB</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="s1">'{}'</span><span class="p">,</span>
    <span class="n">state_data</span> <span class="n">JSONB</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="s1">'{}'</span><span class="p">,</span>
    <span class="n">metrics</span> <span class="n">JSONB</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="s1">'{}'</span><span class="p">,</span>
    <span class="n">restart_count</span> <span class="nb">INTEGER</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="mi">0</span><span class="p">,</span>
    <span class="n">error_count</span> <span class="nb">INTEGER</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="mi">0</span><span class="p">,</span>
    <span class="k">version</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="s1">'1.0.0'</span><span class="p">,</span>
    <span class="n">tags</span> <span class="n">JSONB</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="s1">'[]'</span>
<span class="p">);</span>

<span class="c1">-- Indexes for performance</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_agents_type</span> <span class="k">ON</span> <span class="n">agents</span><span class="p">(</span><span class="n">agent_type</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_agents_state</span> <span class="k">ON</span> <span class="n">agents</span><span class="p">(</span><span class="n">current_state</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_agents_supervisor</span> <span class="k">ON</span> <span class="n">agents</span><span class="p">(</span><span class="n">supervisor_id</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_agents_heartbeat</span> <span class="k">ON</span> <span class="n">agents</span><span class="p">(</span><span class="n">last_heartbeat</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_agents_capabilities</span> <span class="k">ON</span> <span class="n">agents</span> <span class="k">USING</span> <span class="n">GIN</span><span class="p">(</span><span class="n">capabilities</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_agents_tags</span> <span class="k">ON</span> <span class="n">agents</span> <span class="k">USING</span> <span class="n">GIN</span><span class="p">(</span><span class="n">tags</span><span class="p">);</span>

<span class="c1">-- Trigger for updated_at</span>
<span class="k">CREATE</span> <span class="k">OR</span> <span class="k">REPLACE</span> <span class="k">FUNCTION</span> <span class="n">update_updated_at_column</span><span class="p">()</span>
<span class="k">RETURNS</span> <span class="k">TRIGGER</span> <span class="k">AS</span> <span class="err">$$</span>
<span class="k">BEGIN</span>
    <span class="k">NEW</span><span class="p">.</span><span class="n">updated_at</span> <span class="o">=</span> <span class="n">NOW</span><span class="p">();</span>
    <span class="k">RETURN</span> <span class="k">NEW</span><span class="p">;</span>
<span class="k">END</span><span class="p">;</span>
<span class="err">$$</span> <span class="k">language</span> <span class="s1">'plpgsql'</span><span class="p">;</span>

<span class="k">CREATE</span> <span class="k">TRIGGER</span> <span class="n">update_agents_updated_at</span> <span class="k">BEFORE</span> <span class="k">UPDATE</span> <span class="k">ON</span> <span class="n">agents</span>
    <span class="k">FOR</span> <span class="k">EACH</span> <span class="k">ROW</span> <span class="k">EXECUTE</span> <span class="k">FUNCTION</span> <span class="n">update_updated_at_column</span><span class="p">();</span>
</code></pre></div></div>

<h4 id="1412-tasks-table">14.1.2 Tasks Table</h4>

<div class="language-sql highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">CREATE</span> <span class="k">TABLE</span> <span class="n">tasks</span> <span class="p">(</span>
    <span class="n">task_id</span> <span class="n">UUID</span> <span class="k">PRIMARY</span> <span class="k">KEY</span> <span class="k">DEFAULT</span> <span class="n">gen_random_uuid</span><span class="p">(),</span>
    <span class="n">agent_id</span> <span class="n">UUID</span> <span class="k">REFERENCES</span> <span class="n">agents</span><span class="p">(</span><span class="n">agent_id</span><span class="p">)</span> <span class="k">ON</span> <span class="k">DELETE</span> <span class="k">SET</span> <span class="k">NULL</span><span class="p">,</span>
    <span class="n">parent_task_id</span> <span class="n">UUID</span> <span class="k">REFERENCES</span> <span class="n">tasks</span><span class="p">(</span><span class="n">task_id</span><span class="p">),</span>
    <span class="n">task_type</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">CHECK</span> <span class="p">(</span><span class="n">task_type</span> <span class="k">IN</span> <span class="p">(</span>
        <span class="s1">'RESEARCH'</span><span class="p">,</span> <span class="s1">'CODE'</span><span class="p">,</span> <span class="s1">'ANALYZE'</span><span class="p">,</span> <span class="s1">'REVIEW'</span><span class="p">,</span> <span class="s1">'DEPLOY'</span><span class="p">,</span> <span class="s1">'MONITOR'</span><span class="p">,</span> <span class="s1">'CUSTOM'</span>
    <span class="p">)),</span>
    <span class="n">status</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">CHECK</span> <span class="p">(</span><span class="n">status</span> <span class="k">IN</span> <span class="p">(</span>
        <span class="s1">'PENDING'</span><span class="p">,</span> <span class="s1">'ASSIGNED'</span><span class="p">,</span> <span class="s1">'IN_PROGRESS'</span><span class="p">,</span> <span class="s1">'COMPLETED'</span><span class="p">,</span> <span class="s1">'FAILED'</span><span class="p">,</span> <span class="s1">'CANCELLED'</span>
    <span class="p">)),</span>
    <span class="n">priority</span> <span class="nb">INTEGER</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="mi">5</span> <span class="k">CHECK</span> <span class="p">(</span><span class="n">priority</span> <span class="k">BETWEEN</span> <span class="mi">0</span> <span class="k">AND</span> <span class="mi">9</span><span class="p">),</span>
    <span class="n">title</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">200</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span><span class="p">,</span>
    <span class="n">description</span> <span class="nb">TEXT</span><span class="p">,</span>
    <span class="n">requirements</span> <span class="n">JSONB</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="s1">'{}'</span><span class="p">,</span>
    <span class="n">input_data</span> <span class="n">JSONB</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="s1">'{}'</span><span class="p">,</span>
    <span class="n">output_data</span> <span class="n">JSONB</span><span class="p">,</span>
    <span class="n">progress_percentage</span> <span class="nb">INTEGER</span> <span class="k">DEFAULT</span> <span class="mi">0</span> <span class="k">CHECK</span> <span class="p">(</span><span class="n">progress_percentage</span> <span class="k">BETWEEN</span> <span class="mi">0</span> <span class="k">AND</span> <span class="mi">100</span><span class="p">),</span>
    <span class="n">error_info</span> <span class="n">JSONB</span><span class="p">,</span>
    <span class="n">created_at</span> <span class="n">TIMESTAMPTZ</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="n">NOW</span><span class="p">(),</span>
    <span class="n">assigned_at</span> <span class="n">TIMESTAMPTZ</span><span class="p">,</span>
    <span class="n">started_at</span> <span class="n">TIMESTAMPTZ</span><span class="p">,</span>
    <span class="n">completed_at</span> <span class="n">TIMESTAMPTZ</span><span class="p">,</span>
    <span class="n">deadline</span> <span class="n">TIMESTAMPTZ</span><span class="p">,</span>
    <span class="n">estimated_duration_seconds</span> <span class="nb">INTEGER</span><span class="p">,</span>
    <span class="n">actual_duration_seconds</span> <span class="nb">INTEGER</span><span class="p">,</span>
    <span class="n">retry_count</span> <span class="nb">INTEGER</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="mi">0</span><span class="p">,</span>
    <span class="n">max_retries</span> <span class="nb">INTEGER</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="mi">3</span><span class="p">,</span>
    <span class="n">tags</span> <span class="n">JSONB</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="s1">'[]'</span>
<span class="p">);</span>

<span class="c1">-- Indexes</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_tasks_agent</span> <span class="k">ON</span> <span class="n">tasks</span><span class="p">(</span><span class="n">agent_id</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_tasks_status</span> <span class="k">ON</span> <span class="n">tasks</span><span class="p">(</span><span class="n">status</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_tasks_type</span> <span class="k">ON</span> <span class="n">tasks</span><span class="p">(</span><span class="n">task_type</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_tasks_priority</span> <span class="k">ON</span> <span class="n">tasks</span><span class="p">(</span><span class="n">priority</span> <span class="k">DESC</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_tasks_created</span> <span class="k">ON</span> <span class="n">tasks</span><span class="p">(</span><span class="n">created_at</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_tasks_deadline</span> <span class="k">ON</span> <span class="n">tasks</span><span class="p">(</span><span class="n">deadline</span><span class="p">)</span> <span class="k">WHERE</span> <span class="n">deadline</span> <span class="k">IS</span> <span class="k">NOT</span> <span class="k">NULL</span><span class="p">;</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_tasks_parent</span> <span class="k">ON</span> <span class="n">tasks</span><span class="p">(</span><span class="n">parent_task_id</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_tasks_tags</span> <span class="k">ON</span> <span class="n">tasks</span> <span class="k">USING</span> <span class="n">GIN</span><span class="p">(</span><span class="n">tags</span><span class="p">);</span>

<span class="k">CREATE</span> <span class="k">TRIGGER</span> <span class="n">update_tasks_updated_at</span> <span class="k">BEFORE</span> <span class="k">UPDATE</span> <span class="k">ON</span> <span class="n">tasks</span>
    <span class="k">FOR</span> <span class="k">EACH</span> <span class="k">ROW</span> <span class="k">EXECUTE</span> <span class="k">FUNCTION</span> <span class="n">update_updated_at_column</span><span class="p">();</span>
</code></pre></div></div>

<h4 id="1413-messages-table">14.1.3 Messages Table</h4>

<div class="language-sql highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">CREATE</span> <span class="k">TABLE</span> <span class="n">messages</span> <span class="p">(</span>
    <span class="n">message_id</span> <span class="n">UUID</span> <span class="k">PRIMARY</span> <span class="k">KEY</span> <span class="k">DEFAULT</span> <span class="n">gen_random_uuid</span><span class="p">(),</span>
    <span class="n">sender_id</span> <span class="n">UUID</span> <span class="k">REFERENCES</span> <span class="n">agents</span><span class="p">(</span><span class="n">agent_id</span><span class="p">),</span>
    <span class="n">receiver_id</span> <span class="n">UUID</span> <span class="k">REFERENCES</span> <span class="n">agents</span><span class="p">(</span><span class="n">agent_id</span><span class="p">),</span>
    <span class="n">message_type</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span><span class="p">,</span>
    <span class="n">correlation_id</span> <span class="n">UUID</span><span class="p">,</span>
    <span class="n">routing_type</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">CHECK</span> <span class="p">(</span><span class="n">routing_type</span> <span class="k">IN</span> <span class="p">(</span>
        <span class="s1">'BROADCAST'</span><span class="p">,</span> <span class="s1">'TARGET'</span><span class="p">,</span> <span class="s1">'ROUND_ROBIN'</span>
    <span class="p">)),</span>
    <span class="n">priority</span> <span class="nb">INTEGER</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="mi">5</span> <span class="k">CHECK</span> <span class="p">(</span><span class="n">priority</span> <span class="k">BETWEEN</span> <span class="mi">0</span> <span class="k">AND</span> <span class="mi">9</span><span class="p">),</span>
    <span class="n">ttl_seconds</span> <span class="nb">INTEGER</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="mi">300</span><span class="p">,</span>
    <span class="n">payload</span> <span class="n">JSONB</span> <span class="k">NOT</span> <span class="k">NULL</span><span class="p">,</span>
    <span class="n">status</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="s1">'PENDING'</span> <span class="k">CHECK</span> <span class="p">(</span><span class="n">status</span> <span class="k">IN</span> <span class="p">(</span>
        <span class="s1">'PENDING'</span><span class="p">,</span> <span class="s1">'DELIVERED'</span><span class="p">,</span> <span class="s1">'PROCESSED'</span><span class="p">,</span> <span class="s1">'FAILED'</span><span class="p">,</span> <span class="s1">'EXPIRED'</span>
    <span class="p">)),</span>
    <span class="n">created_at</span> <span class="n">TIMESTAMPTZ</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="n">NOW</span><span class="p">(),</span>
    <span class="n">delivered_at</span> <span class="n">TIMESTAMPTZ</span><span class="p">,</span>
    <span class="n">processed_at</span> <span class="n">TIMESTAMPTZ</span><span class="p">,</span>
    <span class="n">expires_at</span> <span class="n">TIMESTAMPTZ</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="p">(</span><span class="n">NOW</span><span class="p">()</span> <span class="o">+</span> <span class="n">INTERVAL</span> <span class="s1">'300 seconds'</span><span class="p">),</span>
    <span class="n">retry_count</span> <span class="nb">INTEGER</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="mi">0</span><span class="p">,</span>
    <span class="n">error_message</span> <span class="nb">TEXT</span>
<span class="p">);</span>

<span class="c1">-- Indexes</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_messages_sender</span> <span class="k">ON</span> <span class="n">messages</span><span class="p">(</span><span class="n">sender_id</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_messages_receiver</span> <span class="k">ON</span> <span class="n">messages</span><span class="p">(</span><span class="n">receiver_id</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_messages_type</span> <span class="k">ON</span> <span class="n">messages</span><span class="p">(</span><span class="n">message_type</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_messages_correlation</span> <span class="k">ON</span> <span class="n">messages</span><span class="p">(</span><span class="n">correlation_id</span><span class="p">)</span> <span class="k">WHERE</span> <span class="n">correlation_id</span> <span class="k">IS</span> <span class="k">NOT</span> <span class="k">NULL</span><span class="p">;</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_messages_status</span> <span class="k">ON</span> <span class="n">messages</span><span class="p">(</span><span class="n">status</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_messages_created</span> <span class="k">ON</span> <span class="n">messages</span><span class="p">(</span><span class="n">created_at</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_messages_expires</span> <span class="k">ON</span> <span class="n">messages</span><span class="p">(</span><span class="n">expires_at</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_messages_priority</span> <span class="k">ON</span> <span class="n">messages</span><span class="p">(</span><span class="n">priority</span> <span class="k">DESC</span><span class="p">,</span> <span class="n">created_at</span><span class="p">);</span>

<span class="c1">-- Partitioning by creation date for large scale</span>
<span class="k">CREATE</span> <span class="k">TABLE</span> <span class="n">messages_y2024m01</span> <span class="k">PARTITION</span> <span class="k">OF</span> <span class="n">messages</span>
    <span class="k">FOR</span> <span class="k">VALUES</span> <span class="k">FROM</span> <span class="p">(</span><span class="s1">'2024-01-01'</span><span class="p">)</span> <span class="k">TO</span> <span class="p">(</span><span class="s1">'2024-02-01'</span><span class="p">);</span>
<span class="c1">-- Add more partitions as needed</span>
</code></pre></div></div>

<h4 id="1414-supervision-events-table">14.1.4 Supervision Events Table</h4>

<div class="language-sql highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">CREATE</span> <span class="k">TABLE</span> <span class="n">supervision_events</span> <span class="p">(</span>
    <span class="n">event_id</span> <span class="n">UUID</span> <span class="k">PRIMARY</span> <span class="k">KEY</span> <span class="k">DEFAULT</span> <span class="n">gen_random_uuid</span><span class="p">(),</span>
    <span class="n">supervisor_id</span> <span class="n">UUID</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">REFERENCES</span> <span class="n">agents</span><span class="p">(</span><span class="n">agent_id</span><span class="p">),</span>
    <span class="n">subject_agent_id</span> <span class="n">UUID</span> <span class="k">REFERENCES</span> <span class="n">agents</span><span class="p">(</span><span class="n">agent_id</span><span class="p">),</span>
    <span class="n">event_type</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">30</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">CHECK</span> <span class="p">(</span><span class="n">event_type</span> <span class="k">IN</span> <span class="p">(</span>
        <span class="s1">'AGENT_SPAWNED'</span><span class="p">,</span> <span class="s1">'AGENT_TERMINATED'</span><span class="p">,</span> <span class="s1">'AGENT_RESTARTED'</span><span class="p">,</span> 
        <span class="s1">'HEALTH_CHECK_FAILED'</span><span class="p">,</span> <span class="s1">'RESOURCE_LIMIT_EXCEEDED'</span><span class="p">,</span> <span class="s1">'POLICY_VIOLATION'</span>
    <span class="p">)),</span>
    <span class="n">event_data</span> <span class="n">JSONB</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="s1">'{}'</span><span class="p">,</span>
    <span class="n">severity</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">CHECK</span> <span class="p">(</span><span class="n">severity</span> <span class="k">IN</span> <span class="p">(</span><span class="s1">'LOW'</span><span class="p">,</span> <span class="s1">'MEDIUM'</span><span class="p">,</span> <span class="s1">'HIGH'</span><span class="p">,</span> <span class="s1">'CRITICAL'</span><span class="p">)),</span>
    <span class="n">created_at</span> <span class="n">TIMESTAMPTZ</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="n">NOW</span><span class="p">(),</span>
    <span class="n">resolved_at</span> <span class="n">TIMESTAMPTZ</span><span class="p">,</span>
    <span class="n">resolution_action</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">100</span><span class="p">),</span>
    <span class="n">correlation_id</span> <span class="n">UUID</span>
<span class="p">);</span>

<span class="c1">-- Indexes</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_supervision_supervisor</span> <span class="k">ON</span> <span class="n">supervision_events</span><span class="p">(</span><span class="n">supervisor_id</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_supervision_subject</span> <span class="k">ON</span> <span class="n">supervision_events</span><span class="p">(</span><span class="n">subject_agent_id</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_supervision_type</span> <span class="k">ON</span> <span class="n">supervision_events</span><span class="p">(</span><span class="n">event_type</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_supervision_severity</span> <span class="k">ON</span> <span class="n">supervision_events</span><span class="p">(</span><span class="n">severity</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_supervision_created</span> <span class="k">ON</span> <span class="n">supervision_events</span><span class="p">(</span><span class="n">created_at</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_supervision_unresolved</span> <span class="k">ON</span> <span class="n">supervision_events</span><span class="p">(</span><span class="n">resolved_at</span><span class="p">)</span> <span class="k">WHERE</span> <span class="n">resolved_at</span> <span class="k">IS</span> <span class="k">NULL</span><span class="p">;</span>
</code></pre></div></div>

<h4 id="1415-agent-metrics-table">14.1.5 Agent Metrics Table</h4>

<div class="language-sql highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">CREATE</span> <span class="k">TABLE</span> <span class="n">agent_metrics</span> <span class="p">(</span>
    <span class="n">metric_id</span> <span class="n">UUID</span> <span class="k">PRIMARY</span> <span class="k">KEY</span> <span class="k">DEFAULT</span> <span class="n">gen_random_uuid</span><span class="p">(),</span>
    <span class="n">agent_id</span> <span class="n">UUID</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">REFERENCES</span> <span class="n">agents</span><span class="p">(</span><span class="n">agent_id</span><span class="p">)</span> <span class="k">ON</span> <span class="k">DELETE</span> <span class="k">CASCADE</span><span class="p">,</span>
    <span class="n">metric_type</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span><span class="p">,</span>
    <span class="n">metric_value</span> <span class="nb">NUMERIC</span> <span class="k">NOT</span> <span class="k">NULL</span><span class="p">,</span>
    <span class="n">metric_unit</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">20</span><span class="p">),</span>
    <span class="n">tags</span> <span class="n">JSONB</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="s1">'{}'</span><span class="p">,</span>
    <span class="n">recorded_at</span> <span class="n">TIMESTAMPTZ</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="n">NOW</span><span class="p">()</span>
<span class="p">);</span>

<span class="c1">-- Indexes</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_metrics_agent</span> <span class="k">ON</span> <span class="n">agent_metrics</span><span class="p">(</span><span class="n">agent_id</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_metrics_type</span> <span class="k">ON</span> <span class="n">agent_metrics</span><span class="p">(</span><span class="n">metric_type</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_metrics_recorded</span> <span class="k">ON</span> <span class="n">agent_metrics</span><span class="p">(</span><span class="n">recorded_at</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_metrics_agent_type_time</span> <span class="k">ON</span> <span class="n">agent_metrics</span><span class="p">(</span><span class="n">agent_id</span><span class="p">,</span> <span class="n">metric_type</span><span class="p">,</span> <span class="n">recorded_at</span><span class="p">);</span>

<span class="c1">-- Hypertable for TimescaleDB (if using)</span>
<span class="c1">-- SELECT create_hypertable('agent_metrics', 'recorded_at');</span>
</code></pre></div></div>

<h3 id="142-audit-and-logging-tables">14.2 Audit and Logging Tables</h3>

<h4 id="1421-state-transitions-table">14.2.1 State Transitions Table</h4>

<div class="language-sql highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">CREATE</span> <span class="k">TABLE</span> <span class="n">state_transitions</span> <span class="p">(</span>
    <span class="n">transition_id</span> <span class="n">UUID</span> <span class="k">PRIMARY</span> <span class="k">KEY</span> <span class="k">DEFAULT</span> <span class="n">gen_random_uuid</span><span class="p">(),</span>
    <span class="n">agent_id</span> <span class="n">UUID</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">REFERENCES</span> <span class="n">agents</span><span class="p">(</span><span class="n">agent_id</span><span class="p">)</span> <span class="k">ON</span> <span class="k">DELETE</span> <span class="k">CASCADE</span><span class="p">,</span>
    <span class="n">from_state</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span><span class="p">,</span>
    <span class="n">to_state</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span> <span class="k">NOT</span> <span class="k">NULL</span><span class="p">,</span>
    <span class="n">trigger_event</span> <span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">100</span><span class="p">),</span>
    <span class="n">transition_data</span> <span class="n">JSONB</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="s1">'{}'</span><span class="p">,</span>
    <span class="n">duration_ms</span> <span class="nb">INTEGER</span><span class="p">,</span>
    <span class="n">success</span> <span class="nb">BOOLEAN</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="k">true</span><span class="p">,</span>
    <span class="n">error_message</span> <span class="nb">TEXT</span><span class="p">,</span>
    <span class="n">created_at</span> <span class="n">TIMESTAMPTZ</span> <span class="k">NOT</span> <span class="k">NULL</span> <span class="k">DEFAULT</span> <span class="n">NOW</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_transitions_agent</span> <span class="k">ON</span> <span class="n">state_transitions</span><span class="p">(</span><span class="n">agent_id</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_transitions_states</span> <span class="k">ON</span> <span class="n">state_transitions</span><span class="p">(</span><span class="n">from_state</span><span class="p">,</span> <span class="n">to_state</span><span class="p">);</span>
<span class="k">CREATE</span> <span class="k">INDEX</span> <span class="n">idx_transitions_created</span> <span class="k">ON</span> <span class="n">state_transitions</span><span class="p">(</span><span class="n">created_at</span><span class="p">);</span>
</code></pre></div></div>

<h3 id="143-views-for-common-queries">14.3 Views for Common Queries</h3>

<div class="language-sql highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">-- Active agents view</span>
<span class="k">CREATE</span> <span class="k">VIEW</span> <span class="n">active_agents</span> <span class="k">AS</span>
<span class="k">SELECT</span> 
    <span class="n">agent_id</span><span class="p">,</span>
    <span class="n">agent_type</span><span class="p">,</span>
    <span class="n">current_state</span><span class="p">,</span>
    <span class="n">capabilities</span><span class="p">,</span>
    <span class="n">last_heartbeat</span><span class="p">,</span>
    <span class="n">NOW</span><span class="p">()</span> <span class="o">-</span> <span class="n">last_heartbeat</span> <span class="k">as</span> <span class="n">last_seen_duration</span>
<span class="k">FROM</span> <span class="n">agents</span> 
<span class="k">WHERE</span> <span class="n">current_state</span> <span class="k">IN</span> <span class="p">(</span><span class="s1">'RUNNING'</span><span class="p">,</span> <span class="s1">'PAUSED'</span><span class="p">)</span>
    <span class="k">AND</span> <span class="n">last_heartbeat</span> <span class="o">&gt;</span> <span class="n">NOW</span><span class="p">()</span> <span class="o">-</span> <span class="n">INTERVAL</span> <span class="s1">'5 minutes'</span><span class="p">;</span>

<span class="c1">-- Task queue view</span>
<span class="k">CREATE</span> <span class="k">VIEW</span> <span class="n">task_queue</span> <span class="k">AS</span>
<span class="k">SELECT</span> 
    <span class="n">task_id</span><span class="p">,</span>
    <span class="n">task_type</span><span class="p">,</span>
    <span class="n">priority</span><span class="p">,</span>
    <span class="n">title</span><span class="p">,</span>
    <span class="n">requirements</span><span class="p">,</span>
    <span class="n">created_at</span><span class="p">,</span>
    <span class="n">deadline</span>
<span class="k">FROM</span> <span class="n">tasks</span> 
<span class="k">WHERE</span> <span class="n">status</span> <span class="o">=</span> <span class="s1">'PENDING'</span>
<span class="k">ORDER</span> <span class="k">BY</span> <span class="n">priority</span> <span class="k">DESC</span><span class="p">,</span> <span class="n">created_at</span> <span class="k">ASC</span><span class="p">;</span>

<span class="c1">-- Agent health summary</span>
<span class="k">CREATE</span> <span class="k">VIEW</span> <span class="n">agent_health_summary</span> <span class="k">AS</span>
<span class="k">SELECT</span> 
    <span class="n">a</span><span class="p">.</span><span class="n">agent_id</span><span class="p">,</span>
    <span class="n">a</span><span class="p">.</span><span class="n">agent_type</span><span class="p">,</span>
    <span class="n">a</span><span class="p">.</span><span class="n">current_state</span><span class="p">,</span>
    <span class="n">a</span><span class="p">.</span><span class="n">restart_count</span><span class="p">,</span>
    <span class="n">a</span><span class="p">.</span><span class="n">error_count</span><span class="p">,</span>
    <span class="k">COUNT</span><span class="p">(</span><span class="n">t</span><span class="p">.</span><span class="n">task_id</span><span class="p">)</span> <span class="k">as</span> <span class="n">active_tasks</span><span class="p">,</span>
    <span class="k">AVG</span><span class="p">(</span><span class="k">CASE</span> <span class="k">WHEN</span> <span class="n">am</span><span class="p">.</span><span class="n">metric_type</span> <span class="o">=</span> <span class="s1">'cpu_usage'</span> <span class="k">THEN</span> <span class="n">am</span><span class="p">.</span><span class="n">metric_value</span> <span class="k">END</span><span class="p">)</span> <span class="k">as</span> <span class="n">avg_cpu_usage</span><span class="p">,</span>
    <span class="k">AVG</span><span class="p">(</span><span class="k">CASE</span> <span class="k">WHEN</span> <span class="n">am</span><span class="p">.</span><span class="n">metric_type</span> <span class="o">=</span> <span class="s1">'memory_usage'</span> <span class="k">THEN</span> <span class="n">am</span><span class="p">.</span><span class="n">metric_value</span> <span class="k">END</span><span class="p">)</span> <span class="k">as</span> <span class="n">avg_memory_usage</span>
<span class="k">FROM</span> <span class="n">agents</span> <span class="n">a</span>
<span class="k">LEFT</span> <span class="k">JOIN</span> <span class="n">tasks</span> <span class="n">t</span> <span class="k">ON</span> <span class="n">a</span><span class="p">.</span><span class="n">agent_id</span> <span class="o">=</span> <span class="n">t</span><span class="p">.</span><span class="n">agent_id</span> <span class="k">AND</span> <span class="n">t</span><span class="p">.</span><span class="n">status</span> <span class="o">=</span> <span class="s1">'IN_PROGRESS'</span>
<span class="k">LEFT</span> <span class="k">JOIN</span> <span class="n">agent_metrics</span> <span class="n">am</span> <span class="k">ON</span> <span class="n">a</span><span class="p">.</span><span class="n">agent_id</span> <span class="o">=</span> <span class="n">am</span><span class="p">.</span><span class="n">agent_id</span> 
    <span class="k">AND</span> <span class="n">am</span><span class="p">.</span><span class="n">recorded_at</span> <span class="o">&gt;</span> <span class="n">NOW</span><span class="p">()</span> <span class="o">-</span> <span class="n">INTERVAL</span> <span class="s1">'5 minutes'</span>
<span class="k">GROUP</span> <span class="k">BY</span> <span class="n">a</span><span class="p">.</span><span class="n">agent_id</span><span class="p">,</span> <span class="n">a</span><span class="p">.</span><span class="n">agent_type</span><span class="p">,</span> <span class="n">a</span><span class="p">.</span><span class="n">current_state</span><span class="p">,</span> <span class="n">a</span><span class="p">.</span><span class="n">restart_count</span><span class="p">,</span> <span class="n">a</span><span class="p">.</span><span class="n">error_count</span><span class="p">;</span>
</code></pre></div></div>

<h2 id="15-message-serialization-and-communication">15. Message Serialization and Communication</h2>

<h3 id="151-serialization-formats">15.1 Serialization Formats</h3>

<h4 id="1511-primary-format-json">15.1.1 Primary Format: JSON</h4>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://json-schema.org/draft/2020-12/schema"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"$id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/message-envelope"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Message Serialization Envelope"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"version"</span><span class="p">,</span><span class="w"> </span><span class="s2">"encoding"</span><span class="p">,</span><span class="w"> </span><span class="s2">"compression"</span><span class="p">,</span><span class="w"> </span><span class="s2">"message"</span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"version"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^</span><span class="se">\\</span><span class="s2">d+</span><span class="se">\\</span><span class="s2">.</span><span class="se">\\</span><span class="s2">d+</span><span class="se">\\</span><span class="s2">.</span><span class="se">\\</span><span class="s2">d+$"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Serialization format version (semantic versioning)"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="s2">"1.0.0"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"encoding"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"json"</span><span class="p">,</span><span class="w"> </span><span class="s2">"msgpack"</span><span class="p">,</span><span class="w"> </span><span class="s2">"protobuf"</span><span class="p">],</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Message encoding format"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="s2">"json"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"compression"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"none"</span><span class="p">,</span><span class="w"> </span><span class="s2">"gzip"</span><span class="p">,</span><span class="w"> </span><span class="s2">"lz4"</span><span class="p">,</span><span class="w"> </span><span class="s2">"zstd"</span><span class="p">],</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Compression algorithm applied"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="s2">"none"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"checksum"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^[a-f0-9]{64}$"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"SHA-256 checksum of message content"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"The actual message content (varies by encoding)"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"metadata"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"size_bytes"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="p">},</span><span class="w">
        </span><span class="nl">"compression_ratio"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="p">},</span><span class="w">
        </span><span class="nl">"serialization_time_ms"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h4 id="1512-performance-format-messagepack-schema">15.1.2 Performance Format: MessagePack Schema</h4>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://json-schema.org/draft/2020-12/schema"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"$id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/msgpack-config"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"MessagePack Serialization Configuration"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"use_bin_type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"boolean"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Use bin format for binary data"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"raw"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"boolean"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Use raw format (deprecated but faster)"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"strict_map_key"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"boolean"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Enforce string keys in maps"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"use_single_float"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"boolean"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Use 32-bit floats when possible"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"autoreset"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"boolean"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Reset buffer automatically"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"max_buffer_size"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">1024</span><span class="p">,</span><span class="w">
      </span><span class="nl">"maximum"</span><span class="p">:</span><span class="w"> </span><span class="mi">104857600</span><span class="p">,</span><span class="w">
      </span><span class="nl">"default"</span><span class="p">:</span><span class="w"> </span><span class="mi">1048576</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Maximum buffer size in bytes (1MB default)"</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h3 id="152-error-handling-schema">15.2 Error Handling Schema</h3>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://json-schema.org/draft/2020-12/schema"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"$id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/serialization-error"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Serialization Error Response"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"error_type"</span><span class="p">,</span><span class="w"> </span><span class="s2">"error_code"</span><span class="p">,</span><span class="w"> </span><span class="s2">"message"</span><span class="p">,</span><span class="w"> </span><span class="s2">"timestamp"</span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"error_type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
        </span><span class="s2">"SERIALIZATION_FAILED"</span><span class="p">,</span><span class="w">
        </span><span class="s2">"DESERIALIZATION_FAILED"</span><span class="p">,</span><span class="w"> 
        </span><span class="s2">"SCHEMA_VALIDATION_FAILED"</span><span class="p">,</span><span class="w">
        </span><span class="s2">"COMPRESSION_FAILED"</span><span class="p">,</span><span class="w">
        </span><span class="s2">"CHECKSUM_MISMATCH"</span><span class="p">,</span><span class="w">
        </span><span class="s2">"VERSION_INCOMPATIBLE"</span><span class="p">,</span><span class="w">
        </span><span class="s2">"SIZE_LIMIT_EXCEEDED"</span><span class="w">
      </span><span class="p">]</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"error_code"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^[A-Z0-9_]+$"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Machine-readable error code"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"maxLength"</span><span class="p">:</span><span class="w"> </span><span class="mi">500</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Human-readable error message"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"context"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"input_size_bytes"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="w"> </span><span class="p">},</span><span class="w">
        </span><span class="nl">"expected_schema"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
        </span><span class="nl">"actual_format"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
        </span><span class="nl">"validation_errors"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">}</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"recovery_suggestions"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"action"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">}</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h3 id="153-version-compatibility-rules">15.3 Version Compatibility Rules</h3>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://json-schema.org/draft/2020-12/schema"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"$id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://mister-smith.ai/schemas/version-compatibility"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Message Format Version Compatibility Matrix"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"current_version"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^</span><span class="se">\\</span><span class="s2">d+</span><span class="se">\\</span><span class="s2">.</span><span class="se">\\</span><span class="s2">d+</span><span class="se">\\</span><span class="s2">.</span><span class="se">\\</span><span class="s2">d+$"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"compatibility_matrix"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"patternProperties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"^\\d+\\.\\d+\\.\\d+$"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"read_compatible"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"boolean"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"write_compatible"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"boolean"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"requires_migration"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"boolean"</span><span class="w"> </span><span class="p">},</span><span class="w">
            </span><span class="nl">"migration_strategy"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
              </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"AUTOMATIC"</span><span class="p">,</span><span class="w"> </span><span class="s2">"MANUAL"</span><span class="p">,</span><span class="w"> </span><span class="s2">"UNSUPPORTED"</span><span class="p">]</span><span class="w">
            </span><span class="p">},</span><span class="w">
            </span><span class="nl">"breaking_changes"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
              </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
              </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">}</span><span class="w">
            </span><span class="p">}</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"migration_rules"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"from_version"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"to_version"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"transformation"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="w"> </span><span class="p">},</span><span class="w">
          </span><span class="nl">"data_loss_risk"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"NONE"</span><span class="p">,</span><span class="w"> </span><span class="s2">"LOW"</span><span class="p">,</span><span class="w"> </span><span class="s2">"MEDIUM"</span><span class="p">,</span><span class="w"> </span><span class="s2">"HIGH"</span><span class="p">]</span><span class="w"> </span><span class="p">}</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h2 id="summary">Summary</h2>

<p>This document provides comprehensive agent orchestration patterns including:</p>

<ol>
  <li><strong>Agent architecture</strong> - Core agent types (Planner, Executor, Critic, Router, Memory) with Rust trait definitions</li>
  <li><strong>Supervision patterns</strong> - Hub-and-spoke, event-driven message bus, and hierarchical supervision trees</li>
  <li><strong>Communication patterns</strong> - Direct RPC, publish-subscribe, blackboard, and mailbox patterns</li>
  <li><strong>Task distribution</strong> - Work queues and load balancing</li>
  <li><strong>Coordination patterns</strong> - Request-response and publish-subscribe</li>
  <li><strong>Agent discovery</strong> - Registry and health monitoring</li>
  <li><strong>Workflow orchestration</strong> - Sequential and parallel execution</li>
  <li><strong>Error handling</strong> - Recovery strategies and circuit breakers</li>
  <li><strong>Basic metrics</strong> - Simple performance monitoring</li>
  <li><strong>Spawn patterns</strong> - Role-based spawning with resource bounds</li>
  <li><strong>Tool-bus integration</strong> - Shared tool registry and agent-as-tool patterns</li>
  <li><strong>Extension mechanisms</strong> - Middleware and event emitter patterns</li>
</ol>

<h3 id="key-design-principles">Key Design Principles</h3>

<ol>
  <li><strong>Hierarchical supervision</strong> - Use hierarchical supervisors with bounded spawning</li>
  <li><strong>Event-driven architecture</strong> - Async channels with routing strategies</li>
  <li><strong>Shared tool registry</strong> - Tools with permission management</li>
  <li><strong>Context management</strong> - Windowed memory with periodic summarization</li>
  <li><strong>Extension points</strong> - Middleware, event emitters, and trait-based plugins</li>
  <li><strong>Resource control</strong> - Always bound agent spawning and set timeouts</li>
  <li><strong>Error boundaries</strong> - Isolate agent failures from system crashes</li>
</ol>

<p>These patterns provide a solid foundation for building distributed agent systems while avoiding common anti-patterns and maintaining type safety and performance.</p>
:ET
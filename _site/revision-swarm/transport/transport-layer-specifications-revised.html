<h1 id="transport-layer-specifications---foundation-patterns">Transport Layer Specifications - Foundation Patterns</h1>
<h2 id="agent-implementation-framework">Agent Implementation Framework</h2>

<blockquote>
  <p><strong>Canonical Reference</strong>: See <code class="language-plaintext highlighter-rouge">/Users/<USER>/<PERSON>-<PERSON>/<PERSON>-<PERSON>/tech-framework.md</code> for authoritative technology stack specifications</p>
</blockquote>

<p>As stated in the canonical source:</p>
<blockquote>
  <p>“This is the authoritative source for the Claude-Flow tech stack. All implementations should reference this document.”</p>
</blockquote>

<h2 id="overview">Overview</h2>

<p>This document defines foundational transport patterns for agent communication using the Claude-Flow Rust Stack. Focus is on basic communication patterns suitable for learning distributed systems.</p>

<p><strong>Technology Stack</strong> (from tech-framework.md):</p>
<ul>
  <li>async-nats 0.34</li>
  <li>Tonic 0.11 (gRPC)</li>
  <li>Axum 0.8 (HTTP)</li>
  <li>Tokio 1.38 (async runtime)</li>
</ul>

<h2 id="1-basic-message-transport-patterns">1. Basic Message Transport Patterns</h2>

<h3 id="11-request-response-pattern">1.1 Request-Response Pattern</h3>

<pre><code class="language-pseudocode">PATTERN RequestResponse:
    AGENT sends REQUEST to TARGET_AGENT
    TARGET_AGENT processes REQUEST
    TARGET_AGENT returns RESPONSE to AGENT
    
    Properties:
        - Synchronous communication
        - Direct addressing
        - Guaranteed response or timeout
</code></pre>

<h3 id="12-publish-subscribe-pattern">1.2 Publish-Subscribe Pattern</h3>

<pre><code class="language-pseudocode">PATTERN PublishSubscribe:
    PUBLISHER_AGENT publishes MESSAGE to TOPIC
    ALL SUBSCRIBER_AGENTS on TOPIC receive MESSAGE
    
    Properties:
        - Asynchronous broadcast
        - Topic-based routing
        - Multiple consumers
</code></pre>

<h3 id="13-queue-group-pattern">1.3 Queue Group Pattern</h3>

<pre><code class="language-pseudocode">PATTERN QueueGroup:
    PRODUCER_AGENT sends TASK to QUEUE
    ONE WORKER_AGENT from GROUP receives TASK
    
    Properties:
        - Load balancing
        - Work distribution
        - Single consumer per message
</code></pre>

<h3 id="14-blackboard-pattern">1.4 Blackboard Pattern</h3>

<pre><code class="language-pseudocode">PATTERN Blackboard:
    AGENTS write/read from SHARED_KNOWLEDGE_SPACE
    ALL AGENTS can observe changes to BLACKBOARD
    
    Properties:
        - Shared memory coordination
        - Decoupled collaboration
        - Knowledge persistence
        
    Implementation:
        - Shared store with versioning
        - Watch notifications on changes
        - Concurrent access control
</code></pre>

<h2 id="2-nats-messaging-foundations">2. NATS Messaging Foundations</h2>

<h3 id="21-basic-nats-subjects">2.1 Basic NATS Subjects</h3>

<pre><code class="language-pseudocode">SUBJECT_HIERARCHY:
    agents.{agent_id}.commands    # Direct agent commands
    agents.{agent_id}.status      # Agent status updates
    tasks.{task_type}.queue       # Task distribution
    events.{event_type}           # System events
    cmd.{type}.{target}           # Command routing

    # Claude-CLI Hook System Integration
    control.startup               # CLI initialization and capabilities
    agent.{id}.pre                # Pre-task hook processing
    agent.{id}.post               # Post-task hook processing
    agent.{id}.error              # Error hook handling
    agent.{id}.hook_response      # Hook mutation responses
    ctx.{gid}.file_change         # File change notifications

WILDCARD_PATTERNS:
    agents.*.status               # All agent statuses (single token)
    tasks.&gt;                       # All task queues (multiple tokens)
    task.*                        # All task types
    agent.*.pre                   # All pre-task hooks
    agent.*.post                  # All post-task hooks
    agent.*.error                 # All error hooks
    ctx.*.file_change             # All file change events

TOPIC_CONVENTIONS:
    task.created                  # Task lifecycle events
    task.assigned
    task.completed
    agent.ready                   # Agent state transitions
    agent.busy
    system.metrics                # System observability

    # Hook Lifecycle Events
    hook.startup.triggered        # Startup hook execution
    hook.pre_task.triggered       # Pre-task hook execution
    hook.post_task.triggered      # Post-task hook execution
    hook.on_error.triggered       # Error hook execution
    hook.on_file_change.triggered # File change hook execution
</code></pre>

<h3 id="211-claude-cli-hook-message-formats">2.1.1 Claude CLI Hook Message Formats</h3>

<pre><code class="language-pseudocode">HOOK_EVENT_MESSAGE_FORMAT:
    {
        "hook_type": "startup" | "pre_task" | "post_task" | "on_error" | "on_file_change",
        "agent_id": "string",
        "tool_name": "optional_string",
        "tool_input": "optional_json_object",
        "tool_response": "optional_json_object",
        "session_info": {
            "agent_id": "string",
            "session_id": "string",
            "model": "string",
            "start_time": "iso8601_timestamp"
        },
        "timestamp": "iso8601_timestamp",
        "context_id": "optional_string"
    }

HOOK_RESPONSE_MESSAGE_FORMAT:
    {
        "decision": "approve" | "block" | "continue",
        "reason": "optional_string",
        "continue": "boolean",
        "stop_reason": "optional_string",
        "modifications": "optional_json_object"
    }

TASK_OUTPUT_MESSAGE_FORMAT:
    {
        "task_info": {
            "type": "agent_id" | "description",
            "value": "string_or_number"
        },
        "output_line": "string",
        "task_status": "running" | "completed" | "failed",
        "timestamp": "iso8601_timestamp",
        "agent_id": "string"
    }
</code></pre>

<h3 id="22-nats-performance-characteristics">2.2 NATS Performance Characteristics</h3>

<pre><code class="language-pseudocode">PERFORMANCE_BENCHMARKS:
    CORE_NATS:
        throughput: 3+ million msgs/sec
        latency: microseconds to low milliseconds
        delivery: at-most-once (fire-and-forget)
        
    JETSTREAM:
        throughput: ~200k msgs/sec (fsync overhead)
        latency: P99 low milliseconds
        delivery: at-least-once with acknowledgments
        failure_mode: Publishers drop if fsync overwhelmed
</code></pre>

<h3 id="23-jetstream-persistence">2.3 JetStream Persistence</h3>

<pre><code class="language-pseudocode">STREAM_CONFIGURATION:
    CREATE STREAM "agent-events"
        subjects: ["agents.*.events"]
        storage: file
        retention: limits
        max_age: 7_days
        max_bytes: configurable
        discard_policy: old_on_full
        
    CREATE CONSUMER "event-processor"
        stream: "agent-events"
        deliver: all
        ack_policy: explicit
        
    RESOURCE_LIMITS:
        max_memory: 512M
        max_disk: 1G
        max_streams: 10
        max_consumers: 100
</code></pre>

<h3 id="24-basic-message-flow">2.4 Basic Message Flow</h3>

<pre><code class="language-pseudocode">AGENT_COMMUNICATION_FLOW:
    1. SENDER creates MESSAGE
    2. MESSAGE includes:
        - agent_id
        - message_type
        - payload
        - timestamp
    3. SENDER publishes to SUBJECT
    4. NATS routes to SUBSCRIBERS
    5. RECEIVERS process MESSAGE
    
MESSAGE_SCHEMAS:
    TaskAssignment:
        task_id: string
        task: Task
        deadline: optional&lt;Duration&gt;
        
    StatusUpdate:
        agent_id: string
        status: AgentStatus
        capacity: float
        
    ResultNotification:
        task_id: string
        result: TaskOutput
        metrics: ExecutionMetrics
        
    CapabilityQuery:
        required_capabilities: List&lt;Capability&gt;
        
    CapabilityResponse:
        agent_id: string
        capabilities: List&lt;Capability&gt;
        availability: Availability
</code></pre>

<h2 id="3-grpc-service-patterns">3. gRPC Service Patterns</h2>

<h3 id="31-basic-service-definition">3.1 Basic Service Definition</h3>

<pre><code class="language-pseudocode">SERVICE AgentCommunication:
    METHOD send_message(request) -&gt; response
    METHOD get_status(agent_id) -&gt; status
    METHOD list_agents() -&gt; agent_list
    
MESSAGE TYPES:
    - Simple request/response
    - Status queries
    - List operations
</code></pre>

<h3 id="32-streaming-patterns">3.2 Streaming Patterns</h3>

<pre><code class="language-pseudocode">STREAMING_PATTERNS:
    
    SERVER_STREAMING:
        CLIENT requests updates
        SERVER streams responses
        Use case: Status monitoring
        
    CLIENT_STREAMING:
        CLIENT streams requests
        SERVER returns summary
        Use case: Batch operations
        
    BIDIRECTIONAL_STREAMING:
        Both stream concurrently
        Use case: Real-time chat
</code></pre>

<h2 id="4-http-api-patterns">4. HTTP API Patterns</h2>

<h3 id="41-restful-endpoints">4.1 RESTful Endpoints</h3>

<pre><code class="language-pseudocode">API_STRUCTURE:
    GET  /agents              # List agents
    POST /agents              # Register agent
    GET  /agents/{id}         # Get agent details
    POST /agents/{id}/message # Send message
    
    GET  /tasks               # List tasks
    POST /tasks               # Create task
    GET  /tasks/{id}          # Get task status
</code></pre>

<h3 id="42-websocket-communication">4.2 WebSocket Communication</h3>

<pre><code class="language-pseudocode">WEBSOCKET_PROTOCOL:
    CONNECTION:
        Client connects to /ws
        Server accepts connection
        
    MESSAGE_FORMAT:
        type: "request" | "response" | "event"
        action: string
        payload: data
        
    PATTERNS:
        - Event notification
        - Real-time updates
        - Bidirectional messaging
</code></pre>

<h2 id="5-transport-abstraction">5. Transport Abstraction</h2>

<h3 id="51-generic-transport-interface">5.1 Generic Transport Interface</h3>

<pre><code class="language-pseudocode">INTERFACE Transport:
    connect(config)
    disconnect()
    send(message) -&gt; response
    subscribe(topic, handler)
    
IMPLEMENTATIONS:
    - NatsTransport
    - GrpcTransport
    - HttpTransport
</code></pre>

<h3 id="52-message-routing">5.2 Message Routing</h3>

<pre><code class="language-pseudocode">ROUTING_LOGIC:
    RECEIVE message
    EXTRACT destination
    LOOKUP transport for destination
    FORWARD using appropriate protocol
    
FALLBACK:
    If primary transport fails
    Try secondary transport
    Log routing decision
</code></pre>

<h2 id="6-agent-communication-patterns">6. Agent Communication Patterns</h2>

<h3 id="61-core-agent-message-interfaces">6.1 Core Agent Message Interfaces</h3>

<pre><code class="language-pseudocode">AGENT_INTERFACES:
    Planner:
        create_plan(goal) -&gt; TaskList
        refine_plan(feedback) -&gt; TaskList
        
    Executor:
        execute_task(task) -&gt; TaskOutput
        can_execute(task_type) -&gt; bool
        
    Critic:
        evaluate(output, criteria) -&gt; Feedback
        validate_plan(plan) -&gt; ValidationResult
        
    Router:
        route_task(task) -&gt; AgentId
        balance_load(tasks) -&gt; TaskAssignments
        
    Memory:
        store(key, value, metadata) -&gt; Result
        retrieve(key) -&gt; Option&lt;Value&gt;
        query(pattern) -&gt; List&lt;Results&gt;
</code></pre>

<h3 id="62-coordination-topologies">6.2 Coordination Topologies</h3>

<pre><code class="language-pseudocode">CENTRALIZED_PATTERN:
    ORCHESTRATOR maintains global_state
    ORCHESTRATOR assigns tasks to AGENTS
    AGENTS report results to ORCHESTRATOR
    
HIERARCHICAL_PATTERN:
    PARENT delegates to CHILDREN
    CHILDREN report to PARENT
    Multiple levels of delegation
    
PEER_TO_PEER_PATTERN:
    AGENTS communicate directly
    No central coordinator
    Self-organizing behavior
</code></pre>

<h2 id="7-advanced-connection-management">7. Advanced Connection Management</h2>

<h3 id="71-connection-pool-architecture">7.1 Connection Pool Architecture</h3>

<pre><code class="language-pseudocode">INTERFACE ConnectionPoolManager {
    create_pool(config: PoolConfig) -&gt; Pool
    monitor_health() -&gt; HealthMetrics
    handle_backpressure() -&gt; BackpressureAction
    scale_connections(metrics: LoadMetrics) -&gt; ScalingDecision
}

CLASS AdvancedConnectionPool {
    PRIVATE config: PoolConfiguration
    PRIVATE connections: ConnectionSet
    PRIVATE health_monitor: HealthMonitor
    PRIVATE metrics_collector: MetricsCollector
    
    STRUCT PoolConfiguration {
        max_connections: Integer = 10
        min_connections: Integer = 2
        acquire_timeout: Duration = 30_seconds
        idle_timeout: Duration = 600_seconds
        max_lifetime: Duration = 3600_seconds
        health_check_interval: Duration = 30_seconds
        connection_recycling: RecyclingStrategy = FAST
    }
}
</code></pre>

<h3 id="72-protocol-specific-pool-configurations">7.2 Protocol-Specific Pool Configurations</h3>

<pre><code class="language-pseudocode">-- NATS Connection Pool with Deadpool Pattern
CLASS NatsConnectionPool IMPLEMENTS ConnectionPoolManager {
    FUNCTION create_nats_pool(config: NatsConfig) -&gt; NatsPool {
        pool_config = {
            max_connections: config.max_connections,
            reconnect_strategy: ExponentialBackoff {
                initial_delay: Duration.millis(100),
                max_delay: Duration.seconds(5),
                max_attempts: 10
            },
            connection_timeout: Duration.seconds(10),
            subscription_capacity: 1000,
            jetstream_domain: config.jetstream_domain
        }
        
        RETURN create_managed_pool(pool_config)
    }
    
    FUNCTION configure_nats_connection(conn: NatsConnection) -&gt; Result {
        -- Post-connection configuration
        conn.set_subscription_capacity(1000)
        conn.configure_jetstream(js_config)
        conn.enable_heartbeat(Duration.seconds(30))
        RETURN Success()
    }
}

-- PostgreSQL Connection Pool (coordinated with data layer)
CLASS PostgresConnectionPool IMPLEMENTS ConnectionPoolManager {
    FUNCTION create_postgres_pool(config: PgConfig) -&gt; PgPool {
        pool_config = {
            max_connections: config.max_connections,
            min_connections: config.min_connections,
            acquire_timeout: Duration.seconds(30),
            idle_timeout: Duration.minutes(10),
            max_lifetime: Duration.hours(2),
            after_connect: configure_session
        }
        
        RETURN deadpool_postgres.create_pool(pool_config)
    }
    
    FUNCTION configure_session(conn: PgConnection) -&gt; Result {
        -- Session-level configuration
        conn.execute("SET application_name = 'agent_transport'")
        conn.execute("SET statement_timeout = '30s'")
        conn.execute("SET idle_in_transaction_session_timeout = '60s'")
        RETURN Success()
    }
}

-- gRPC Connection Pool
CLASS GrpcConnectionPool IMPLEMENTS ConnectionPoolManager {
    FUNCTION create_grpc_pool(config: GrpcConfig) -&gt; GrpcPool {
        pool_config = {
            max_connections: config.max_connections,
            keep_alive_interval: Duration.seconds(60),
            keep_alive_timeout: Duration.seconds(5),
            connect_timeout: Duration.seconds(10),
            http2_flow_control: true,
            max_message_size: 4_MB
        }
        
        RETURN create_grpc_pool_with_config(pool_config)
    }
}
</code></pre>

<h3 id="73-connection-string-templates-and-configuration">7.3 Connection String Templates and Configuration</h3>

<pre><code class="language-pseudocode">CLASS ConnectionStringManager {
    ENUM ProtocolType {
        NATS,
        POSTGRESQL,
        GRPC,
        HTTP
    }
    
    FUNCTION build_connection_string(
        protocol: ProtocolType, 
        config: Map&lt;String, String&gt;
    ) -&gt; String {
        SWITCH protocol {
            CASE NATS:
                RETURN build_nats_url(config)
            CASE POSTGRESQL:
                RETURN build_postgres_url(config)
            CASE GRPC:
                RETURN build_grpc_url(config)
            CASE HTTP:
                RETURN build_http_url(config)
        }
    }
    
    FUNCTION build_nats_url(config: Map&lt;String, String&gt;) -&gt; String {
        -- Support multiple formats:
        -- nats://user:pass@host:4222
        -- nats://host1:4222,host2:4222,host3:4222 (cluster)
        -- tls://user:pass@host:4222 (secure)
        
        IF config.contains("cluster_hosts") THEN
            hosts = config.get("cluster_hosts").split(",")
            RETURN "nats://" + hosts.join(",")
        ELSE
            user_pass = build_auth_string(config)
            host_port = config.get("host", "localhost") + ":" + config.get("port", "4222")
            protocol = config.get("tls", "false") == "true" ? "tls" : "nats"
            RETURN protocol + "://" + user_pass + host_port
        END IF
    }
    
    FUNCTION build_postgres_url(config: Map&lt;String, String&gt;) -&gt; String {
        -- Support multiple formats:
        -- postgres://user:pass@host:port/database
        -- postgres://%2Fvar%2Frun%2Fpostgresql/database (Unix socket)
        -- postgres://host/db?application_name=agent&amp;sslmode=require
        
        IF config.contains("socket_path") THEN
            encoded_path = url_encode(config.get("socket_path"))
            database = config.get("database", "postgres")
            RETURN "postgres://" + encoded_path + "/" + database
        ELSE
            user_pass = build_auth_string(config)
            host_port = config.get("host", "localhost") + ":" + config.get("port", "5432")
            database = config.get("database", "postgres")
            query_params = build_query_params(config)
            RETURN "postgres://" + user_pass + host_port + "/" + database + query_params
        END IF
    }
}

-- Environment-based configuration loading
CLASS EnvironmentConfigLoader {
    FUNCTION load_nats_config() -&gt; Map&lt;String, String&gt; {
        config = Map()
        config.put("host", env.get("NATS_HOST", "localhost"))
        config.put("port", env.get("NATS_PORT", "4222"))
        config.put("user", env.get("NATS_USER", ""))
        config.put("password", env.get("NATS_PASSWORD", ""))
        config.put("cluster_hosts", env.get("NATS_CLUSTER", ""))
        config.put("tls", env.get("NATS_TLS", "false"))
        RETURN config
    }
    
    FUNCTION load_postgres_config() -&gt; Map&lt;String, String&gt; {
        config = Map()
        config.put("host", env.get("PG_HOST", "localhost"))
        config.put("port", env.get("PG_PORT", "5432"))
        config.put("user", env.get("PG_USER", "postgres"))
        config.put("password", env.get("PG_PASSWORD", ""))
        config.put("database", env.get("PG_DATABASE", "postgres"))
        config.put("socket_path", env.get("PG_SOCKET_PATH", ""))
        config.put("application_name", env.get("PG_APP_NAME", "agent_transport"))
        config.put("sslmode", env.get("PG_SSL_MODE", "prefer"))
        RETURN config
    }
}
</code></pre>

<h3 id="74-advanced-health-checking-and-recovery">7.4 Advanced Health Checking and Recovery</h3>

<pre><code class="language-pseudocode">CLASS AdvancedHealthMonitor {
    PRIVATE health_checkers: Map&lt;ProtocolType, HealthChecker&gt;
    PRIVATE circuit_breakers: Map&lt;String, CircuitBreaker&gt;
    PRIVATE recovery_strategies: Map&lt;ProtocolType, RecoveryStrategy&gt;
    
    INTERFACE HealthChecker {
        check_health() -&gt; HealthStatus
        recover_connection() -&gt; RecoveryResult
        get_metrics() -&gt; HealthMetrics
    }
    
    CLASS NatsHealthChecker IMPLEMENTS HealthChecker {
        FUNCTION check_health() -&gt; HealthStatus {
            TRY {
                start_time = now()
                response = connection.ping()
                latency = now() - start_time
                
                IF latency &gt; Duration.seconds(1) THEN
                    RETURN HealthStatus.DEGRADED
                ELSE
                    RETURN HealthStatus.HEALTHY
                END IF
            } CATCH (error) {
                RETURN HealthStatus.UNHEALTHY
            }
        }
        
        FUNCTION recover_connection() -&gt; RecoveryResult {
            -- Implement exponential backoff reconnection
            attempts = 0
            max_attempts = 5
            backoff = Duration.millis(100)
            
            WHILE attempts &lt; max_attempts {
                TRY {
                    connection.reconnect()
                    IF check_health() == HEALTHY THEN
                        RETURN RecoveryResult.SUCCESS
                    END IF
                } CATCH (error) {
                    log_error("Reconnection attempt failed", error)
                }
                
                sleep(backoff)
                backoff = min(backoff * 2, Duration.seconds(5))
                attempts += 1
            }
            
            RETURN RecoveryResult.FAILED
        }
    }
    
    CLASS PostgresHealthChecker IMPLEMENTS HealthChecker {
        FUNCTION check_health() -&gt; HealthStatus {
            TRY {
                start_time = now()
                result = connection.execute("SELECT 1")
                latency = now() - start_time
                
                -- Check for slow queries
                IF latency &gt; Duration.millis(100) THEN
                    RETURN HealthStatus.DEGRADED
                ELSE
                    RETURN HealthStatus.HEALTHY
                END IF
            } CATCH (error) {
                RETURN HealthStatus.UNHEALTHY
            }
        }
    }
}

-- Circuit breaker pattern for connection management
CLASS ConnectionCircuitBreaker {
    ENUM CircuitState {
        CLOSED,     -- Normal operation
        OPEN,       -- Failing fast, not attempting connections
        HALF_OPEN   -- Testing if service has recovered
    }
    
    PRIVATE state: CircuitState = CLOSED
    PRIVATE failure_count: Integer = 0
    PRIVATE last_failure_time: Timestamp
    PRIVATE failure_threshold: Integer = 5
    PRIVATE recovery_timeout: Duration = Duration.seconds(60)
    
    FUNCTION attempt_connection() -&gt; ConnectionResult {
        SWITCH state {
            CASE CLOSED:
                RETURN try_connection()
            CASE OPEN:
                IF now() - last_failure_time &gt; recovery_timeout THEN
                    state = HALF_OPEN
                    RETURN try_connection()
                ELSE
                    RETURN ConnectionResult.CIRCUIT_OPEN
                END IF
            CASE HALF_OPEN:
                RETURN try_connection()
        }
    }
    
    FUNCTION record_success() {
        failure_count = 0
        state = CLOSED
    }
    
    FUNCTION record_failure() {
        failure_count += 1
        last_failure_time = now()
        
        IF failure_count &gt;= failure_threshold THEN
            state = OPEN
        END IF
    }
}
</code></pre>

<h3 id="75-resource-limits-and-backpressure-management">7.5 Resource Limits and Backpressure Management</h3>

<pre><code class="language-pseudocode">CLASS ResourceLimitManager {
    STRUCT ResourceLimits {
        max_memory_usage: Bytes = 512_MB
        max_connection_queue_size: Integer = 1000
        max_pending_requests: Integer = 10000
        connection_acquire_timeout: Duration = Duration.seconds(30)
        request_timeout: Duration = Duration.seconds(30)
        max_concurrent_operations: Integer = 100
    }
    
    CLASS BackpressureController {
        PRIVATE current_load: LoadMetrics
        PRIVATE resource_limits: ResourceLimits
        PRIVATE adaptive_throttling: AdaptiveThrottler
        
        FUNCTION handle_backpressure(request: ConnectionRequest) -&gt; BackpressureAction {
            current_metrics = collect_current_metrics()
            
            -- Check memory usage
            IF current_metrics.memory_usage &gt; resource_limits.max_memory_usage * 0.9 THEN
                RETURN BackpressureAction.REJECT_REQUEST
            END IF
            
            -- Check connection queue size
            IF current_metrics.queue_size &gt; resource_limits.max_connection_queue_size THEN
                RETURN BackpressureAction.QUEUE_FULL
            END IF
            
            -- Check pending request count
            IF current_metrics.pending_requests &gt; resource_limits.max_pending_requests THEN
                RETURN BackpressureAction.THROTTLE_REQUEST
            END IF
            
            -- Apply adaptive throttling based on success rate
            IF current_metrics.success_rate &lt; 0.95 THEN
                throttle_delay = adaptive_throttling.calculate_delay(current_metrics)
                RETURN BackpressureAction.DELAY_REQUEST(throttle_delay)
            END IF
            
            RETURN BackpressureAction.ALLOW_REQUEST
        }
        
        FUNCTION collect_current_metrics() -&gt; LoadMetrics {
            RETURN LoadMetrics {
                memory_usage: get_memory_usage(),
                queue_size: get_connection_queue_size(),
                pending_requests: get_pending_request_count(),
                success_rate: get_recent_success_rate(),
                average_latency: get_average_latency()
            }
        }
    }
    
    -- Protocol-specific backpressure handling
    CLASS NatsBackpressureHandler {
        FUNCTION handle_slow_consumer() {
            -- NATS built-in slow consumer protection
            subscription.set_pending_limits(1000, 50_MB)
            
            -- Custom overflow handling
            IF subscription.pending_messages() &gt; 800 THEN
                log_warning("Approaching NATS subscription limit")
                trigger_load_shedding()
            END IF
        }
        
        FUNCTION trigger_load_shedding() {
            -- Drop non-critical messages
            -- Increase processing parallelism
            -- Signal upstream producers to slow down
        }
    }
}
</code></pre>

<h3 id="76-performance-monitoring-and-metrics">7.6 Performance Monitoring and Metrics</h3>

<pre><code class="language-pseudocode">CLASS ConnectionPerformanceMonitor {
    PRIVATE metrics_collector: MetricsCollector
    PRIVATE alert_manager: AlertManager
    
    STRUCT ConnectionMetrics {
        pool_size: Integer
        active_connections: Integer
        idle_connections: Integer
        pending_acquisitions: Integer
        total_acquisitions: Counter
        failed_acquisitions: Counter
        acquisition_time_histogram: Histogram
        connection_lifetime_histogram: Histogram
        health_check_success_rate: Gauge
    }
    
    FUNCTION collect_metrics() -&gt; ConnectionMetrics {
        RETURN ConnectionMetrics {
            pool_size: connection_pool.size(),
            active_connections: connection_pool.active_count(),
            idle_connections: connection_pool.idle_count(),
            pending_acquisitions: connection_pool.pending_count(),
            total_acquisitions: acquisition_counter.value(),
            failed_acquisitions: failure_counter.value(),
            acquisition_time_histogram: acquisition_timer.snapshot(),
            connection_lifetime_histogram: lifetime_timer.snapshot(),
            health_check_success_rate: health_success_rate.value()
        }
    }
    
    FUNCTION monitor_thresholds() {
        metrics = collect_metrics()
        
        -- Connection pool utilization alerts
        utilization = metrics.active_connections / metrics.pool_size
        IF utilization &gt; 0.9 THEN
            alert_manager.trigger_alert(AlertType.HIGH_POOL_UTILIZATION, utilization)
        END IF
        
        -- Acquisition time alerts
        p95_acquisition_time = metrics.acquisition_time_histogram.percentile(95)
        IF p95_acquisition_time &gt; Duration.seconds(5) THEN
            alert_manager.trigger_alert(AlertType.SLOW_ACQUISITION, p95_acquisition_time)
        END IF
        
        -- Failed acquisition rate alerts
        failure_rate = metrics.failed_acquisitions / metrics.total_acquisitions
        IF failure_rate &gt; 0.05 THEN
            alert_manager.trigger_alert(AlertType.HIGH_FAILURE_RATE, failure_rate)
        END IF
        
        -- Health check alerts
        IF metrics.health_check_success_rate &lt; 0.95 THEN
            alert_manager.trigger_alert(AlertType.HEALTH_CHECK_FAILURES, 
                                      metrics.health_check_success_rate)
        END IF
    }
    
    FUNCTION export_metrics_for_prometheus() -&gt; PrometheusMetrics {
        -- Export metrics in Prometheus format for monitoring
        metrics = collect_metrics()
        
        prometheus_metrics = PrometheusMetrics()
        prometheus_metrics.add_gauge("connection_pool_size", metrics.pool_size)
        prometheus_metrics.add_gauge("connection_pool_active", metrics.active_connections)
        prometheus_metrics.add_gauge("connection_pool_idle", metrics.idle_connections)
        prometheus_metrics.add_counter("connection_acquisitions_total", metrics.total_acquisitions)
        prometheus_metrics.add_counter("connection_acquisition_failures_total", metrics.failed_acquisitions)
        prometheus_metrics.add_histogram("connection_acquisition_duration_seconds", 
                                        metrics.acquisition_time_histogram)
        
        RETURN prometheus_metrics
    }
}
</code></pre>

<h2 id="8-error-handling-patterns">8. Error Handling Patterns</h2>

<h3 id="81-basic-retry-logic">8.1 Basic Retry Logic</h3>

<pre><code class="language-pseudocode">RETRY_PATTERN:
    attempts = 0
    WHILE attempts &lt; max_retries:
        TRY:
            SEND message
            RETURN success
        CATCH error:
            attempts += 1
            WAIT backoff_time
    RETURN failure
</code></pre>

<h3 id="82-timeout-handling">8.2 Timeout Handling</h3>

<pre><code class="language-pseudocode">TIMEOUT_PATTERN:
    START timer(timeout_duration)
    SEND request
    WAIT for response OR timeout
    IF timeout:
        CANCEL request
        RETURN timeout_error
    ELSE:
        RETURN response
</code></pre>

<h2 id="9-basic-security">9. Basic Security</h2>

<h3 id="91-tls-configuration">9.1 TLS Configuration</h3>

<pre><code class="language-pseudocode">TLS_SETUP:
    LOAD certificates
    CONFIGURE tls_config:
        - server_cert
        - server_key
        - ca_cert (required for mTLS)
    APPLY to transport layer
    
mTLS_PATTERN:
    SERVER_CONFIG:
        cert_file: "./certs/nats-server.crt"
        key_file:  "./certs/nats-server.key"
        ca_file:   "./certs/ca.crt"
        verify: true  # Enforce client certificates
        
    CLIENT_CONFIG:
        require_tls: true
        root_certificates: ca.crt
        client_certificate: client.crt
        client_key: client.key
</code></pre>

<h3 id="92-authentication-pattern">9.2 Authentication Pattern</h3>

<pre><code class="language-pseudocode">AUTH_FLOW:
    CLIENT sends credentials
    SERVER validates credentials
    SERVER returns token
    CLIENT includes token in requests
    SERVER validates token on each request
    
SUBJECT_AUTHORIZATION:
    PER_USER_ACL:
        admin_permissions:
            publish: [ "&gt;" ]      # Full access
            subscribe: [ "&gt;" ]
            
        tenant_permissions:
            publish: { allow: ["tenantA.&gt;"] }
            subscribe: { allow: ["tenantA.&gt;"] }
            
    ACCOUNT_ISOLATION:
        - Each tenant gets NATS account
        - Complete namespace separation
        - Built-in multi-tenancy
        
    SECURITY_PRINCIPLES:
        - Never share accounts between tenants
        - Always enforce mTLS
        - Apply least privilege subjects
        - Set resource quotas per account
        - Monitor wildcard usage
</code></pre>

<h3 id="93-zero-downtime-key-rotation">9.3 Zero-Downtime Key Rotation</h3>

<pre><code class="language-pseudocode">KEY_ROTATION_PATTERN:
    STATE_MACHINE:
        KeyAActive -&gt; StagingNewKey
        StagingNewKey -&gt; ReloadingConfig
        ReloadingConfig -&gt; KeyBActive
        
    IMPLEMENTATION:
        SIGNAL_HANDLER(SIGHUP):
            LOAD new_certificates
            ATOMIC_SWAP certificates
            UPDATE connections
            NO_DOWNTIME
</code></pre>

<h2 id="10-implementation-guidelines">10. Implementation Guidelines</h2>

<h3 id="101-agent-integration">10.1 Agent Integration</h3>

<p>Agents implementing transport should:</p>
<ol>
  <li>Choose appropriate pattern for use case</li>
  <li>Handle connection failures gracefully</li>
  <li>Implement basic retry logic</li>
  <li>Log communication events</li>
</ol>

<h3 id="102-testing-patterns">10.2 Testing Patterns</h3>

<pre><code class="language-pseudocode">TEST_SCENARIOS:
    - Connection establishment
    - Message delivery
    - Timeout handling
    - Reconnection logic
    - Basic error cases
</code></pre>

<h2 id="11-configuration-templates">11. Configuration Templates</h2>

<h3 id="111-nats-configuration">11.1 NATS Configuration</h3>

<pre><code class="language-pseudocode">NATS_CONFIG:
    servers: ["nats://localhost:4222"]
    max_reconnects: 5
    reconnect_wait: 2_seconds
    timeout: 10_seconds
</code></pre>

<h3 id="112-grpc-configuration">11.2 gRPC Configuration</h3>

<pre><code class="language-pseudocode">GRPC_CONFIG:
    address: "localhost:50051"
    timeout: 30_seconds
    keepalive: 60_seconds
    max_message_size: 4_mb
</code></pre>

<h3 id="113-http-configuration">11.3 HTTP Configuration</h3>

<pre><code class="language-pseudocode">HTTP_CONFIG:
    bind_address: "0.0.0.0:8080"
    request_timeout: 30_seconds
    max_connections: 100
</code></pre>

<h2 id="summary">Summary</h2>

<p>This document provides foundational transport patterns for agent communication. Focus is on:</p>
<ul>
  <li>Basic messaging patterns</li>
  <li>Simple protocol usage</li>
  <li>Foundation error handling</li>
  <li>Essential connection management</li>
</ul>

<p>Agents should implement these patterns incrementally, starting with basic request-response and expanding as needed.</p>

<h2 id="12-nats-concrete-implementation">12. NATS Concrete Implementation</h2>

<h3 id="121-complete-subject-taxonomy">12.1 Complete Subject Taxonomy</h3>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="err">AGENT_SUBJECTS:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"commands"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agents.{agent_id}.commands.{command_type}"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"status"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agents.{agent_id}.status.{status_type}"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"heartbeat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agents.{agent_id}.heartbeat"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"capabilities"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agents.{agent_id}.capabilities"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"metrics"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agents.{agent_id}.metrics.{metric_type}"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"logs"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agents.{agent_id}.logs.{level}"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"events"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agents.{agent_id}.events.{event_type}"</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="err">TASK_SUBJECTS:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"assignment"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tasks.{task_type}.assignment"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"queue"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tasks.{task_type}.queue.{priority}"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"progress"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tasks.{task_id}.progress"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"result"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tasks.{task_id}.result"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"error"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tasks.{task_id}.error"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"cancel"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tasks.{task_id}.cancel"</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="err">SYSTEM_SUBJECTS:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"control"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system.control.{operation}"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"discovery"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system.discovery.{service_type}"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"health"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system.health.{component}"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"config"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system.config.{component}.{action}"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"alerts"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system.alerts.{severity}"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"audit"</span><span class="p">:</span><span class="w"> </span><span class="s2">"system.audit.{action}"</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="err">WORKFLOW_SUBJECTS:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"orchestration"</span><span class="p">:</span><span class="w"> </span><span class="s2">"workflow.{workflow_id}.orchestration"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"coordination"</span><span class="p">:</span><span class="w"> </span><span class="s2">"workflow.{workflow_id}.coordination"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"dependencies"</span><span class="p">:</span><span class="w"> </span><span class="s2">"workflow.{workflow_id}.dependencies"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"rollback"</span><span class="p">:</span><span class="w"> </span><span class="s2">"workflow.{workflow_id}.rollback"</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="err">CLAUDE_CLI_SUBJECTS:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"startup"</span><span class="p">:</span><span class="w"> </span><span class="s2">"cli.startup"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"hooks"</span><span class="p">:</span><span class="w"> </span><span class="s2">"cli.hooks.{hook_type}.{agent_id}"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"responses"</span><span class="p">:</span><span class="w"> </span><span class="s2">"cli.responses.{agent_id}"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"mutations"</span><span class="p">:</span><span class="w"> </span><span class="s2">"cli.mutations.{agent_id}"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"context"</span><span class="p">:</span><span class="w"> </span><span class="s2">"cli.context.{group_id}.{context_type}"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h3 id="122-message-payload-schemas">12.2 Message Payload Schemas</h3>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="err">AGENT_COMMAND_SCHEMA:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"http://json-schema.org/draft-07/schema#"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"command_id"</span><span class="p">,</span><span class="w"> </span><span class="s2">"command_type"</span><span class="p">,</span><span class="w"> </span><span class="s2">"agent_id"</span><span class="p">,</span><span class="w"> </span><span class="s2">"timestamp"</span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"command_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"command_type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"execute"</span><span class="p">,</span><span class="w"> </span><span class="s2">"stop"</span><span class="p">,</span><span class="w"> </span><span class="s2">"pause"</span><span class="p">,</span><span class="w"> </span><span class="s2">"resume"</span><span class="p">,</span><span class="w"> </span><span class="s2">"configure"</span><span class="p">]},</span><span class="w">
    </span><span class="nl">"agent_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^[a-zA-Z0-9_-]+$"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"payload"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"priority"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="nl">"maximum"</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">},</span><span class="w">
    </span><span class="nl">"timeout_ms"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span><span class="p">},</span><span class="w">
    </span><span class="nl">"reply_to"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^[a-zA-Z0-9._-]+$"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"correlation_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"metadata"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"source"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">},</span><span class="w">
        </span><span class="nl">"trace_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">},</span><span class="w">
        </span><span class="nl">"user_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">},</span><span class="w">
        </span><span class="nl">"session_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="err">AGENT_STATUS_SCHEMA:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"http://json-schema.org/draft-07/schema#"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"agent_id"</span><span class="p">,</span><span class="w"> </span><span class="s2">"status"</span><span class="p">,</span><span class="w"> </span><span class="s2">"timestamp"</span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"agent_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^[a-zA-Z0-9_-]+$"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"status"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"idle"</span><span class="p">,</span><span class="w"> </span><span class="s2">"busy"</span><span class="p">,</span><span class="w"> </span><span class="s2">"error"</span><span class="p">,</span><span class="w"> </span><span class="s2">"offline"</span><span class="p">,</span><span class="w"> </span><span class="s2">"starting"</span><span class="p">,</span><span class="w"> </span><span class="s2">"stopping"</span><span class="p">]},</span><span class="w">
    </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"capacity"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="nl">"maximum"</span><span class="p">:</span><span class="w"> </span><span class="mf">1.0</span><span class="p">},</span><span class="w">
    </span><span class="nl">"current_tasks"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">},</span><span class="w">
    </span><span class="nl">"max_tasks"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">},</span><span class="w">
    </span><span class="nl">"uptime_seconds"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">},</span><span class="w">
    </span><span class="nl">"last_heartbeat"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"capabilities"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"resource_usage"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"cpu_percent"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="nl">"maximum"</span><span class="p">:</span><span class="w"> </span><span class="mf">100.0</span><span class="p">},</span><span class="w">
        </span><span class="nl">"memory_mb"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">},</span><span class="w">
        </span><span class="nl">"disk_mb"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="err">TASK_ASSIGNMENT_SCHEMA:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"http://json-schema.org/draft-07/schema#"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"task_id"</span><span class="p">,</span><span class="w"> </span><span class="s2">"task_type"</span><span class="p">,</span><span class="w"> </span><span class="s2">"assigned_agent"</span><span class="p">,</span><span class="w"> </span><span class="s2">"timestamp"</span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"task_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"task_type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"analysis"</span><span class="p">,</span><span class="w"> </span><span class="s2">"synthesis"</span><span class="p">,</span><span class="w"> </span><span class="s2">"execution"</span><span class="p">,</span><span class="w"> </span><span class="s2">"validation"</span><span class="p">,</span><span class="w"> </span><span class="s2">"monitoring"</span><span class="p">]},</span><span class="w">
    </span><span class="nl">"assigned_agent"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^[a-zA-Z0-9_-]+$"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"priority"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="nl">"maximum"</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">},</span><span class="w">
    </span><span class="nl">"deadline"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"requirements"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"capabilities"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w"> </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">}},</span><span class="w">
        </span><span class="nl">"resources"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
          </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"cpu_cores"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">},</span><span class="w">
            </span><span class="nl">"memory_mb"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">256</span><span class="p">},</span><span class="w">
            </span><span class="nl">"disk_mb"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="p">}</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"task_data"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"dependencies"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w"> </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">}},</span><span class="w">
    </span><span class="nl">"callback_subject"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="err">TASK_RESULT_SCHEMA:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"http://json-schema.org/draft-07/schema#"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"task_id"</span><span class="p">,</span><span class="w"> </span><span class="s2">"agent_id"</span><span class="p">,</span><span class="w"> </span><span class="s2">"status"</span><span class="p">,</span><span class="w"> </span><span class="s2">"timestamp"</span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"task_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"agent_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^[a-zA-Z0-9_-]+$"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"status"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"completed"</span><span class="p">,</span><span class="w"> </span><span class="s2">"failed"</span><span class="p">,</span><span class="w"> </span><span class="s2">"cancelled"</span><span class="p">,</span><span class="w"> </span><span class="s2">"timeout"</span><span class="p">]},</span><span class="w">
    </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w"> </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"execution_time_ms"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">},</span><span class="w">
    </span><span class="nl">"result_data"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">},</span><span class="w">
    </span><span class="nl">"error_details"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"error_code"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">},</span><span class="w">
        </span><span class="nl">"error_message"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">},</span><span class="w">
        </span><span class="nl">"stack_trace"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">},</span><span class="w">
        </span><span class="nl">"retry_count"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w"> </span><span class="nl">"minimum"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"metrics"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"cpu_usage_percent"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">},</span><span class="w">
        </span><span class="nl">"memory_peak_mb"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"number"</span><span class="p">},</span><span class="w">
        </span><span class="nl">"io_operations"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h3 id="123-jetstream-configuration-specifications">12.3 JetStream Configuration Specifications</h3>

<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">JETSTREAM_STREAMS</span><span class="pi">:</span>
  <span class="na">agent_events</span><span class="pi">:</span>
    <span class="na">subjects</span><span class="pi">:</span> <span class="pi">[</span><span class="s2">"</span><span class="s">agents.*.events.&gt;"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">agents.*.status"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">agents.*.heartbeat"</span><span class="pi">]</span>
    <span class="na">storage</span><span class="pi">:</span> <span class="s">file</span>
    <span class="na">retention</span><span class="pi">:</span> <span class="s">limits</span>
    <span class="na">max_age</span><span class="pi">:</span> <span class="m">604800</span>  <span class="c1"># 7 days in seconds</span>
    <span class="na">max_bytes</span><span class="pi">:</span> <span class="m">1073741824</span>  <span class="c1"># 1GB</span>
    <span class="na">max_msgs</span><span class="pi">:</span> <span class="m">1000000</span>
    <span class="na">max_msg_size</span><span class="pi">:</span> <span class="m">1048576</span>  <span class="c1"># 1MB</span>
    <span class="na">discard</span><span class="pi">:</span> <span class="s">old</span>
    <span class="na">num_replicas</span><span class="pi">:</span> <span class="m">3</span>
    <span class="na">duplicate_window</span><span class="pi">:</span> <span class="m">120</span>  <span class="c1"># 2 minutes</span>

  <span class="na">task_lifecycle</span><span class="pi">:</span>
    <span class="na">subjects</span><span class="pi">:</span> <span class="pi">[</span><span class="s2">"</span><span class="s">tasks.*.assignment"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">tasks.*.result"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">tasks.*.progress"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">tasks.*.error"</span><span class="pi">]</span>
    <span class="na">storage</span><span class="pi">:</span> <span class="s">file</span>
    <span class="na">retention</span><span class="pi">:</span> <span class="s">limits</span>
    <span class="na">max_age</span><span class="pi">:</span> <span class="m">2592000</span>  <span class="c1"># 30 days</span>
    <span class="na">max_bytes</span><span class="pi">:</span> <span class="m">5368709120</span>  <span class="c1"># 5GB</span>
    <span class="na">max_msgs</span><span class="pi">:</span> <span class="m">10000000</span>
    <span class="na">max_msg_size</span><span class="pi">:</span> <span class="m">10485760</span>  <span class="c1"># 10MB</span>
    <span class="na">discard</span><span class="pi">:</span> <span class="s">old</span>
    <span class="na">num_replicas</span><span class="pi">:</span> <span class="m">3</span>
    <span class="na">duplicate_window</span><span class="pi">:</span> <span class="m">300</span>  <span class="c1"># 5 minutes</span>

  <span class="na">system_monitoring</span><span class="pi">:</span>
    <span class="na">subjects</span><span class="pi">:</span> <span class="pi">[</span><span class="s2">"</span><span class="s">system.&gt;.health"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">system.&gt;.alerts"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">system.&gt;.audit"</span><span class="pi">]</span>
    <span class="na">storage</span><span class="pi">:</span> <span class="s">file</span>
    <span class="na">retention</span><span class="pi">:</span> <span class="s">limits</span>
    <span class="na">max_age</span><span class="pi">:</span> <span class="m">7776000</span>  <span class="c1"># 90 days</span>
    <span class="na">max_bytes</span><span class="pi">:</span> <span class="m">**********</span>  <span class="c1"># 2GB</span>
    <span class="na">max_msgs</span><span class="pi">:</span> <span class="m">5000000</span>
    <span class="na">max_msg_size</span><span class="pi">:</span> <span class="m">524288</span>  <span class="c1"># 512KB</span>
    <span class="na">discard</span><span class="pi">:</span> <span class="s">old</span>
    <span class="na">num_replicas</span><span class="pi">:</span> <span class="m">3</span>
    <span class="na">duplicate_window</span><span class="pi">:</span> <span class="m">60</span>  <span class="c1"># 1 minute</span>

  <span class="na">claude_cli_integration</span><span class="pi">:</span>
    <span class="na">subjects</span><span class="pi">:</span> <span class="pi">[</span><span class="s2">"</span><span class="s">cli.&gt;"</span><span class="pi">]</span>
    <span class="na">storage</span><span class="pi">:</span> <span class="s">memory</span>
    <span class="na">retention</span><span class="pi">:</span> <span class="s">limits</span>
    <span class="na">max_age</span><span class="pi">:</span> <span class="m">3600</span>  <span class="c1"># 1 hour</span>
    <span class="na">max_bytes</span><span class="pi">:</span> <span class="m">268435456</span>  <span class="c1"># 256MB</span>
    <span class="na">max_msgs</span><span class="pi">:</span> <span class="m">100000</span>
    <span class="na">max_msg_size</span><span class="pi">:</span> <span class="m">1048576</span>  <span class="c1"># 1MB</span>
    <span class="na">discard</span><span class="pi">:</span> <span class="s">new</span>
    <span class="na">num_replicas</span><span class="pi">:</span> <span class="m">1</span>
    <span class="na">duplicate_window</span><span class="pi">:</span> <span class="m">30</span>  <span class="c1"># 30 seconds</span>

<span class="na">JETSTREAM_CONSUMERS</span><span class="pi">:</span>
  <span class="na">agent_status_monitor</span><span class="pi">:</span>
    <span class="na">stream</span><span class="pi">:</span> <span class="s">agent_events</span>
    <span class="na">filter_subject</span><span class="pi">:</span> <span class="s2">"</span><span class="s">agents.*.status"</span>
    <span class="na">deliver_policy</span><span class="pi">:</span> <span class="s">new</span>
    <span class="na">ack_policy</span><span class="pi">:</span> <span class="s">explicit</span>
    <span class="na">ack_wait</span><span class="pi">:</span> <span class="m">30</span>
    <span class="na">max_deliver</span><span class="pi">:</span> <span class="m">3</span>
    <span class="na">replay_policy</span><span class="pi">:</span> <span class="s">instant</span>
    <span class="na">max_ack_pending</span><span class="pi">:</span> <span class="m">1000</span>

  <span class="na">task_processor</span><span class="pi">:</span>
    <span class="na">stream</span><span class="pi">:</span> <span class="s">task_lifecycle</span>
    <span class="na">filter_subject</span><span class="pi">:</span> <span class="s2">"</span><span class="s">tasks.*.assignment"</span>
    <span class="na">deliver_policy</span><span class="pi">:</span> <span class="s">all</span>
    <span class="na">ack_policy</span><span class="pi">:</span> <span class="s">explicit</span>
    <span class="na">ack_wait</span><span class="pi">:</span> <span class="m">300</span>  <span class="c1"># 5 minutes</span>
    <span class="na">max_deliver</span><span class="pi">:</span> <span class="m">5</span>
    <span class="na">replay_policy</span><span class="pi">:</span> <span class="s">instant</span>
    <span class="na">max_ack_pending</span><span class="pi">:</span> <span class="m">100</span>

  <span class="na">system_alerting</span><span class="pi">:</span>
    <span class="na">stream</span><span class="pi">:</span> <span class="s">system_monitoring</span>
    <span class="na">filter_subject</span><span class="pi">:</span> <span class="s2">"</span><span class="s">system.*.alerts.&gt;"</span>
    <span class="na">deliver_policy</span><span class="pi">:</span> <span class="s">new</span>
    <span class="na">ack_policy</span><span class="pi">:</span> <span class="s">explicit</span>
    <span class="na">ack_wait</span><span class="pi">:</span> <span class="m">60</span>
    <span class="na">max_deliver</span><span class="pi">:</span> <span class="m">3</span>
    <span class="na">replay_policy</span><span class="pi">:</span> <span class="s">instant</span>
    <span class="na">max_ack_pending</span><span class="pi">:</span> <span class="m">500</span>

<span class="na">RESOURCE_LIMITS</span><span class="pi">:</span>
  <span class="na">max_memory</span><span class="pi">:</span> <span class="m">**********</span>  <span class="c1"># 2GB</span>
  <span class="na">max_storage</span><span class="pi">:</span> <span class="m">107374182400</span>  <span class="c1"># 100GB</span>
  <span class="na">max_streams</span><span class="pi">:</span> <span class="m">50</span>
  <span class="na">max_consumers</span><span class="pi">:</span> <span class="m">500</span>
  <span class="na">max_connections</span><span class="pi">:</span> <span class="m">10000</span>
  <span class="na">max_subscriptions</span><span class="pi">:</span> <span class="m">100000</span>
</code></pre></div></div>

<h2 id="13-grpc-service-definitions">13. gRPC Service Definitions</h2>

<h3 id="131-agent-communication-services">13.1 Agent Communication Services</h3>

<div class="language-protobuf highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">syntax</span> <span class="o">=</span> <span class="s">"proto3"</span><span class="p">;</span>
<span class="kn">package</span> <span class="nn">mister_smith</span><span class="o">.</span><span class="n">agent</span><span class="p">;</span>

<span class="k">import</span> <span class="s">"google/protobuf/timestamp.proto"</span><span class="p">;</span>
<span class="k">import</span> <span class="s">"google/protobuf/any.proto"</span><span class="p">;</span>
<span class="k">import</span> <span class="s">"google/protobuf/empty.proto"</span><span class="p">;</span>

<span class="c1">// Core Agent Communication Service</span>
<span class="kd">service</span> <span class="n">AgentCommunication</span> <span class="p">{</span>
  <span class="c1">// Unary RPCs</span>
  <span class="k">rpc</span> <span class="n">SendCommand</span><span class="p">(</span><span class="n">CommandRequest</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">CommandResponse</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">GetStatus</span><span class="p">(</span><span class="n">StatusRequest</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">AgentStatus</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">RegisterAgent</span><span class="p">(</span><span class="n">AgentRegistration</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">RegistrationResponse</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">GetCapabilities</span><span class="p">(</span><span class="n">AgentIdentifier</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">AgentCapabilities</span><span class="p">);</span>
  
  <span class="c1">// Server streaming</span>
  <span class="k">rpc</span> <span class="n">StreamStatus</span><span class="p">(</span><span class="n">StatusRequest</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">stream</span> <span class="n">AgentStatus</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">StreamEvents</span><span class="p">(</span><span class="n">EventSubscription</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">stream</span> <span class="n">AgentEvent</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">StreamLogs</span><span class="p">(</span><span class="n">LogSubscription</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">stream</span> <span class="n">LogEntry</span><span class="p">);</span>
  
  <span class="c1">// Client streaming</span>
  <span class="k">rpc</span> <span class="n">UploadResults</span><span class="p">(</span><span class="n">stream</span> <span class="n">TaskResult</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">UploadSummary</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">StreamMetrics</span><span class="p">(</span><span class="n">stream</span> <span class="n">AgentMetrics</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">MetricsSummary</span><span class="p">);</span>
  
  <span class="c1">// Bidirectional streaming</span>
  <span class="k">rpc</span> <span class="n">AgentChat</span><span class="p">(</span><span class="n">stream</span> <span class="n">AgentMessage</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">stream</span> <span class="n">AgentMessage</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">TaskCoordination</span><span class="p">(</span><span class="n">stream</span> <span class="n">CoordinationMessage</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">stream</span> <span class="n">CoordinationMessage</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">// Task Management Service</span>
<span class="kd">service</span> <span class="n">TaskManagement</span> <span class="p">{</span>
  <span class="k">rpc</span> <span class="n">AssignTask</span><span class="p">(</span><span class="n">TaskAssignment</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">TaskAcceptance</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">GetTaskStatus</span><span class="p">(</span><span class="n">TaskIdentifier</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">TaskStatus</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">CancelTask</span><span class="p">(</span><span class="n">TaskIdentifier</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">TaskCancellation</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">StreamTaskProgress</span><span class="p">(</span><span class="n">TaskIdentifier</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">stream</span> <span class="n">TaskProgress</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">SubmitResult</span><span class="p">(</span><span class="n">TaskResult</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">ResultAcknowledgment</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">// Agent Discovery Service</span>
<span class="kd">service</span> <span class="n">AgentDiscovery</span> <span class="p">{</span>
  <span class="k">rpc</span> <span class="n">DiscoverAgents</span><span class="p">(</span><span class="n">DiscoveryQuery</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">AgentList</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">RegisterCapability</span><span class="p">(</span><span class="n">CapabilityRegistration</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">google.protobuf.Empty</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">FindAgentsWithCapability</span><span class="p">(</span><span class="n">CapabilityQuery</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">AgentList</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">StreamAgentUpdates</span><span class="p">(</span><span class="n">DiscoverySubscription</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">stream</span> <span class="n">AgentUpdate</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">// Health Check Service (following gRPC health checking protocol)</span>
<span class="kd">service</span> <span class="n">Health</span> <span class="p">{</span>
  <span class="k">rpc</span> <span class="n">Check</span><span class="p">(</span><span class="n">HealthCheckRequest</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">HealthCheckResponse</span><span class="p">);</span>
  <span class="k">rpc</span> <span class="n">Watch</span><span class="p">(</span><span class="n">HealthCheckRequest</span><span class="p">)</span> <span class="k">returns</span> <span class="p">(</span><span class="n">stream</span> <span class="n">HealthCheckResponse</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">// Message Definitions</span>
<span class="kd">message</span> <span class="nc">CommandRequest</span> <span class="p">{</span>
  <span class="kt">string</span> <span class="na">command_id</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">agent_id</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="n">CommandType</span> <span class="na">command_type</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="n">google.protobuf.Any</span> <span class="na">payload</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="kt">int32</span> <span class="na">priority</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
  <span class="kt">int64</span> <span class="na">timeout_ms</span> <span class="o">=</span> <span class="mi">6</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">correlation_id</span> <span class="o">=</span> <span class="mi">7</span><span class="p">;</span>
  <span class="n">RequestMetadata</span> <span class="na">metadata</span> <span class="o">=</span> <span class="mi">8</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">message</span> <span class="nc">CommandResponse</span> <span class="p">{</span>
  <span class="kt">string</span> <span class="na">command_id</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="n">ResponseStatus</span> <span class="na">status</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="kt">string</span> <span class="kd">message</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="n">google.protobuf.Any</span> <span class="na">result</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="kt">int64</span> <span class="na">execution_time_ms</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">message</span> <span class="nc">AgentStatus</span> <span class="p">{</span>
  <span class="kt">string</span> <span class="na">agent_id</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="n">AgentState</span> <span class="na">state</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="n">google.protobuf.Timestamp</span> <span class="na">timestamp</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="kt">float</span> <span class="na">capacity</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="kt">int32</span> <span class="na">current_tasks</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
  <span class="kt">int32</span> <span class="na">max_tasks</span> <span class="o">=</span> <span class="mi">6</span><span class="p">;</span>
  <span class="kt">int64</span> <span class="na">uptime_seconds</span> <span class="o">=</span> <span class="mi">7</span><span class="p">;</span>
  <span class="n">ResourceUsage</span> <span class="na">resource_usage</span> <span class="o">=</span> <span class="mi">8</span><span class="p">;</span>
  <span class="k">repeated</span> <span class="kt">string</span> <span class="na">capabilities</span> <span class="o">=</span> <span class="mi">9</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">message</span> <span class="nc">TaskAssignment</span> <span class="p">{</span>
  <span class="kt">string</span> <span class="na">task_id</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="n">TaskType</span> <span class="na">task_type</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">assigned_agent</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="n">google.protobuf.Timestamp</span> <span class="na">deadline</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="kt">int32</span> <span class="na">priority</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
  <span class="n">TaskRequirements</span> <span class="na">requirements</span> <span class="o">=</span> <span class="mi">6</span><span class="p">;</span>
  <span class="n">google.protobuf.Any</span> <span class="na">task_data</span> <span class="o">=</span> <span class="mi">7</span><span class="p">;</span>
  <span class="k">repeated</span> <span class="kt">string</span> <span class="na">dependencies</span> <span class="o">=</span> <span class="mi">8</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">message</span> <span class="nc">TaskResult</span> <span class="p">{</span>
  <span class="kt">string</span> <span class="na">task_id</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">agent_id</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="n">TaskStatus</span> <span class="na">status</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="n">google.protobuf.Timestamp</span> <span class="na">completion_time</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="kt">int64</span> <span class="na">execution_time_ms</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
  <span class="n">google.protobuf.Any</span> <span class="na">result_data</span> <span class="o">=</span> <span class="mi">6</span><span class="p">;</span>
  <span class="n">ErrorDetails</span> <span class="na">error</span> <span class="o">=</span> <span class="mi">7</span><span class="p">;</span>
  <span class="n">TaskMetrics</span> <span class="na">metrics</span> <span class="o">=</span> <span class="mi">8</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">message</span> <span class="nc">AgentEvent</span> <span class="p">{</span>
  <span class="kt">string</span> <span class="na">event_id</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">agent_id</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="n">EventType</span> <span class="na">event_type</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="n">google.protobuf.Timestamp</span> <span class="na">timestamp</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="n">google.protobuf.Any</span> <span class="na">event_data</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">correlation_id</span> <span class="o">=</span> <span class="mi">6</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// Enums</span>
<span class="kd">enum</span> <span class="n">CommandType</span> <span class="p">{</span>
  <span class="na">COMMAND_TYPE_UNSPECIFIED</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
  <span class="na">EXECUTE</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="na">STOP</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="na">PAUSE</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="na">RESUME</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="na">CONFIGURE</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
  <span class="na">SHUTDOWN</span> <span class="o">=</span> <span class="mi">6</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">enum</span> <span class="n">AgentState</span> <span class="p">{</span>
  <span class="na">AGENT_STATE_UNSPECIFIED</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
  <span class="na">IDLE</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="na">BUSY</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="na">ERROR</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="na">OFFLINE</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="na">STARTING</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
  <span class="na">STOPPING</span> <span class="o">=</span> <span class="mi">6</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">enum</span> <span class="n">TaskType</span> <span class="p">{</span>
  <span class="na">TASK_TYPE_UNSPECIFIED</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
  <span class="na">ANALYSIS</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="na">SYNTHESIS</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="na">EXECUTION</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="na">VALIDATION</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="na">MONITORING</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">enum</span> <span class="n">TaskStatus</span> <span class="p">{</span>
  <span class="na">TASK_STATUS_UNSPECIFIED</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
  <span class="na">PENDING</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="na">RUNNING</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="na">COMPLETED</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="na">FAILED</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="na">CANCELLED</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
  <span class="na">TIMEOUT</span> <span class="o">=</span> <span class="mi">6</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">enum</span> <span class="n">ResponseStatus</span> <span class="p">{</span>
  <span class="na">RESPONSE_STATUS_UNSPECIFIED</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
  <span class="na">SUCCESS</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="na">ERROR</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="na">TIMEOUT</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="na">REJECTED</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">enum</span> <span class="n">EventType</span> <span class="p">{</span>
  <span class="na">EVENT_TYPE_UNSPECIFIED</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
  <span class="na">STARTUP</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="na">SHUTDOWN</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="na">TASK_START</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="na">TASK_END</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="na">ERROR_OCCURRED</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
  <span class="na">CAPABILITY_CHANGE</span> <span class="o">=</span> <span class="mi">6</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// Nested message types</span>
<span class="kd">message</span> <span class="nc">RequestMetadata</span> <span class="p">{</span>
  <span class="kt">string</span> <span class="na">source</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">trace_id</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">user_id</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">session_id</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="n">map</span><span class="o">&lt;</span><span class="kt">string</span><span class="p">,</span> <span class="kt">string</span><span class="err">&gt;</span> <span class="na">custom_headers</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">message</span> <span class="nc">ResourceUsage</span> <span class="p">{</span>
  <span class="kt">float</span> <span class="na">cpu_percent</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="kt">int64</span> <span class="na">memory_mb</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="kt">int64</span> <span class="na">disk_mb</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="kt">int32</span> <span class="na">network_connections</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">message</span> <span class="nc">TaskRequirements</span> <span class="p">{</span>
  <span class="k">repeated</span> <span class="kt">string</span> <span class="na">capabilities</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="n">ResourceRequirements</span> <span class="na">resources</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="n">SecurityRequirements</span> <span class="na">security</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">message</span> <span class="nc">ResourceRequirements</span> <span class="p">{</span>
  <span class="kt">int32</span> <span class="na">cpu_cores</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="kt">int64</span> <span class="na">memory_mb</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="kt">int64</span> <span class="na">disk_mb</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="kt">int32</span> <span class="na">network_bandwidth_mbps</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">message</span> <span class="nc">SecurityRequirements</span> <span class="p">{</span>
  <span class="kt">string</span> <span class="na">security_level</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="k">repeated</span> <span class="kt">string</span> <span class="na">required_permissions</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="kt">bool</span> <span class="na">requires_encryption</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">message</span> <span class="nc">ErrorDetails</span> <span class="p">{</span>
  <span class="kt">string</span> <span class="na">error_code</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">error_message</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">stack_trace</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="kt">int32</span> <span class="na">retry_count</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="k">repeated</span> <span class="kt">string</span> <span class="na">error_tags</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">message</span> <span class="nc">TaskMetrics</span> <span class="p">{</span>
  <span class="kt">float</span> <span class="na">cpu_usage_percent</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="kt">int64</span> <span class="na">memory_peak_mb</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="kt">int64</span> <span class="na">io_operations</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="kt">int64</span> <span class="na">network_bytes_sent</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="kt">int64</span> <span class="na">network_bytes_received</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">message</span> <span class="nc">HealthCheckRequest</span> <span class="p">{</span>
  <span class="kt">string</span> <span class="kd">service</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">message</span> <span class="nc">HealthCheckResponse</span> <span class="p">{</span>
  <span class="kd">enum</span> <span class="n">ServingStatus</span> <span class="p">{</span>
    <span class="na">UNKNOWN</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
    <span class="na">SERVING</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
    <span class="na">NOT_SERVING</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
    <span class="na">SERVICE_UNKNOWN</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="p">}</span>
  <span class="n">ServingStatus</span> <span class="na">status</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div></div>

<h3 id="132-grpc-server-configuration">13.2 gRPC Server Configuration</h3>

<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">GRPC_SERVER_CONFIG</span><span class="pi">:</span>
  <span class="na">address</span><span class="pi">:</span> <span class="s2">"</span><span class="s">0.0.0.0:50051"</span>
  <span class="na">max_connections</span><span class="pi">:</span> <span class="m">1000</span>
  <span class="na">max_message_size</span><span class="pi">:</span> <span class="m">16777216</span>  <span class="c1"># 16MB</span>
  <span class="na">max_frame_size</span><span class="pi">:</span> <span class="m">2097152</span>     <span class="c1"># 2MB</span>
  <span class="na">keepalive</span><span class="pi">:</span>
    <span class="na">time</span><span class="pi">:</span> <span class="m">60</span>                  <span class="c1"># seconds</span>
    <span class="na">timeout</span><span class="pi">:</span> <span class="m">5</span>                <span class="c1"># seconds</span>
    <span class="na">permit_without_stream</span><span class="pi">:</span> <span class="kc">true</span>
  <span class="na">tls</span><span class="pi">:</span>
    <span class="na">enabled</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">cert_file</span><span class="pi">:</span> <span class="s2">"</span><span class="s">certs/server.crt"</span>
    <span class="na">key_file</span><span class="pi">:</span> <span class="s2">"</span><span class="s">certs/server.key"</span>
    <span class="na">ca_file</span><span class="pi">:</span> <span class="s2">"</span><span class="s">certs/ca.crt"</span>
    <span class="na">client_auth</span><span class="pi">:</span> <span class="s">require_and_verify</span>
  <span class="na">interceptors</span><span class="pi">:</span>
    <span class="pi">-</span> <span class="s">authentication</span>
    <span class="pi">-</span> <span class="s">authorization</span>
    <span class="pi">-</span> <span class="s">rate_limiting</span>
    <span class="pi">-</span> <span class="s">metrics</span>
    <span class="pi">-</span> <span class="s">tracing</span>
  <span class="na">reflection</span><span class="pi">:</span> <span class="kc">true</span>
  <span class="na">health_check</span><span class="pi">:</span> <span class="kc">true</span>
</code></pre></div></div>

<h2 id="14-http-api-specifications">14. HTTP API Specifications</h2>

<h3 id="141-openapi-30-specification">14.1 OpenAPI 3.0 Specification</h3>

<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">openapi</span><span class="pi">:</span> <span class="s">3.0.3</span>
<span class="na">info</span><span class="pi">:</span>
  <span class="na">title</span><span class="pi">:</span> <span class="s">Mister Smith Agent Framework API</span>
  <span class="na">description</span><span class="pi">:</span> <span class="s">RESTful API for agent communication and management</span>
  <span class="na">version</span><span class="pi">:</span> <span class="s">1.0.0</span>
  <span class="na">contact</span><span class="pi">:</span>
    <span class="na">name</span><span class="pi">:</span> <span class="s">Mister Smith Framework</span>
    <span class="na">url</span><span class="pi">:</span> <span class="s">https://github.com/mister-smith/framework</span>
  <span class="na">license</span><span class="pi">:</span>
    <span class="na">name</span><span class="pi">:</span> <span class="s">Apache </span><span class="m">2.0</span>
    <span class="na">url</span><span class="pi">:</span> <span class="s">https://www.apache.org/licenses/LICENSE-2.0.html</span>

<span class="na">servers</span><span class="pi">:</span>
  <span class="pi">-</span> <span class="na">url</span><span class="pi">:</span> <span class="s">https://api.mister-smith.dev/v1</span>
    <span class="na">description</span><span class="pi">:</span> <span class="s">Production server</span>
  <span class="pi">-</span> <span class="na">url</span><span class="pi">:</span> <span class="s">https://staging-api.mister-smith.dev/v1</span>
    <span class="na">description</span><span class="pi">:</span> <span class="s">Staging server</span>
  <span class="pi">-</span> <span class="na">url</span><span class="pi">:</span> <span class="s">http://localhost:8080/v1</span>
    <span class="na">description</span><span class="pi">:</span> <span class="s">Development server</span>

<span class="na">security</span><span class="pi">:</span>
  <span class="pi">-</span> <span class="na">bearerAuth</span><span class="pi">:</span> <span class="pi">[]</span>
  <span class="pi">-</span> <span class="na">apiKeyAuth</span><span class="pi">:</span> <span class="pi">[]</span>

<span class="na">paths</span><span class="pi">:</span>
  <span class="na">/agents</span><span class="pi">:</span>
    <span class="na">get</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s">List all agents</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Retrieve a paginated list of all registered agents</span>
      <span class="na">operationId</span><span class="pi">:</span> <span class="s">listAgents</span>
      <span class="na">parameters</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">page</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">query</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">integer</span>
            <span class="na">minimum</span><span class="pi">:</span> <span class="m">1</span>
            <span class="na">default</span><span class="pi">:</span> <span class="m">1</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">limit</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">query</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">integer</span>
            <span class="na">minimum</span><span class="pi">:</span> <span class="m">1</span>
            <span class="na">maximum</span><span class="pi">:</span> <span class="m">100</span>
            <span class="na">default</span><span class="pi">:</span> <span class="m">20</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">status</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">query</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
            <span class="na">enum</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">idle</span><span class="pi">,</span> <span class="nv">busy</span><span class="pi">,</span> <span class="nv">error</span><span class="pi">,</span> <span class="nv">offline</span><span class="pi">,</span> <span class="nv">starting</span><span class="pi">,</span> <span class="nv">stopping</span><span class="pi">]</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">capability</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">query</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
      <span class="na">responses</span><span class="pi">:</span>
        <span class="s1">'</span><span class="s">200'</span><span class="err">:</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s">Successful response</span>
          <span class="na">content</span><span class="pi">:</span>
            <span class="na">application/json</span><span class="pi">:</span>
              <span class="na">schema</span><span class="pi">:</span>
                <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/AgentListResponse'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">400'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/BadRequest'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">401'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/Unauthorized'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">500'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/InternalServerError'</span>

    <span class="na">post</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s">Register a new agent</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Register a new agent with the framework</span>
      <span class="na">operationId</span><span class="pi">:</span> <span class="s">registerAgent</span>
      <span class="na">requestBody</span><span class="pi">:</span>
        <span class="na">required</span><span class="pi">:</span> <span class="kc">true</span>
        <span class="na">content</span><span class="pi">:</span>
          <span class="na">application/json</span><span class="pi">:</span>
            <span class="na">schema</span><span class="pi">:</span>
              <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/AgentRegistration'</span>
      <span class="na">responses</span><span class="pi">:</span>
        <span class="s1">'</span><span class="s">201'</span><span class="err">:</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s">Agent registered successfully</span>
          <span class="na">content</span><span class="pi">:</span>
            <span class="na">application/json</span><span class="pi">:</span>
              <span class="na">schema</span><span class="pi">:</span>
                <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/AgentRegistrationResponse'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">400'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/BadRequest'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">409'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/Conflict'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">500'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/InternalServerError'</span>

  <span class="s">/agents/{agentId}</span><span class="err">:</span>
    <span class="na">get</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s">Get agent details</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Retrieve detailed information about a specific agent</span>
      <span class="na">operationId</span><span class="pi">:</span> <span class="s">getAgent</span>
      <span class="na">parameters</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">agentId</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">path</span>
          <span class="na">required</span><span class="pi">:</span> <span class="kc">true</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
            <span class="na">pattern</span><span class="pi">:</span> <span class="s1">'</span><span class="s">^[a-zA-Z0-9_-]+$'</span>
      <span class="na">responses</span><span class="pi">:</span>
        <span class="s1">'</span><span class="s">200'</span><span class="err">:</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s">Successful response</span>
          <span class="na">content</span><span class="pi">:</span>
            <span class="na">application/json</span><span class="pi">:</span>
              <span class="na">schema</span><span class="pi">:</span>
                <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/Agent'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">404'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/NotFound'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">500'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/InternalServerError'</span>

    <span class="na">put</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s">Update agent configuration</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Update the configuration of an existing agent</span>
      <span class="na">operationId</span><span class="pi">:</span> <span class="s">updateAgent</span>
      <span class="na">parameters</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">agentId</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">path</span>
          <span class="na">required</span><span class="pi">:</span> <span class="kc">true</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
            <span class="na">pattern</span><span class="pi">:</span> <span class="s1">'</span><span class="s">^[a-zA-Z0-9_-]+$'</span>
      <span class="na">requestBody</span><span class="pi">:</span>
        <span class="na">required</span><span class="pi">:</span> <span class="kc">true</span>
        <span class="na">content</span><span class="pi">:</span>
          <span class="na">application/json</span><span class="pi">:</span>
            <span class="na">schema</span><span class="pi">:</span>
              <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/AgentUpdate'</span>
      <span class="na">responses</span><span class="pi">:</span>
        <span class="s1">'</span><span class="s">200'</span><span class="err">:</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s">Agent updated successfully</span>
          <span class="na">content</span><span class="pi">:</span>
            <span class="na">application/json</span><span class="pi">:</span>
              <span class="na">schema</span><span class="pi">:</span>
                <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/Agent'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">400'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/BadRequest'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">404'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/NotFound'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">500'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/InternalServerError'</span>

    <span class="na">delete</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s">Deregister agent</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Remove an agent from the framework</span>
      <span class="na">operationId</span><span class="pi">:</span> <span class="s">deregisterAgent</span>
      <span class="na">parameters</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">agentId</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">path</span>
          <span class="na">required</span><span class="pi">:</span> <span class="kc">true</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
            <span class="na">pattern</span><span class="pi">:</span> <span class="s1">'</span><span class="s">^[a-zA-Z0-9_-]+$'</span>
      <span class="na">responses</span><span class="pi">:</span>
        <span class="s1">'</span><span class="s">204'</span><span class="err">:</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s">Agent deregistered successfully</span>
        <span class="s1">'</span><span class="s">404'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/NotFound'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">500'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/InternalServerError'</span>

  <span class="s">/agents/{agentId}/commands</span><span class="err">:</span>
    <span class="na">post</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s">Send command to agent</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Send a command to a specific agent</span>
      <span class="na">operationId</span><span class="pi">:</span> <span class="s">sendCommand</span>
      <span class="na">parameters</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">agentId</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">path</span>
          <span class="na">required</span><span class="pi">:</span> <span class="kc">true</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
            <span class="na">pattern</span><span class="pi">:</span> <span class="s1">'</span><span class="s">^[a-zA-Z0-9_-]+$'</span>
      <span class="na">requestBody</span><span class="pi">:</span>
        <span class="na">required</span><span class="pi">:</span> <span class="kc">true</span>
        <span class="na">content</span><span class="pi">:</span>
          <span class="na">application/json</span><span class="pi">:</span>
            <span class="na">schema</span><span class="pi">:</span>
              <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/Command'</span>
      <span class="na">responses</span><span class="pi">:</span>
        <span class="s1">'</span><span class="s">202'</span><span class="err">:</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s">Command accepted</span>
          <span class="na">content</span><span class="pi">:</span>
            <span class="na">application/json</span><span class="pi">:</span>
              <span class="na">schema</span><span class="pi">:</span>
                <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/CommandResponse'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">400'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/BadRequest'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">404'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/NotFound'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">500'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/InternalServerError'</span>

  <span class="s">/agents/{agentId}/status</span><span class="err">:</span>
    <span class="na">get</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s">Get agent status</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Retrieve the current status of an agent</span>
      <span class="na">operationId</span><span class="pi">:</span> <span class="s">getAgentStatus</span>
      <span class="na">parameters</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">agentId</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">path</span>
          <span class="na">required</span><span class="pi">:</span> <span class="kc">true</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
            <span class="na">pattern</span><span class="pi">:</span> <span class="s1">'</span><span class="s">^[a-zA-Z0-9_-]+$'</span>
      <span class="na">responses</span><span class="pi">:</span>
        <span class="s1">'</span><span class="s">200'</span><span class="err">:</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s">Successful response</span>
          <span class="na">content</span><span class="pi">:</span>
            <span class="na">application/json</span><span class="pi">:</span>
              <span class="na">schema</span><span class="pi">:</span>
                <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/AgentStatus'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">404'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/NotFound'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">500'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/InternalServerError'</span>

  <span class="na">/tasks</span><span class="pi">:</span>
    <span class="na">get</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s">List tasks</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Retrieve a paginated list of tasks</span>
      <span class="na">operationId</span><span class="pi">:</span> <span class="s">listTasks</span>
      <span class="na">parameters</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">page</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">query</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">integer</span>
            <span class="na">minimum</span><span class="pi">:</span> <span class="m">1</span>
            <span class="na">default</span><span class="pi">:</span> <span class="m">1</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">limit</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">query</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">integer</span>
            <span class="na">minimum</span><span class="pi">:</span> <span class="m">1</span>
            <span class="na">maximum</span><span class="pi">:</span> <span class="m">100</span>
            <span class="na">default</span><span class="pi">:</span> <span class="m">20</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">status</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">query</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
            <span class="na">enum</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">pending</span><span class="pi">,</span> <span class="nv">running</span><span class="pi">,</span> <span class="nv">completed</span><span class="pi">,</span> <span class="nv">failed</span><span class="pi">,</span> <span class="nv">cancelled</span><span class="pi">,</span> <span class="nv">timeout</span><span class="pi">]</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">type</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">query</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
            <span class="na">enum</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">analysis</span><span class="pi">,</span> <span class="nv">synthesis</span><span class="pi">,</span> <span class="nv">execution</span><span class="pi">,</span> <span class="nv">validation</span><span class="pi">,</span> <span class="nv">monitoring</span><span class="pi">]</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">agent_id</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">query</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
            <span class="na">pattern</span><span class="pi">:</span> <span class="s1">'</span><span class="s">^[a-zA-Z0-9_-]+$'</span>
      <span class="na">responses</span><span class="pi">:</span>
        <span class="s1">'</span><span class="s">200'</span><span class="err">:</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s">Successful response</span>
          <span class="na">content</span><span class="pi">:</span>
            <span class="na">application/json</span><span class="pi">:</span>
              <span class="na">schema</span><span class="pi">:</span>
                <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/TaskListResponse'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">400'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/BadRequest'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">500'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/InternalServerError'</span>

    <span class="na">post</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s">Create a new task</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Create and assign a new task</span>
      <span class="na">operationId</span><span class="pi">:</span> <span class="s">createTask</span>
      <span class="na">requestBody</span><span class="pi">:</span>
        <span class="na">required</span><span class="pi">:</span> <span class="kc">true</span>
        <span class="na">content</span><span class="pi">:</span>
          <span class="na">application/json</span><span class="pi">:</span>
            <span class="na">schema</span><span class="pi">:</span>
              <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/TaskCreation'</span>
      <span class="na">responses</span><span class="pi">:</span>
        <span class="s1">'</span><span class="s">201'</span><span class="err">:</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s">Task created successfully</span>
          <span class="na">content</span><span class="pi">:</span>
            <span class="na">application/json</span><span class="pi">:</span>
              <span class="na">schema</span><span class="pi">:</span>
                <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/Task'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">400'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/BadRequest'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">500'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/InternalServerError'</span>

  <span class="s">/tasks/{taskId}</span><span class="err">:</span>
    <span class="na">get</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s">Get task details</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Retrieve detailed information about a specific task</span>
      <span class="na">operationId</span><span class="pi">:</span> <span class="s">getTask</span>
      <span class="na">parameters</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">taskId</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">path</span>
          <span class="na">required</span><span class="pi">:</span> <span class="kc">true</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
            <span class="na">format</span><span class="pi">:</span> <span class="s">uuid</span>
      <span class="na">responses</span><span class="pi">:</span>
        <span class="s1">'</span><span class="s">200'</span><span class="err">:</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s">Successful response</span>
          <span class="na">content</span><span class="pi">:</span>
            <span class="na">application/json</span><span class="pi">:</span>
              <span class="na">schema</span><span class="pi">:</span>
                <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/Task'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">404'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/NotFound'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">500'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/InternalServerError'</span>

    <span class="na">delete</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s">Cancel task</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Cancel a pending or running task</span>
      <span class="na">operationId</span><span class="pi">:</span> <span class="s">cancelTask</span>
      <span class="na">parameters</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">taskId</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">path</span>
          <span class="na">required</span><span class="pi">:</span> <span class="kc">true</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
            <span class="na">format</span><span class="pi">:</span> <span class="s">uuid</span>
      <span class="na">responses</span><span class="pi">:</span>
        <span class="s1">'</span><span class="s">200'</span><span class="err">:</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s">Task cancelled successfully</span>
          <span class="na">content</span><span class="pi">:</span>
            <span class="na">application/json</span><span class="pi">:</span>
              <span class="na">schema</span><span class="pi">:</span>
                <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/TaskCancellation'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">404'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/NotFound'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">409'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/Conflict'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">500'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/InternalServerError'</span>

  <span class="na">/ws</span><span class="pi">:</span>
    <span class="na">get</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s">WebSocket endpoint</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">WebSocket connection for real-time communication</span>
      <span class="na">operationId</span><span class="pi">:</span> <span class="s">websocketConnect</span>
      <span class="na">parameters</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">protocol</span>
          <span class="na">in</span><span class="pi">:</span> <span class="s">query</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
            <span class="na">enum</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">events</span><span class="pi">,</span> <span class="nv">commands</span><span class="pi">,</span> <span class="nv">status</span><span class="pi">]</span>
            <span class="na">default</span><span class="pi">:</span> <span class="s">events</span>
      <span class="na">responses</span><span class="pi">:</span>
        <span class="s1">'</span><span class="s">101'</span><span class="err">:</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s">Switching protocols to WebSocket</span>
        <span class="s1">'</span><span class="s">400'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/BadRequest'</span>
      <span class="err">  </span><span class="s1">'</span><span class="s">401'</span><span class="err">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/responses/Unauthorized'</span>

<span class="na">components</span><span class="pi">:</span>
  <span class="na">securitySchemes</span><span class="pi">:</span>
    <span class="na">bearerAuth</span><span class="pi">:</span>
      <span class="na">type</span><span class="pi">:</span> <span class="s">http</span>
      <span class="na">scheme</span><span class="pi">:</span> <span class="s">bearer</span>
      <span class="na">bearerFormat</span><span class="pi">:</span> <span class="s">JWT</span>
    <span class="na">apiKeyAuth</span><span class="pi">:</span>
      <span class="na">type</span><span class="pi">:</span> <span class="s">apiKey</span>
      <span class="na">in</span><span class="pi">:</span> <span class="s">header</span>
      <span class="na">name</span><span class="pi">:</span> <span class="s">X-API-Key</span>

  <span class="na">schemas</span><span class="pi">:</span>
    <span class="na">Agent</span><span class="pi">:</span>
      <span class="na">type</span><span class="pi">:</span> <span class="s">object</span>
      <span class="na">required</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">agent_id</span><span class="pi">,</span> <span class="nv">status</span><span class="pi">,</span> <span class="nv">capabilities</span><span class="pi">,</span> <span class="nv">registered_at</span><span class="pi">]</span>
      <span class="na">properties</span><span class="pi">:</span>
        <span class="na">agent_id</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">pattern</span><span class="pi">:</span> <span class="s1">'</span><span class="s">^[a-zA-Z0-9_-]+$'</span>
          <span class="na">example</span><span class="pi">:</span> <span class="s2">"</span><span class="s">analyzer-001"</span>
        <span class="na">status</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">enum</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">idle</span><span class="pi">,</span> <span class="nv">busy</span><span class="pi">,</span> <span class="nv">error</span><span class="pi">,</span> <span class="nv">offline</span><span class="pi">,</span> <span class="nv">starting</span><span class="pi">,</span> <span class="nv">stopping</span><span class="pi">]</span>
          <span class="na">example</span><span class="pi">:</span> <span class="s2">"</span><span class="s">idle"</span>
        <span class="na">capabilities</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">array</span>
          <span class="na">items</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">example</span><span class="pi">:</span> <span class="pi">[</span><span class="s2">"</span><span class="s">text-analysis"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">data-processing"</span><span class="pi">]</span>
        <span class="na">registered_at</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">format</span><span class="pi">:</span> <span class="s">date-time</span>
        <span class="na">last_heartbeat</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">format</span><span class="pi">:</span> <span class="s">date-time</span>
        <span class="na">resource_usage</span><span class="pi">:</span>
          <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/ResourceUsage'</span>
        <span class="na">current_tasks</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">integer</span>
          <span class="na">minimum</span><span class="pi">:</span> <span class="m">0</span>
          <span class="na">example</span><span class="pi">:</span> <span class="m">2</span>
        <span class="na">max_tasks</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">integer</span>
          <span class="na">minimum</span><span class="pi">:</span> <span class="m">1</span>
          <span class="na">example</span><span class="pi">:</span> <span class="m">10</span>

    <span class="na">AgentRegistration</span><span class="pi">:</span>
      <span class="na">type</span><span class="pi">:</span> <span class="s">object</span>
      <span class="na">required</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">agent_id</span><span class="pi">,</span> <span class="nv">capabilities</span><span class="pi">]</span>
      <span class="na">properties</span><span class="pi">:</span>
        <span class="na">agent_id</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">pattern</span><span class="pi">:</span> <span class="s1">'</span><span class="s">^[a-zA-Z0-9_-]+$'</span>
        <span class="na">capabilities</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">array</span>
          <span class="na">items</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">minItems</span><span class="pi">:</span> <span class="m">1</span>
        <span class="na">max_tasks</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">integer</span>
          <span class="na">minimum</span><span class="pi">:</span> <span class="m">1</span>
          <span class="na">default</span><span class="pi">:</span> <span class="m">5</span>
        <span class="na">metadata</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">object</span>
          <span class="na">additionalProperties</span><span class="pi">:</span> <span class="kc">true</span>

    <span class="na">Task</span><span class="pi">:</span>
      <span class="na">type</span><span class="pi">:</span> <span class="s">object</span>
      <span class="na">required</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">task_id</span><span class="pi">,</span> <span class="nv">type</span><span class="pi">,</span> <span class="nv">status</span><span class="pi">,</span> <span class="nv">created_at</span><span class="pi">]</span>
      <span class="na">properties</span><span class="pi">:</span>
        <span class="na">task_id</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">format</span><span class="pi">:</span> <span class="s">uuid</span>
        <span class="na">type</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">enum</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">analysis</span><span class="pi">,</span> <span class="nv">synthesis</span><span class="pi">,</span> <span class="nv">execution</span><span class="pi">,</span> <span class="nv">validation</span><span class="pi">,</span> <span class="nv">monitoring</span><span class="pi">]</span>
        <span class="na">status</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">enum</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">pending</span><span class="pi">,</span> <span class="nv">running</span><span class="pi">,</span> <span class="nv">completed</span><span class="pi">,</span> <span class="nv">failed</span><span class="pi">,</span> <span class="nv">cancelled</span><span class="pi">,</span> <span class="nv">timeout</span><span class="pi">]</span>
        <span class="na">assigned_agent</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">pattern</span><span class="pi">:</span> <span class="s1">'</span><span class="s">^[a-zA-Z0-9_-]+$'</span>
        <span class="na">created_at</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">format</span><span class="pi">:</span> <span class="s">date-time</span>
        <span class="na">deadline</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">format</span><span class="pi">:</span> <span class="s">date-time</span>
        <span class="na">priority</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">integer</span>
          <span class="na">minimum</span><span class="pi">:</span> <span class="m">1</span>
          <span class="na">maximum</span><span class="pi">:</span> <span class="m">10</span>
        <span class="na">task_data</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">object</span>
          <span class="na">additionalProperties</span><span class="pi">:</span> <span class="kc">true</span>
        <span class="na">result</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">object</span>
          <span class="na">additionalProperties</span><span class="pi">:</span> <span class="kc">true</span>

    <span class="na">Command</span><span class="pi">:</span>
      <span class="na">type</span><span class="pi">:</span> <span class="s">object</span>
      <span class="na">required</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">command_type</span><span class="pi">]</span>
      <span class="na">properties</span><span class="pi">:</span>
        <span class="na">command_type</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">enum</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">execute</span><span class="pi">,</span> <span class="nv">stop</span><span class="pi">,</span> <span class="nv">pause</span><span class="pi">,</span> <span class="nv">resume</span><span class="pi">,</span> <span class="nv">configure</span><span class="pi">,</span> <span class="nv">shutdown</span><span class="pi">]</span>
        <span class="na">payload</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">object</span>
          <span class="na">additionalProperties</span><span class="pi">:</span> <span class="kc">true</span>
        <span class="na">priority</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">integer</span>
          <span class="na">minimum</span><span class="pi">:</span> <span class="m">1</span>
          <span class="na">maximum</span><span class="pi">:</span> <span class="m">10</span>
          <span class="na">default</span><span class="pi">:</span> <span class="m">5</span>
        <span class="na">timeout_ms</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">integer</span>
          <span class="na">minimum</span><span class="pi">:</span> <span class="m">1000</span>
          <span class="na">default</span><span class="pi">:</span> <span class="m">30000</span>

    <span class="na">ResourceUsage</span><span class="pi">:</span>
      <span class="na">type</span><span class="pi">:</span> <span class="s">object</span>
      <span class="na">properties</span><span class="pi">:</span>
        <span class="na">cpu_percent</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">number</span>
          <span class="na">minimum</span><span class="pi">:</span> <span class="m">0</span>
          <span class="na">maximum</span><span class="pi">:</span> <span class="m">100</span>
        <span class="na">memory_mb</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">number</span>
          <span class="na">minimum</span><span class="pi">:</span> <span class="m">0</span>
        <span class="na">disk_mb</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">number</span>
          <span class="na">minimum</span><span class="pi">:</span> <span class="m">0</span>

    <span class="na">Error</span><span class="pi">:</span>
      <span class="na">type</span><span class="pi">:</span> <span class="s">object</span>
      <span class="na">required</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">error_code</span><span class="pi">,</span> <span class="nv">message</span><span class="pi">]</span>
      <span class="na">properties</span><span class="pi">:</span>
        <span class="na">error_code</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">example</span><span class="pi">:</span> <span class="s2">"</span><span class="s">INVALID_REQUEST"</span>
        <span class="na">message</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">example</span><span class="pi">:</span> <span class="s2">"</span><span class="s">The</span><span class="nv"> </span><span class="s">request</span><span class="nv"> </span><span class="s">payload</span><span class="nv"> </span><span class="s">is</span><span class="nv"> </span><span class="s">invalid"</span>
        <span class="na">details</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">array</span>
          <span class="na">items</span><span class="pi">:</span>
            <span class="na">type</span><span class="pi">:</span> <span class="s">object</span>
            <span class="na">properties</span><span class="pi">:</span>
              <span class="na">field</span><span class="pi">:</span>
                <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
              <span class="na">issue</span><span class="pi">:</span>
                <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
        <span class="na">trace_id</span><span class="pi">:</span>
          <span class="na">type</span><span class="pi">:</span> <span class="s">string</span>
          <span class="na">format</span><span class="pi">:</span> <span class="s">uuid</span>

  <span class="na">responses</span><span class="pi">:</span>
    <span class="na">BadRequest</span><span class="pi">:</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Bad request</span>
      <span class="na">content</span><span class="pi">:</span>
        <span class="na">application/json</span><span class="pi">:</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/Error'</span>
    <span class="na">Unauthorized</span><span class="pi">:</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Unauthorized</span>
      <span class="na">content</span><span class="pi">:</span>
        <span class="na">application/json</span><span class="pi">:</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/Error'</span>
    <span class="na">NotFound</span><span class="pi">:</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Resource not found</span>
      <span class="na">content</span><span class="pi">:</span>
        <span class="na">application/json</span><span class="pi">:</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/Error'</span>
    <span class="na">Conflict</span><span class="pi">:</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Resource conflict</span>
      <span class="na">content</span><span class="pi">:</span>
        <span class="na">application/json</span><span class="pi">:</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/Error'</span>
    <span class="na">InternalServerError</span><span class="pi">:</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s">Internal server error</span>
      <span class="na">content</span><span class="pi">:</span>
        <span class="na">application/json</span><span class="pi">:</span>
          <span class="na">schema</span><span class="pi">:</span>
            <span class="na">$ref</span><span class="pi">:</span> <span class="s1">'</span><span class="s">#/components/schemas/Error'</span>
</code></pre></div></div>

<h3 id="142-websocket-protocol-specification">14.2 WebSocket Protocol Specification</h3>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="err">WEBSOCKET_PROTOCOL:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"connection"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"url"</span><span class="p">:</span><span class="w"> </span><span class="s2">"wss://api.mister-smith.dev/v1/ws"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"protocols"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"events"</span><span class="p">,</span><span class="w"> </span><span class="s2">"commands"</span><span class="p">,</span><span class="w"> </span><span class="s2">"status"</span><span class="p">],</span><span class="w">
    </span><span class="nl">"authentication"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"bearer_token"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"header"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Authorization"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"heartbeat_interval"</span><span class="p">:</span><span class="w"> </span><span class="mi">30</span><span class="p">,</span><span class="w">
    </span><span class="nl">"reconnect_policy"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"initial_delay"</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span><span class="p">,</span><span class="w">
      </span><span class="nl">"max_delay"</span><span class="p">:</span><span class="w"> </span><span class="mi">30000</span><span class="p">,</span><span class="w">
      </span><span class="nl">"backoff_factor"</span><span class="p">:</span><span class="w"> </span><span class="mf">2.0</span><span class="p">,</span><span class="w">
      </span><span class="nl">"max_attempts"</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"message_format"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"type"</span><span class="p">,</span><span class="w"> </span><span class="s2">"id"</span><span class="p">,</span><span class="w"> </span><span class="s2">"timestamp"</span><span class="p">],</span><span class="w">
    </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"enum"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"event"</span><span class="p">,</span><span class="w"> </span><span class="s2">"command"</span><span class="p">,</span><span class="w"> </span><span class="s2">"response"</span><span class="p">,</span><span class="w"> </span><span class="s2">"heartbeat"</span><span class="p">,</span><span class="w"> </span><span class="s2">"error"</span><span class="p">]</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="nl">"id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="nl">"payload"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="nl">"correlation_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"event_types"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"agent_status_changed"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"payload"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"agent_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"old_status"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"new_status"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"task_assigned"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"payload"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"task_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"agent_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"task_type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"priority"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"task_completed"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"payload"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"task_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"agent_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"status"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"execution_time_ms"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"system_alert"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"payload"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"severity"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"component"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"details"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h2 id="15-error-response-standards">15. Error Response Standards</h2>

<h3 id="151-standardized-error-codes">15.1 Standardized Error Codes</h3>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="err">ERROR_CODES:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"VALIDATION"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"INVALID_REQUEST"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E1001"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">400</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Request validation failed"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"MISSING_REQUIRED_FIELD"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E1002"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">400</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Required field is missing"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"INVALID_FIELD_FORMAT"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E1003"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">400</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Field format is invalid"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"AUTHENTICATION"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"INVALID_TOKEN"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E2001"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">401</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Authentication token is invalid"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"TOKEN_EXPIRED"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E2002"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">401</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Authentication token has expired"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"INSUFFICIENT_PERMISSIONS"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E2003"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">403</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Insufficient permissions for this operation"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"RESOURCE"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"AGENT_NOT_FOUND"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E3001"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">404</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Agent not found"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"TASK_NOT_FOUND"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E3002"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">404</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Task not found"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"AGENT_ALREADY_EXISTS"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E3003"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">409</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Agent already exists"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"SYSTEM"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"INTERNAL_ERROR"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E5001"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">500</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Internal server error"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"SERVICE_UNAVAILABLE"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E5002"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">503</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Service temporarily unavailable"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"TIMEOUT"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E5003"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">504</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Request timeout"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"AGENT"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"AGENT_OFFLINE"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E4001"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">503</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Agent is offline"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"AGENT_BUSY"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E4002"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">429</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Agent is at capacity"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"CAPABILITY_NOT_SUPPORTED"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"E4003"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"http_status"</span><span class="p">:</span><span class="w"> </span><span class="mi">400</span><span class="p">,</span><span class="w">
      </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Agent does not support required capability"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"retryable"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h3 id="152-error-response-format">15.2 Error Response Format</h3>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="err">ERROR_RESPONSE_SCHEMA:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"http://json-schema.org/draft-07/schema#"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"error_code"</span><span class="p">,</span><span class="w"> </span><span class="s2">"message"</span><span class="p">,</span><span class="w"> </span><span class="s2">"timestamp"</span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"error_code"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^E</span><span class="se">\\</span><span class="s2">d{4}$"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Standardized error code"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Human-readable error message"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"When the error occurred"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"trace_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Unique identifier for tracing this error"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"details"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"array"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"items"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"field"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Field that caused the error"</span><span class="w">
          </span><span class="p">},</span><span class="w">
          </span><span class="nl">"issue"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Specific issue with the field"</span><span class="w">
          </span><span class="p">},</span><span class="w">
          </span><span class="nl">"code"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Field-specific error code"</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"retry_after"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"integer"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Seconds to wait before retrying (for retryable errors)"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"documentation_url"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uri"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Link to relevant documentation"</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h3 id="153-retry-policies">15.3 Retry Policies</h3>

<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">RETRY_POLICIES</span><span class="pi">:</span>
  <span class="na">exponential_backoff</span><span class="pi">:</span>
    <span class="na">initial_delay</span><span class="pi">:</span> <span class="m">1000</span>      <span class="c1"># milliseconds</span>
    <span class="na">max_delay</span><span class="pi">:</span> <span class="m">30000</span>         <span class="c1"># milliseconds</span>
    <span class="na">backoff_factor</span><span class="pi">:</span> <span class="m">2.0</span>
    <span class="na">jitter</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">max_attempts</span><span class="pi">:</span> <span class="m">5</span>

  <span class="na">linear_backoff</span><span class="pi">:</span>
    <span class="na">initial_delay</span><span class="pi">:</span> <span class="m">1000</span>      <span class="c1"># milliseconds</span>
    <span class="na">delay_increment</span><span class="pi">:</span> <span class="m">1000</span>    <span class="c1"># milliseconds</span>
    <span class="na">max_delay</span><span class="pi">:</span> <span class="m">10000</span>         <span class="c1"># milliseconds</span>
    <span class="na">max_attempts</span><span class="pi">:</span> <span class="m">3</span>

  <span class="na">immediate_retry</span><span class="pi">:</span>
    <span class="na">delay</span><span class="pi">:</span> <span class="m">0</span>
    <span class="na">max_attempts</span><span class="pi">:</span> <span class="m">2</span>

<span class="na">RETRY_CONDITIONS</span><span class="pi">:</span>
  <span class="na">retryable_errors</span><span class="pi">:</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">E2002"</span>  <span class="c1"># TOKEN_EXPIRED</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">E5001"</span>  <span class="c1"># INTERNAL_ERROR</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">E5002"</span>  <span class="c1"># SERVICE_UNAVAILABLE</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">E5003"</span>  <span class="c1"># TIMEOUT</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">E4001"</span>  <span class="c1"># AGENT_OFFLINE</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">E4002"</span>  <span class="c1"># AGENT_BUSY</span>

  <span class="na">non_retryable_errors</span><span class="pi">:</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">E1001"</span>  <span class="c1"># INVALID_REQUEST</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">E1002"</span>  <span class="c1"># MISSING_REQUIRED_FIELD</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">E2001"</span>  <span class="c1"># INVALID_TOKEN</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">E2003"</span>  <span class="c1"># INSUFFICIENT_PERMISSIONS</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">E3001"</span>  <span class="c1"># AGENT_NOT_FOUND</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">E4003"</span>  <span class="c1"># CAPABILITY_NOT_SUPPORTED</span>

<span class="na">CIRCUIT_BREAKER</span><span class="pi">:</span>
  <span class="na">failure_threshold</span><span class="pi">:</span> <span class="m">5</span>
  <span class="na">recovery_timeout</span><span class="pi">:</span> <span class="m">30000</span>    <span class="c1"># milliseconds</span>
  <span class="na">test_request_volume</span><span class="pi">:</span> <span class="m">3</span>
  <span class="na">minimum_throughput</span><span class="pi">:</span> <span class="m">10</span>
</code></pre></div></div>

<h2 id="16-connection-pool-configurations">16. Connection Pool Configurations</h2>

<h3 id="161-nats-connection-pool">16.1 NATS Connection Pool</h3>

<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">NATS_CONNECTION_POOL</span><span class="pi">:</span>
  <span class="na">servers</span><span class="pi">:</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">nats://nats-01.internal:4222"</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">nats://nats-02.internal:4222"</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">nats://nats-03.internal:4222"</span>
  
  <span class="na">connection_settings</span><span class="pi">:</span>
    <span class="na">max_connections</span><span class="pi">:</span> <span class="m">100</span>
    <span class="na">max_idle_connections</span><span class="pi">:</span> <span class="m">20</span>
    <span class="na">connection_timeout</span><span class="pi">:</span> <span class="m">10000</span>    <span class="c1"># milliseconds</span>
    <span class="na">ping_interval</span><span class="pi">:</span> <span class="m">60000</span>         <span class="c1"># milliseconds</span>
    <span class="na">max_reconnects</span><span class="pi">:</span> <span class="m">10</span>
    <span class="na">reconnect_wait</span><span class="pi">:</span> <span class="m">2000</span>         <span class="c1"># milliseconds</span>
    <span class="na">reconnect_jitter</span><span class="pi">:</span> <span class="m">1000</span>       <span class="c1"># milliseconds</span>
    <span class="na">disconnect_error_handler</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">async_error_handler</span><span class="pi">:</span> <span class="kc">true</span>

  <span class="na">subscription_settings</span><span class="pi">:</span>
    <span class="na">max_subscriptions</span><span class="pi">:</span> <span class="m">1000</span>
    <span class="na">subscription_timeout</span><span class="pi">:</span> <span class="m">5000</span>   <span class="c1"># milliseconds</span>
    <span class="na">pending_message_limit</span><span class="pi">:</span> <span class="m">1000</span>
    <span class="na">pending_byte_limit</span><span class="pi">:</span> <span class="m">10485760</span> <span class="c1"># 10MB</span>
    <span class="na">subscription_capacity</span><span class="pi">:</span> <span class="m">1000</span>

  <span class="na">tls_config</span><span class="pi">:</span>
    <span class="na">enabled</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">cert_file</span><span class="pi">:</span> <span class="s2">"</span><span class="s">certs/client.crt"</span>
    <span class="na">key_file</span><span class="pi">:</span> <span class="s2">"</span><span class="s">certs/client.key"</span>
    <span class="na">ca_file</span><span class="pi">:</span> <span class="s2">"</span><span class="s">certs/ca.crt"</span>
    <span class="na">verify_certificates</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">cipher_suites</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"</span>

  <span class="na">jetstream_config</span><span class="pi">:</span>
    <span class="na">max_ack_pending</span><span class="pi">:</span> <span class="m">1000</span>
    <span class="na">ack_wait</span><span class="pi">:</span> <span class="m">30000</span>             <span class="c1"># milliseconds</span>
    <span class="na">max_deliver</span><span class="pi">:</span> <span class="m">3</span>
    <span class="na">delivery_policy</span><span class="pi">:</span> <span class="s2">"</span><span class="s">new"</span>
    <span class="na">replay_policy</span><span class="pi">:</span> <span class="s2">"</span><span class="s">instant"</span>

  <span class="na">monitoring</span><span class="pi">:</span>
    <span class="na">stats_interval</span><span class="pi">:</span> <span class="m">60000</span>       <span class="c1"># milliseconds</span>
    <span class="na">slow_consumer_threshold</span><span class="pi">:</span> <span class="m">1000</span>
    <span class="na">enable_metrics</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">enable_tracing</span><span class="pi">:</span> <span class="kc">true</span>
</code></pre></div></div>

<h3 id="162-grpc-connection-pool">16.2 gRPC Connection Pool</h3>

<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">GRPC_CONNECTION_POOL</span><span class="pi">:</span>
  <span class="na">target_addresses</span><span class="pi">:</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">grpc-01.internal:50051"</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">grpc-02.internal:50051"</span>
    <span class="pi">-</span> <span class="s2">"</span><span class="s">grpc-03.internal:50051"</span>

  <span class="na">connection_settings</span><span class="pi">:</span>
    <span class="na">max_connections_per_target</span><span class="pi">:</span> <span class="m">10</span>
    <span class="na">max_idle_connections</span><span class="pi">:</span> <span class="m">5</span>
    <span class="na">connection_timeout</span><span class="pi">:</span> <span class="m">15000</span>    <span class="c1"># milliseconds</span>
    <span class="na">keepalive_time</span><span class="pi">:</span> <span class="m">60000</span>        <span class="c1"># milliseconds</span>
    <span class="na">keepalive_timeout</span><span class="pi">:</span> <span class="m">5000</span>      <span class="c1"># milliseconds</span>
    <span class="na">keepalive_without_stream</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">max_connection_idle</span><span class="pi">:</span> <span class="m">300000</span>  <span class="c1"># milliseconds</span>
    <span class="na">max_connection_age</span><span class="pi">:</span> <span class="m">600000</span>   <span class="c1"># milliseconds</span>

  <span class="na">call_settings</span><span class="pi">:</span>
    <span class="na">max_message_size</span><span class="pi">:</span> <span class="m">16777216</span>   <span class="c1"># 16MB</span>
    <span class="na">max_header_list_size</span><span class="pi">:</span> <span class="m">8192</span>   <span class="c1"># 8KB</span>
    <span class="na">call_timeout</span><span class="pi">:</span> <span class="m">30000</span>          <span class="c1"># milliseconds</span>
    <span class="na">retry_enabled</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">max_retry_attempts</span><span class="pi">:</span> <span class="m">3</span>
    <span class="na">initial_backoff</span><span class="pi">:</span> <span class="m">1000</span>        <span class="c1"># milliseconds</span>
    <span class="na">max_backoff</span><span class="pi">:</span> <span class="m">10000</span>           <span class="c1"># milliseconds</span>
    <span class="na">backoff_multiplier</span><span class="pi">:</span> <span class="m">2.0</span>

  <span class="na">tls_config</span><span class="pi">:</span>
    <span class="na">enabled</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">server_name_override</span><span class="pi">:</span> <span class="s2">"</span><span class="s">"</span>
    <span class="na">cert_file</span><span class="pi">:</span> <span class="s2">"</span><span class="s">certs/client.crt"</span>
    <span class="na">key_file</span><span class="pi">:</span> <span class="s2">"</span><span class="s">certs/client.key"</span>
    <span class="na">ca_file</span><span class="pi">:</span> <span class="s2">"</span><span class="s">certs/ca.crt"</span>
    <span class="na">insecure_skip_verify</span><span class="pi">:</span> <span class="kc">false</span>

  <span class="na">load_balancing</span><span class="pi">:</span>
    <span class="na">policy</span><span class="pi">:</span> <span class="s2">"</span><span class="s">round_robin"</span>       <span class="c1"># round_robin, least_request, consistent_hash</span>
    <span class="na">health_check_enabled</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">health_check_interval</span><span class="pi">:</span> <span class="m">30000</span> <span class="c1"># milliseconds</span>

  <span class="na">monitoring</span><span class="pi">:</span>
    <span class="na">enable_stats</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">stats_tags</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">service"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">method"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">status"</span>
    <span class="na">enable_tracing</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">trace_sampling_rate</span><span class="pi">:</span> <span class="m">0.1</span>
</code></pre></div></div>

<h3 id="163-http-connection-pool">16.3 HTTP Connection Pool</h3>

<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">HTTP_CONNECTION_POOL</span><span class="pi">:</span>
  <span class="na">max_connections</span><span class="pi">:</span> <span class="m">200</span>
  <span class="na">max_idle_connections</span><span class="pi">:</span> <span class="m">50</span>
  <span class="na">max_connections_per_host</span><span class="pi">:</span> <span class="m">20</span>
  <span class="na">idle_connection_timeout</span><span class="pi">:</span> <span class="m">90000</span>  <span class="c1"># milliseconds</span>
  <span class="na">connection_timeout</span><span class="pi">:</span> <span class="m">10000</span>       <span class="c1"># milliseconds</span>
  <span class="na">request_timeout</span><span class="pi">:</span> <span class="m">30000</span>          <span class="c1"># milliseconds</span>
  <span class="na">response_header_timeout</span><span class="pi">:</span> <span class="m">10000</span>  <span class="c1"># milliseconds</span>
  <span class="na">tls_handshake_timeout</span><span class="pi">:</span> <span class="m">10000</span>    <span class="c1"># milliseconds</span>
  <span class="na">expect_continue_timeout</span><span class="pi">:</span> <span class="m">1000</span>   <span class="c1"># milliseconds</span>

  <span class="na">retry_config</span><span class="pi">:</span>
    <span class="na">max_retries</span><span class="pi">:</span> <span class="m">3</span>
    <span class="na">initial_backoff</span><span class="pi">:</span> <span class="m">1000</span>         <span class="c1"># milliseconds</span>
    <span class="na">max_backoff</span><span class="pi">:</span> <span class="m">10000</span>           <span class="c1"># milliseconds</span>
    <span class="na">backoff_multiplier</span><span class="pi">:</span> <span class="m">2.0</span>
    <span class="na">retryable_status_codes</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="m">429</span>  <span class="c1"># Too Many Requests</span>
      <span class="pi">-</span> <span class="m">502</span>  <span class="c1"># Bad Gateway</span>
      <span class="pi">-</span> <span class="m">503</span>  <span class="c1"># Service Unavailable</span>
      <span class="pi">-</span> <span class="m">504</span>  <span class="c1"># Gateway Timeout</span>

  <span class="na">rate_limiting</span><span class="pi">:</span>
    <span class="na">requests_per_second</span><span class="pi">:</span> <span class="m">100</span>
    <span class="na">burst_size</span><span class="pi">:</span> <span class="m">200</span>
    <span class="na">per_host_limit</span><span class="pi">:</span> <span class="m">50</span>

  <span class="na">compression</span><span class="pi">:</span>
    <span class="na">enabled</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">algorithms</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">gzip"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">deflate"</span>
    <span class="na">min_size</span><span class="pi">:</span> <span class="m">1024</span>              <span class="c1"># bytes</span>

  <span class="na">tls_config</span><span class="pi">:</span>
    <span class="na">min_version</span><span class="pi">:</span> <span class="s2">"</span><span class="s">TLS1.2"</span>
    <span class="na">max_version</span><span class="pi">:</span> <span class="s2">"</span><span class="s">TLS1.3"</span>
    <span class="na">cipher_suites</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"</span>
    <span class="na">verify_certificates</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">ca_file</span><span class="pi">:</span> <span class="s2">"</span><span class="s">certs/ca.crt"</span>
    <span class="na">cert_file</span><span class="pi">:</span> <span class="s2">"</span><span class="s">certs/client.crt"</span>
    <span class="na">key_file</span><span class="pi">:</span> <span class="s2">"</span><span class="s">certs/client.key"</span>

  <span class="na">monitoring</span><span class="pi">:</span>
    <span class="na">enable_metrics</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">metrics_interval</span><span class="pi">:</span> <span class="m">60000</span>       <span class="c1"># milliseconds</span>
    <span class="na">enable_access_logs</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">log_request_body</span><span class="pi">:</span> <span class="kc">false</span>
    <span class="na">log_response_body</span><span class="pi">:</span> <span class="kc">false</span>
</code></pre></div></div>

<h2 id="17-serialization-specifications">17. Serialization Specifications</h2>

<h3 id="171-json-schema-definitions">17.1 JSON Schema Definitions</h3>

<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="err">SERIALIZATION_STANDARDS:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"encoding"</span><span class="p">:</span><span class="w"> </span><span class="s2">"UTF-8"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"JSON"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"validation"</span><span class="p">:</span><span class="w"> </span><span class="s2">"JSON Schema Draft 07"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"date_format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"ISO 8601 (RFC 3339)"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"number_precision"</span><span class="p">:</span><span class="w"> </span><span class="s2">"IEEE 754 double precision"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"string_max_length"</span><span class="p">:</span><span class="w"> </span><span class="mi">1048576</span><span class="p">,</span><span class="w">
  </span><span class="nl">"object_max_depth"</span><span class="p">:</span><span class="w"> </span><span class="mi">32</span><span class="p">,</span><span class="w">
  </span><span class="nl">"array_max_length"</span><span class="p">:</span><span class="w"> </span><span class="mi">10000</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="err">BASE_MESSAGE_SCHEMA:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"$schema"</span><span class="p">:</span><span class="w"> </span><span class="s2">"http://json-schema.org/draft-07/schema#"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"$id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://schemas.mister-smith.dev/base-message.json"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"object"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"required"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"message_id"</span><span class="p">,</span><span class="w"> </span><span class="s2">"timestamp"</span><span class="p">,</span><span class="w"> </span><span class="s2">"version"</span><span class="p">],</span><span class="w">
  </span><span class="nl">"properties"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"message_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Unique message identifier"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"date-time"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Message creation timestamp in ISO 8601 format"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"version"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"pattern"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^</span><span class="se">\\</span><span class="s2">d+</span><span class="se">\\</span><span class="s2">.</span><span class="se">\\</span><span class="s2">d+</span><span class="se">\\</span><span class="s2">.</span><span class="se">\\</span><span class="s2">d+$"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Schema version (semantic versioning)"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"correlation_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Optional correlation identifier for message tracking"</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="nl">"trace_id"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"string"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"uuid"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Distributed tracing identifier"</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="err">VALIDATION_RULES:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"strict_mode"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
  </span><span class="nl">"additional_properties"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span><span class="w">
  </span><span class="nl">"validate_formats"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
  </span><span class="nl">"validate_required"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
  </span><span class="nl">"fail_on_unknown_keywords"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
  </span><span class="nl">"error_aggregation"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
  </span><span class="nl">"max_validation_errors"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="err">COMPRESSION_SETTINGS:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"algorithm"</span><span class="p">:</span><span class="w"> </span><span class="s2">"gzip"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"compression_level"</span><span class="p">:</span><span class="w"> </span><span class="mi">6</span><span class="p">,</span><span class="w">
  </span><span class="nl">"min_size_threshold"</span><span class="p">:</span><span class="w"> </span><span class="mi">1024</span><span class="p">,</span><span class="w">
  </span><span class="nl">"content_types"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
    </span><span class="s2">"application/json"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"text/plain"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"application/xml"</span><span class="w">
  </span><span class="p">]</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="err">SECURITY_CONSTRAINTS:</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">"max_string_length"</span><span class="p">:</span><span class="w"> </span><span class="mi">1048576</span><span class="p">,</span><span class="w">
  </span><span class="nl">"max_array_length"</span><span class="p">:</span><span class="w"> </span><span class="mi">10000</span><span class="p">,</span><span class="w">
  </span><span class="nl">"max_object_properties"</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span><span class="p">,</span><span class="w">
  </span><span class="nl">"max_nesting_depth"</span><span class="p">:</span><span class="w"> </span><span class="mi">32</span><span class="p">,</span><span class="w">
  </span><span class="nl">"prohibited_patterns"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
    </span><span class="s2">"javascript:"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"data:"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"vbscript:"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"&lt;script"</span><span class="p">,</span><span class="w">
    </span><span class="s2">"&lt;/script&gt;"</span><span class="w">
  </span><span class="p">],</span><span class="w">
  </span><span class="nl">"sanitization"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"html_entities"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
    </span><span class="nl">"sql_injection"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
    </span><span class="nl">"xss_prevention"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h3 id="172-binary-serialization-protocol-buffers">17.2 Binary Serialization (Protocol Buffers)</h3>

<div class="language-protobuf highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">syntax</span> <span class="o">=</span> <span class="s">"proto3"</span><span class="p">;</span>
<span class="kn">package</span> <span class="nn">mister_smith</span><span class="o">.</span><span class="n">serialization</span><span class="p">;</span>

<span class="k">import</span> <span class="s">"google/protobuf/timestamp.proto"</span><span class="p">;</span>
<span class="k">import</span> <span class="s">"google/protobuf/any.proto"</span><span class="p">;</span>

<span class="c1">// Base message wrapper for all protocol buffer messages</span>
<span class="kd">message</span> <span class="nc">BaseMessage</span> <span class="p">{</span>
  <span class="kt">string</span> <span class="na">message_id</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="n">google.protobuf.Timestamp</span> <span class="na">timestamp</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">version</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">correlation_id</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">trace_id</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
  <span class="n">google.protobuf.Any</span> <span class="na">payload</span> <span class="o">=</span> <span class="mi">6</span><span class="p">;</span>
  <span class="n">map</span><span class="o">&lt;</span><span class="kt">string</span><span class="p">,</span> <span class="kt">string</span><span class="err">&gt;</span> <span class="na">metadata</span> <span class="o">=</span> <span class="mi">7</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// Performance optimized message for high-frequency data</span>
<span class="kd">message</span> <span class="nc">CompactMessage</span> <span class="p">{</span>
  <span class="kt">bytes</span> <span class="na">message_id</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>  <span class="c1">// 16-byte UUID as bytes</span>
  <span class="kt">int64</span> <span class="na">timestamp_unix</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>  <span class="c1">// Unix timestamp in nanoseconds</span>
  <span class="kt">bytes</span> <span class="na">payload</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>  <span class="c1">// Compressed payload</span>
  <span class="kt">uint32</span> <span class="na">checksum</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>  <span class="c1">// CRC32 checksum</span>
<span class="p">}</span>

<span class="c1">// Message envelope for batch processing</span>
<span class="kd">message</span> <span class="nc">MessageBatch</span> <span class="p">{</span>
  <span class="k">repeated</span> <span class="n">BaseMessage</span> <span class="na">messages</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="kt">uint32</span> <span class="na">batch_size</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="kt">string</span> <span class="na">batch_id</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="n">google.protobuf.Timestamp</span> <span class="na">created_at</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
  <span class="n">CompressionType</span> <span class="na">compression</span> <span class="o">=</span> <span class="mi">5</span><span class="p">;</span>
<span class="p">}</span>

<span class="kd">enum</span> <span class="n">CompressionType</span> <span class="p">{</span>
  <span class="na">COMPRESSION_TYPE_UNSPECIFIED</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
  <span class="na">NONE</span> <span class="o">=</span> <span class="mi">1</span><span class="p">;</span>
  <span class="na">GZIP</span> <span class="o">=</span> <span class="mi">2</span><span class="p">;</span>
  <span class="na">LZ4</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
  <span class="na">SNAPPY</span> <span class="o">=</span> <span class="mi">4</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div></div>

<h3 id="173-serialization-performance-configuration">17.3 Serialization Performance Configuration</h3>

<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">SERIALIZATION_PERFORMANCE</span><span class="pi">:</span>
  <span class="na">json</span><span class="pi">:</span>
    <span class="na">parser</span><span class="pi">:</span> <span class="s2">"</span><span class="s">serde_json"</span>
    <span class="na">writer_buffer_size</span><span class="pi">:</span> <span class="m">8192</span>
    <span class="na">reader_buffer_size</span><span class="pi">:</span> <span class="m">8192</span>
    <span class="na">pretty_print</span><span class="pi">:</span> <span class="kc">false</span>
    <span class="na">escape_unicode</span><span class="pi">:</span> <span class="kc">false</span>
    <span class="na">floating_point_precision</span><span class="pi">:</span> <span class="m">15</span>
    <span class="na">use_compact_representation</span><span class="pi">:</span> <span class="kc">true</span>

  <span class="na">protobuf</span><span class="pi">:</span>
    <span class="na">codec</span><span class="pi">:</span> <span class="s2">"</span><span class="s">prost"</span>
    <span class="na">max_message_size</span><span class="pi">:</span> <span class="m">67108864</span>  <span class="c1"># 64MB</span>
    <span class="na">use_varint_encoding</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">preserve_unknown_fields</span><span class="pi">:</span> <span class="kc">false</span>
    <span class="na">deterministic_serialization</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">lazy_parsing</span><span class="pi">:</span> <span class="kc">true</span>

  <span class="na">messagepack</span><span class="pi">:</span>
    <span class="na">codec</span><span class="pi">:</span> <span class="s2">"</span><span class="s">rmp-serde"</span>
    <span class="na">use_compact_integers</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">use_string_keys</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">preserve_order</span><span class="pi">:</span> <span class="kc">false</span>

  <span class="na">compression</span><span class="pi">:</span>
    <span class="na">threshold_bytes</span><span class="pi">:</span> <span class="m">1024</span>
    <span class="na">algorithms</span><span class="pi">:</span>
      <span class="na">gzip</span><span class="pi">:</span>
        <span class="na">level</span><span class="pi">:</span> <span class="m">6</span>
        <span class="na">window_bits</span><span class="pi">:</span> <span class="m">15</span>
        <span class="na">memory_level</span><span class="pi">:</span> <span class="m">8</span>
      <span class="na">lz4</span><span class="pi">:</span>
        <span class="na">acceleration</span><span class="pi">:</span> <span class="m">1</span>
        <span class="na">compression_level</span><span class="pi">:</span> <span class="m">0</span>
      <span class="na">snappy</span><span class="pi">:</span>
        <span class="na">enable_checksum</span><span class="pi">:</span> <span class="kc">true</span>

<span class="na">VALIDATION_PERFORMANCE</span><span class="pi">:</span>
  <span class="na">schema_cache_size</span><span class="pi">:</span> <span class="m">1000</span>
  <span class="na">schema_cache_ttl</span><span class="pi">:</span> <span class="m">3600</span>      <span class="c1"># seconds</span>
  <span class="na">parallel_validation</span><span class="pi">:</span> <span class="kc">true</span>
  <span class="na">max_validation_threads</span><span class="pi">:</span> <span class="m">4</span>
  <span class="na">validation_timeout</span><span class="pi">:</span> <span class="m">5000</span>    <span class="c1"># milliseconds</span>
  <span class="na">enable_fast_path</span><span class="pi">:</span> <span class="kc">true</span>
  <span class="na">skip_optional_validation</span><span class="pi">:</span> <span class="kc">false</span>
</code></pre></div></div>

<hr />

<p><em>Transport Layer Specifications - Complete Protocol Implementation for Agent Communication</em></p>

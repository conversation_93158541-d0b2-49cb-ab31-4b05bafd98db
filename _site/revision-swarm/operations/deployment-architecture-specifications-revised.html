<h1 id="deployment-architecture-specifications---revised">Deployment Architecture Specifications - Revised</h1>
<h2 id="multi-agent-platform-deployment-patterns">Multi-Agent Platform Deployment Patterns</h2>

<h3 id="executive-summary">Executive Summary</h3>

<p>This document provides deployment architecture patterns for multi-agent platforms, focusing on containerization strategies, orchestration patterns, and scalable infrastructure designs without implementation-specific details.</p>

<h3 id="1-container-architecture-patterns">1. Container Architecture Patterns</h3>

<h4 id="11-base-container-strategy-pattern">1.1 Base Container Strategy Pattern</h4>
<pre><code class="language-pseudocode">PATTERN MultiStageContainer:
    STAGES:
        build_stage:
            setup_build_environment()
            copy_source_code()
            compile_application()
            run_tests()
        
        runtime_stage:
            create_minimal_image()
            copy_built_artifacts()
            configure_runtime()
            expose_service_ports()
            define_entry_point()
</code></pre>

<h4 id="12-agent-container-types-pattern">1.2 Agent Container Types Pattern</h4>
<pre><code class="language-pseudocode">PATTERN AgentContainerTypes:
    CONTAINER_CATEGORIES:
        orchestrator_container:
            purpose: "coordination and management"
            resource_profile: "moderate"
            exposed_services: ["management_api", "coordination_rpc"]
        
        worker_container:
            purpose: "task execution"
            resource_profile: "variable"
            exposed_services: ["worker_api"]
        
        messaging_container:
            purpose: "inter-agent communication"
            resource_profile: "minimal"
            exposed_services: ["message_bus", "event_stream"]
</code></pre>

<h4 id="13-sidecar-pattern-implementation">1.3 Sidecar Pattern Implementation</h4>
<pre><code class="language-pseudocode">PATTERN SidecarArchitecture:
    SIDECAR_TYPES:
        coordination_proxy:
            intercept_agent_communication()
            apply_routing_rules()
            enforce_policies()
        
        metrics_collector:
            gather_agent_metrics()
            aggregate_locally()
            export_to_backend()
        
        security_proxy:
            handle_authentication()
            enforce_authorization()
            audit_access()
</code></pre>

<h3 id="2-orchestration-patterns">2. Orchestration Patterns</h3>

<h4 id="21-dag-vs-fsm-orchestration-pattern">2.1 DAG vs FSM Orchestration Pattern</h4>
<pre><code class="language-pseudocode">PATTERN OrchestrationModelSelection:
    DAG_PATTERN:
        characteristics:
            - "explicit dependency graph"
            - "batch processing oriented"
            - "visual representation"
            - "static or dynamic construction"
        
        use_cases:
            - "data pipelines"
            - "batch workflows"
            - "ETL processes"
            - "parallel task execution"
        
        implementation:
            static_dag:
                define_all_tasks()
                explicit_dependencies()
                compile_time_validation()
            
            dynamic_dag:
                runtime_task_generation()
                inferred_dependencies()
                flexible_execution_paths()
    
    FSM_PATTERN:
        characteristics:
            - "state-based control flow"
            - "complex decision logic"
            - "compensation support"
            - "dynamic transitions"
        
        use_cases:
            - "order processing"
            - "approval workflows"
            - "multi-step transactions"
            - "error recovery flows"
        
        implementation:
            state_machine:
                define_states()
                transition_rules()
                compensation_stack()
                rollback_handlers()
</code></pre>

<h4 id="22-workflow-retry-pattern">2.2 Workflow Retry Pattern</h4>
<pre><code class="language-pseudocode">PATTERN RetryStrategies:
    RETRY_POLICIES:
        exponential_backoff:
            initial_delay: "1s"
            max_delay: "300s"
            backoff_factor: 2
            jitter: "random_0_to_1"
            
        linear_backoff:
            fixed_delay: "30s"
            max_attempts: 3
            
        circuit_breaker:
            failure_threshold: 5
            recovery_timeout: "60s"
            half_open_attempts: 3
    
    RETRY_IMPLEMENTATION:
        task_level_retry:
            configure_per_task_policy()
            track_attempt_count()
            handle_final_failure()
            
        workflow_level_retry:
            checkpoint_state()
            resume_from_failure()
            compensate_completed_tasks()
            
        dead_letter_handling:
            capture_failed_tasks()
            manual_intervention_queue()
            retry_with_modifications()
</code></pre>

<h4 id="23-namespace-organization-pattern">2.3 Namespace Organization Pattern</h4>
<pre><code class="language-pseudocode">PATTERN NamespaceArchitecture:
    NAMESPACE_CATEGORIES:
        system_namespace:
            purpose: "core platform components"
            isolation_level: "strict"
            components: ["orchestrators", "messaging", "configuration"]
        
        workload_namespace:
            purpose: "agent workloads"
            isolation_level: "moderate"
            components: ["agent_pools", "task_queues"]
        
        data_namespace:
            purpose: "persistence layer"
            isolation_level: "strict"
            components: ["databases", "caches", "storage"]
        
        monitoring_namespace:
            purpose: "observability stack"
            isolation_level: "moderate"
            components: ["metrics", "logs", "traces"]
</code></pre>

<h4 id="24-deployment-topology-pattern">2.4 Deployment Topology Pattern</h4>
<pre><code class="language-pseudocode">PATTERN DeploymentTopology:
    ORCHESTRATOR_DEPLOYMENT:
        replica_strategy: "odd_number_for_consensus"
        distribution: "across_availability_zones"
        update_strategy: "rolling_with_zero_downtime"
        health_checks: ["liveness", "readiness", "startup"]
    
    WORKER_DEPLOYMENT:
        replica_strategy: "dynamic_based_on_load"
        distribution: "maximize_spread"
        update_strategy: "blue_green_or_canary"
        scaling_policy: "horizontal_and_vertical"
</code></pre>

<h4 id="25-service-discovery-pattern">2.5 Service Discovery Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ServiceDiscovery:
    DISCOVERY_METHODS:
        dns_based:
            register_service_endpoints()
            resolve_by_service_name()
            handle_endpoint_changes()
        
        registry_based:
            register_with_metadata()
            query_by_attributes()
            watch_for_updates()
        
        mesh_based:
            automatic_sidecar_injection()
            intelligent_routing()
            circuit_breaking()
</code></pre>

<h3 id="3-scaling-architecture-patterns">3. Scaling Architecture Patterns</h3>

<h4 id="31-horizontal-scaling-pattern">3.1 Horizontal Scaling Pattern</h4>
<pre><code class="language-pseudocode">PATTERN HorizontalScaling:
    SCALING_TRIGGERS:
        resource_based:
            monitor_cpu_usage()
            monitor_memory_usage()
            monitor_network_throughput()
        
        application_based:
            monitor_queue_depth()
            monitor_response_time()
            monitor_error_rate()
        
        custom_metrics:
            monitor_business_metrics()
            monitor_agent_specific_metrics()
    
    SCALING_BEHAVIOR:
        scale_up_policy:
            stabilization_window
            maximum_scale_rate
            scale_up_threshold
        
        scale_down_policy:
            cooldown_period
            gradual_reduction
            minimum_instances
</code></pre>

<h4 id="32-cluster-autoscaling-pattern">3.2 Cluster Autoscaling Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ClusterAutoscaling:
    NODE_GROUPS:
        control_plane_nodes:
            purpose: "orchestration"
            scaling: "vertical_preferred"
            placement: "dedicated_hosts"
        
        worker_nodes:
            purpose: "agent_workloads"
            scaling: "horizontal_preferred"
            placement: "spot_instances_allowed"
        
        data_nodes:
            purpose: "persistence"
            scaling: "careful_horizontal"
            placement: "storage_optimized"
</code></pre>

<h4 id="33-resource-allocation-pattern">3.3 Resource Allocation Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ResourceAllocation:
    RESOURCE_TIERS:
        minimal_tier:
            cpu_allocation: "fractional"
            memory_allocation: "constrained"
            priority: "best_effort"
        
        standard_tier:
            cpu_allocation: "guaranteed_minimum"
            memory_allocation: "reserved"
            priority: "normal"
        
        premium_tier:
            cpu_allocation: "dedicated"
            memory_allocation: "exclusive"
            priority: "high"
</code></pre>

<h4 id="34-orchestration-autoscale-pattern">3.4 Orchestration Autoscale Pattern</h4>
<pre><code class="language-pseudocode">PATTERN OrchestrationAutoscale:
    SCALING_STRATEGIES:
        queue_based_scaling:
            monitor_queue_depth()
            calculate_processing_rate()
            scale_workers_proportionally()
            consider_message_age()
            
        event_driven_scaling:
            monitor_event_streams()
            predict_load_patterns()
            preemptive_scaling()
            custom_metric_triggers()
            
        schedule_based_scaling:
            define_time_patterns()
            pre_scale_for_known_loads()
            combine_with_reactive_scaling()
            handle_timezone_variations()
    
    ORCHESTRATOR_SCALING:
        task_distribution_model:
            worker_pool_sizing:
                min_workers_per_queue: 2
                max_workers_per_queue: 100
                scale_increment: "10%_or_1_worker"
                
            task_assignment:
                round_robin_distribution()
                load_based_assignment()
                affinity_based_routing()
                
        resource_based_autoscale:
            cpu_threshold: "70%_sustained_60s"
            memory_threshold: "80%_sustained_30s"
            queue_depth_threshold: "100_pending_tasks"
            
        failure_aware_scaling:
            monitor_task_failures()
            detect_poison_messages()
            isolate_failing_workers()
            scale_healthy_workers()
</code></pre>

<h3 id="4-package-management-patterns">4. Package Management Patterns</h3>

<h4 id="41-helm-chart-structure-pattern">4.1 Helm Chart Structure Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ChartOrganization:
    STRUCTURE:
        chart_metadata:
            define_chart_properties()
            specify_dependencies()
            set_version_constraints()
        
        templates:
            deployment_templates()
            service_templates()
            configuration_templates()
            security_templates()
        
        values:
            default_values()
            environment_overrides()
            secret_references()
</code></pre>

<h4 id="42-configuration-management-pattern">4.2 Configuration Management Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ConfigurationHierarchy:
    LEVELS:
        base_configuration:
            common_settings()
            default_behaviors()

        environment_configuration:
            tier_specific_settings()
            resource_adjustments()

        runtime_configuration:
            dynamic_overrides()
            feature_toggles()
</code></pre>

<h4 id="43-claude-cli-configuration-pattern">4.3 Claude-CLI Configuration Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ClaudeCLIConfiguration:
    PARALLEL_EXECUTION_SETTINGS:
        environment_variables:
            CLAUDE_PARALLEL_DEFAULT: "default_fan_out_per_task_plan"
            # Controls default number of parallel agents spawned
            # when --parallel flag is not explicitly specified

        deployment_configuration:
            parallel_agent_limits:
                min_parallel_agents: 1
                max_parallel_agents: 50
                default_parallel_agents: 4

            resource_allocation:
                per_agent_cpu_limit: "0.5"
                per_agent_memory_limit: "512Mi"
                total_parallel_cpu_budget: "4.0"

        integration_settings:
            output_parsing_enabled: true
            nats_subject_mapping: true
            observability_integration: true
            span_tagging_per_agent: true
</code></pre>

<h3 id="5-network-architecture-patterns">5. Network Architecture Patterns</h3>

<h4 id="51-network-segmentation-pattern">5.1 Network Segmentation Pattern</h4>
<pre><code class="language-pseudocode">PATTERN NetworkSegmentation:
    SEGMENTS:
        control_plane_network:
            purpose: "management_traffic"
            isolation: "strict"
            encryption: "required"
        
        data_plane_network:
            purpose: "agent_communication"
            isolation: "moderate"
            encryption: "optional"
        
        ingress_network:
            purpose: "external_access"
            isolation: "dmz_pattern"
            encryption: "tls_required"
</code></pre>

<h4 id="52-service-mesh-pattern">5.2 Service Mesh Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ServiceMesh:
    CAPABILITIES:
        traffic_management:
            intelligent_routing()
            load_balancing()
            circuit_breaking()
            retry_logic:
                per_service_configuration()
                backoff_algorithms()
                retry_budgets()
                idempotency_headers()
        
        security:
            mutual_tls()
            authorization_policies()
            encryption_in_transit()
        
        observability:
            distributed_tracing()
            metrics_collection()
            traffic_visualization()
    
    ADVANCED_RETRY_CONFIGURATION:
        retry_conditions:
            retriable_status_codes: [502, 503, 504]
            retriable_headers: ["x-retry-after"]
            connect_failure: true
            reset_before_request: true
            
        retry_budgets:
            percentage_allowed: "20%_of_requests"
            min_retry_concurrency: 10
            ttl_seconds: 10
            
        backoff_configuration:
            base_interval: "25ms"
            max_interval: "250ms"
            backoff_algorithm: "exponential_with_jitter"
</code></pre>

<h3 id="6-deployment-pipeline-patterns">6. Deployment Pipeline Patterns</h3>

<h4 id="61-gitops-pattern">6.1 GitOps Pattern</h4>
<pre><code class="language-pseudocode">PATTERN GitOpsDeployment:
    WORKFLOW:
        source_of_truth: "git_repository"
        
        change_process:
            propose_change_via_pr()
            automated_validation()
            approval_workflow()
            automated_deployment()
        
        reconciliation:
            continuous_monitoring()
            drift_detection()
            automatic_correction()
</code></pre>

<h4 id="62-progressive-delivery-pattern">6.2 Progressive Delivery Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ProgressiveDelivery:
    STAGES:
        canary_release:
            deploy_to_small_subset()
            monitor_key_metrics()
            automated_analysis()
            promotion_decision()
        
        feature_flags:
            gradual_enablement()
            user_segmentation()
            instant_rollback()
        
        blue_green:
            parallel_environments()
            instant_switchover()
            rollback_capability()
</code></pre>

<h3 id="7-multi-environment-strategy-pattern">7. Multi-Environment Strategy Pattern</h3>

<h4 id="71-environment-tiers-pattern">7.1 Environment Tiers Pattern</h4>
<pre><code class="language-pseudocode">PATTERN EnvironmentTiers:
    TIER_DEFINITIONS:
        tier_1_experimental:
            purpose: "development_testing"
            scale: "minimal"
            data: "synthetic"
            stability: "volatile"
        
        tier_2_validation:
            purpose: "integration_testing"
            scale: "moderate"
            data: "anonymized"
            stability: "stable"
        
        tier_3_operational:
            purpose: "live_operations"
            scale: "full"
            data: "production"
            stability: "highly_stable"
</code></pre>

<h4 id="72-environment-promotion-pattern">7.2 Environment Promotion Pattern</h4>
<pre><code class="language-pseudocode">PATTERN EnvironmentPromotion:
    PROMOTION_FLOW:
        build_artifacts()
        deploy_to_tier_1()
        run_tier_1_validation()
        promote_to_tier_2()
        run_integration_tests()
        gate_checks()
        promote_to_tier_3()
        monitor_deployment()
</code></pre>

<h3 id="8-high-availability-patterns">8. High Availability Patterns</h3>

<h4 id="81-multi-region-deployment-pattern">8.1 Multi-Region Deployment Pattern</h4>
<pre><code class="language-pseudocode">PATTERN MultiRegionDeployment:
    TOPOLOGY:
        active_active:
            all_regions_serve_traffic()
            data_replication_async()
            conflict_resolution()
        
        active_passive:
            primary_region_active()
            standby_regions_ready()
            failover_automation()
</code></pre>

<h4 id="82-disaster-recovery-pattern">8.2 Disaster Recovery Pattern</h4>
<pre><code class="language-pseudocode">PATTERN DisasterRecovery:
    COMPONENTS:
        backup_strategy:
            continuous_backups()
            point_in_time_recovery()
            geographic_distribution()
        
        recovery_procedures:
            automated_failover()
            data_restoration()
            service_reconstruction()
            validation_testing()
</code></pre>

<h3 id="9-security-patterns">9. Security Patterns</h3>

<h4 id="91-zero-trust-architecture-pattern">9.1 Zero Trust Architecture Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ZeroTrustSecurity:
    PRINCIPLES:
        never_trust_always_verify()
        least_privilege_access()
        assume_breach_mindset()
        continuous_verification()
    
    IMPLEMENTATION:
        identity_verification()
        device_validation()
        application_authentication()
        data_encryption()
        continuous_monitoring()
</code></pre>

<h4 id="92-secret-management-pattern">9.2 Secret Management Pattern</h4>
<pre><code class="language-pseudocode">PATTERN SecretManagement:
    SECRET_LIFECYCLE:
        generation: "automated_strong_secrets"
        storage: "encrypted_vault"
        distribution: "secure_injection"
        rotation: "automated_periodic"
        auditing: "comprehensive_logging"
</code></pre>

<h3 id="10-monitoring-integration-patterns">10. Monitoring Integration Patterns</h3>

<h4 id="101-observability-stack-pattern">10.1 Observability Stack Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ObservabilityIntegration:
    COMPONENTS:
        metrics_pipeline:
            collection_agents()
            aggregation_layer()
            storage_backend()
            query_interface()
        
        logging_pipeline:
            log_collectors()
            processing_layer()
            indexing_system()
            search_interface()
        
        tracing_pipeline:
            trace_collectors()
            sampling_layer()
            storage_system()
            analysis_tools()
</code></pre>

<h3 id="11-cost-optimization-patterns">11. Cost Optimization Patterns</h3>

<h4 id="111-resource-optimization-pattern">11.1 Resource Optimization Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ResourceOptimization:
    STRATEGIES:
        right_sizing:
            analyze_usage_patterns()
            adjust_allocations()
            continuous_monitoring()
        
        spot_instance_usage:
            identify_suitable_workloads()
            implement_fallback_strategy()
            cost_benefit_analysis()
        
        auto_shutdown:
            identify_idle_resources()
            schedule_based_scaling()
            on_demand_activation()
</code></pre>

<h3 id="12-implementation-guidelines">12. Implementation Guidelines</h3>

<h4 id="121-deployment-orchestration-pattern">12.1 Deployment Orchestration Pattern</h4>
<pre><code class="language-pseudocode">PATTERN DeploymentOrchestration:
    PHASES:
        infrastructure_preparation:
            provision_compute_resources()
            configure_networking()
            setup_storage_systems()
        
        platform_deployment:
            deploy_core_services()
            configure_messaging()
            setup_orchestration()
        
        workload_deployment:
            deploy_agent_pools()
            configure_scaling()
            enable_monitoring()
        
        validation:
            health_checks()
            integration_tests()
            performance_validation()
</code></pre>

<h3 id="13-best-practices-summary">13. Best Practices Summary</h3>

<h4 id="131-container-best-practices">13.1 Container Best Practices</h4>
<ul>
  <li>Use multi-stage builds for optimization</li>
  <li>Implement proper health checks</li>
  <li>Follow security scanning practices</li>
  <li>Maintain minimal base images</li>
</ul>

<h4 id="132-orchestration-best-practices">13.2 Orchestration Best Practices</h4>
<ul>
  <li>Implement proper resource limits</li>
  <li>Use anti-affinity for high availability</li>
  <li>Enable pod disruption budgets</li>
  <li>Implement graceful shutdowns</li>
</ul>

<h4 id="133-scaling-best-practices">13.3 Scaling Best Practices</h4>
<ul>
  <li>Define clear scaling metrics</li>
  <li>Implement gradual scaling policies</li>
  <li>Monitor scaling effectiveness</li>
  <li>Plan for burst capacity</li>
</ul>

<h4 id="134-security-best-practices">13.4 Security Best Practices</h4>
<ul>
  <li>Implement network policies</li>
  <li>Use service accounts properly</li>
  <li>Enable audit logging</li>
  <li>Regular security updates</li>
</ul>

<hr />

<h2 id="concrete-deployment-templates">CONCRETE DEPLOYMENT TEMPLATES</h2>

<h3 id="14-dockerfile-templates">14. Dockerfile Templates</h3>

<h4 id="141-base-orchestrator-container-template">14.1 Base Orchestrator Container Template</h4>
<div class="language-dockerfile highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c"># syntax=docker/dockerfile:1</span>
<span class="c"># Multi-stage Dockerfile for Mister Smith Orchestrator Agent</span>

<span class="k">ARG</span><span class="s"> RUST_VERSION="1.75"</span>
<span class="k">ARG</span><span class="s"> ALPINE_VERSION="3.19"</span>

<span class="c"># Build Stage</span>
<span class="k">FROM</span><span class="w"> </span><span class="s">rust:${RUST_VERSION}-alpine${ALPINE_VERSION}</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="s">builder</span>
<span class="k">LABEL</span><span class="s"> stage=builder</span>

<span class="c"># Install build dependencies</span>
<span class="k">RUN </span>apk add <span class="nt">--no-cache</span> <span class="se">\
</span>    musl-dev <span class="se">\
</span>    pkgconfig <span class="se">\
</span>    openssl-dev <span class="se">\
</span>    <span class="o">&amp;&amp;</span> <span class="nb">rm</span> <span class="nt">-rf</span> /var/cache/apk/<span class="k">*</span>

<span class="c"># Create app user for security</span>
<span class="k">RUN </span>addgroup <span class="nt">-g</span> 1001 <span class="nt">-S</span> appgroup <span class="o">&amp;&amp;</span> <span class="se">\
</span>    adduser <span class="nt">-u</span> 1001 <span class="nt">-S</span> appuser <span class="nt">-G</span> appgroup

<span class="k">WORKDIR</span><span class="s"> /app</span>

<span class="c"># Copy dependency manifests first for better caching</span>
<span class="k">COPY</span><span class="s"> Cargo.toml Cargo.lock ./</span>
<span class="k">COPY</span><span class="s"> src ./src</span>

<span class="c"># Build with optimizations</span>
<span class="k">RUN </span>cargo build <span class="nt">--release</span> <span class="nt">--locked</span> <span class="o">&amp;&amp;</span> <span class="se">\
</span>    strip target/release/mister-smith-orchestrator

<span class="c"># Runtime Stage</span>
<span class="k">FROM</span><span class="w"> </span><span class="s">alpine:${ALPINE_VERSION}@sha256:a8560b36e8b8210634f77d9f7f9efd7ffa463e380b75e2e74aff4511df3ef88c</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="s">runtime</span>

<span class="c"># Install runtime dependencies</span>
<span class="k">RUN </span>apk add <span class="nt">--no-cache</span> <span class="se">\
</span>    ca-certificates <span class="se">\
</span>    libgcc <span class="se">\
</span>    openssl <span class="se">\
</span>    <span class="o">&amp;&amp;</span> <span class="nb">rm</span> <span class="nt">-rf</span> /var/cache/apk/<span class="k">*</span>

<span class="c"># Create app user (matching builder stage)</span>
<span class="k">RUN </span>addgroup <span class="nt">-g</span> 1001 <span class="nt">-S</span> appgroup <span class="o">&amp;&amp;</span> <span class="se">\
</span>    adduser <span class="nt">-u</span> 1001 <span class="nt">-S</span> appuser <span class="nt">-G</span> appgroup

<span class="c"># Copy binary from builder stage</span>
<span class="k">COPY</span><span class="s"> --from=builder --chown=appuser:appgroup /app/target/release/mister-smith-orchestrator /usr/local/bin/orchestrator</span>

<span class="c"># Create necessary directories</span>
<span class="k">RUN </span><span class="nb">mkdir</span> <span class="nt">-p</span> /app/logs /app/config <span class="o">&amp;&amp;</span> <span class="se">\
</span>    <span class="nb">chown</span> <span class="nt">-R</span> appuser:appgroup /app

<span class="c"># Switch to non-root user</span>
<span class="k">USER</span><span class="s"> appuser</span>

<span class="c"># Set working directory</span>
<span class="k">WORKDIR</span><span class="s"> /app</span>

<span class="c"># Health check endpoint</span>
<span class="k">HEALTHCHECK</span><span class="s"> --interval=30s --timeout=10s --start-period=60s --retries=3 \</span>
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

<span class="c"># Environment variables</span>
<span class="k">ENV</span><span class="s"> RUST_LOG=info</span>
<span class="k">ENV</span><span class="s"> AGENT_TYPE=orchestrator</span>
<span class="k">ENV</span><span class="s"> HTTP_PORT=8080</span>
<span class="k">ENV</span><span class="s"> GRPC_PORT=9090</span>

<span class="c"># Expose ports</span>
<span class="k">EXPOSE</span><span class="s"> 8080 9090</span>

<span class="c"># Start the orchestrator</span>
<span class="k">ENTRYPOINT</span><span class="s"> ["/usr/local/bin/orchestrator"]</span>
</code></pre></div></div>

<h4 id="142-worker-container-template">14.2 Worker Container Template</h4>
<div class="language-dockerfile highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c"># syntax=docker/dockerfile:1</span>
<span class="c"># Multi-stage Dockerfile for Mister Smith Worker Agent</span>

<span class="k">ARG</span><span class="s"> RUST_VERSION="1.75"</span>
<span class="k">ARG</span><span class="s"> ALPINE_VERSION="3.19"</span>

<span class="c"># Build Stage  </span>
<span class="k">FROM</span><span class="w"> </span><span class="s">rust:${RUST_VERSION}-alpine${ALPINE_VERSION}</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="s">builder</span>
<span class="k">LABEL</span><span class="s"> stage=builder</span>

<span class="c"># Install build dependencies</span>
<span class="k">RUN </span>apk add <span class="nt">--no-cache</span> <span class="se">\
</span>    musl-dev <span class="se">\
</span>    pkgconfig <span class="se">\
</span>    openssl-dev <span class="se">\
</span>    <span class="o">&amp;&amp;</span> <span class="nb">rm</span> <span class="nt">-rf</span> /var/cache/apk/<span class="k">*</span>

<span class="k">WORKDIR</span><span class="s"> /app</span>

<span class="c"># Copy and build application</span>
<span class="k">COPY</span><span class="s"> Cargo.toml Cargo.lock ./</span>
<span class="k">COPY</span><span class="s"> src ./src</span>

<span class="k">RUN </span>cargo build <span class="nt">--release</span> <span class="nt">--locked</span> <span class="o">&amp;&amp;</span> <span class="se">\
</span>    strip target/release/mister-smith-worker

<span class="c"># Runtime Stage</span>
<span class="k">FROM</span><span class="w"> </span><span class="s">alpine:${ALPINE_VERSION}@sha256:a8560b36e8b8210634f77d9f7f9efd7ffa463e380b75e2e74aff4511df3ef88c</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="s">runtime</span>

<span class="c"># Install runtime dependencies</span>
<span class="k">RUN </span>apk add <span class="nt">--no-cache</span> <span class="se">\
</span>    ca-certificates <span class="se">\
</span>    libgcc <span class="se">\
</span>    openssl <span class="se">\
</span>    curl <span class="se">\
</span>    <span class="o">&amp;&amp;</span> <span class="nb">rm</span> <span class="nt">-rf</span> /var/cache/apk/<span class="k">*</span>

<span class="c"># Create app user</span>
<span class="k">RUN </span>addgroup <span class="nt">-g</span> 1001 <span class="nt">-S</span> appgroup <span class="o">&amp;&amp;</span> <span class="se">\
</span>    adduser <span class="nt">-u</span> 1001 <span class="nt">-S</span> appuser <span class="nt">-G</span> appgroup

<span class="c"># Copy binary</span>
<span class="k">COPY</span><span class="s"> --from=builder --chown=appuser:appgroup /app/target/release/mister-smith-worker /usr/local/bin/worker</span>

<span class="c"># Create directories</span>
<span class="k">RUN </span><span class="nb">mkdir</span> <span class="nt">-p</span> /app/workspace /app/logs <span class="o">&amp;&amp;</span> <span class="se">\
</span>    <span class="nb">chown</span> <span class="nt">-R</span> appuser:appgroup /app

<span class="k">USER</span><span class="s"> appuser</span>
<span class="k">WORKDIR</span><span class="s"> /app</span>

<span class="c"># Health checks</span>
<span class="k">HEALTHCHECK</span><span class="s"> --interval=15s --timeout=5s --start-period=30s --retries=3 \</span>
    CMD curl -f http://localhost:8081/health || exit 1

<span class="c"># Environment configuration</span>
<span class="k">ENV</span><span class="s"> RUST_LOG=info</span>
<span class="k">ENV</span><span class="s"> AGENT_TYPE=worker</span>
<span class="k">ENV</span><span class="s"> HTTP_PORT=8081</span>
<span class="k">ENV</span><span class="s"> WORKER_CONCURRENCY=4</span>

<span class="k">EXPOSE</span><span class="s"> 8081</span>

<span class="k">ENTRYPOINT</span><span class="s"> ["/usr/local/bin/worker"]</span>
</code></pre></div></div>

<h4 id="143-messaging-container-template">14.3 Messaging Container Template</h4>
<div class="language-dockerfile highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c"># syntax=docker/dockerfile:1</span>
<span class="c"># NATS-based Messaging Container for Mister Smith</span>

<span class="k">FROM</span><span class="w"> </span><span class="s">nats:2.10-alpine</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="s">runtime</span>

<span class="c"># Install additional tools</span>
<span class="k">RUN </span>apk add <span class="nt">--no-cache</span> curl

<span class="c"># Create nats user and directories</span>
<span class="k">RUN </span>addgroup <span class="nt">-g</span> 1001 <span class="nt">-S</span> natsgroup <span class="o">&amp;&amp;</span> <span class="se">\
</span>    adduser <span class="nt">-u</span> 1001 <span class="nt">-S</span> natsuser <span class="nt">-G</span> natsgroup <span class="o">&amp;&amp;</span> <span class="se">\
</span>    <span class="nb">mkdir</span> <span class="nt">-p</span> /etc/nats /var/lib/nats <span class="o">&amp;&amp;</span> <span class="se">\
</span>    <span class="nb">chown</span> <span class="nt">-R</span> natsuser:natsgroup /etc/nats /var/lib/nats

<span class="c"># Copy NATS configuration</span>
<span class="k">COPY</span><span class="s"> --chown=natsuser:natsgroup nats.conf /etc/nats/nats.conf</span>

<span class="k">USER</span><span class="s"> natsuser</span>

<span class="c"># Health check for NATS</span>
<span class="k">HEALTHCHECK</span><span class="s"> --interval=10s --timeout=5s --start-period=10s --retries=3 \</span>
    CMD nats server ping || exit 1

<span class="c"># Environment variables</span>
<span class="k">ENV</span><span class="s"> NATS_CONFIG_FILE=/etc/nats/nats.conf</span>

<span class="k">EXPOSE</span><span class="s"> 4222 8222 6222</span>

<span class="k">ENTRYPOINT</span><span class="s"> ["/nats-server", "-c", "/etc/nats/nats.conf"]</span>
</code></pre></div></div>

<h3 id="15-docker-compose-specifications">15. Docker Compose Specifications</h3>

<h4 id="151-base-development-stack">15.1 Base Development Stack</h4>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># docker-compose.yml - Base Mister Smith development stack</span>
<span class="na">version</span><span class="pi">:</span> <span class="s1">'</span><span class="s">3.8'</span>

<span class="na">services</span><span class="pi">:</span>
  <span class="na">nats</span><span class="pi">:</span>
    <span class="na">image</span><span class="pi">:</span> <span class="s">nats:2.10-alpine</span>
    <span class="na">container_name</span><span class="pi">:</span> <span class="s">mister-smith-nats</span>
    <span class="na">ports</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">4222:4222"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">8222:8222"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">6222:6222"</span>
    <span class="na">command</span><span class="pi">:</span> <span class="pi">[</span><span class="s2">"</span><span class="s">-js"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">-m"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">8222"</span><span class="pi">]</span>
    <span class="na">healthcheck</span><span class="pi">:</span>
      <span class="na">test</span><span class="pi">:</span> <span class="pi">[</span><span class="s2">"</span><span class="s">CMD"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">wget"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">--spider"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">-q"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">http://localhost:8222/healthz"</span><span class="pi">]</span>
      <span class="na">interval</span><span class="pi">:</span> <span class="s">10s</span>
      <span class="na">timeout</span><span class="pi">:</span> <span class="s">5s</span>
      <span class="na">retries</span><span class="pi">:</span> <span class="m">3</span>
    <span class="na">networks</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">mister-smith-net</span>

  <span class="na">redis</span><span class="pi">:</span>
    <span class="na">image</span><span class="pi">:</span> <span class="s">redis:7-alpine</span>
    <span class="na">container_name</span><span class="pi">:</span> <span class="s">mister-smith-redis</span>
    <span class="na">ports</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">6379:6379"</span>
    <span class="na">command</span><span class="pi">:</span> <span class="s">redis-server --appendonly yes</span>
    <span class="na">volumes</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">redis_data:/data</span>
    <span class="na">healthcheck</span><span class="pi">:</span>
      <span class="na">test</span><span class="pi">:</span> <span class="pi">[</span><span class="s2">"</span><span class="s">CMD"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">redis-cli"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">ping"</span><span class="pi">]</span>
      <span class="na">interval</span><span class="pi">:</span> <span class="s">10s</span>
      <span class="na">timeout</span><span class="pi">:</span> <span class="s">5s</span>
      <span class="na">retries</span><span class="pi">:</span> <span class="m">3</span>
    <span class="na">networks</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">mister-smith-net</span>

  <span class="na">postgres</span><span class="pi">:</span>
    <span class="na">image</span><span class="pi">:</span> <span class="s">postgres:16-alpine</span>
    <span class="na">container_name</span><span class="pi">:</span> <span class="s">mister-smith-postgres</span>
    <span class="na">environment</span><span class="pi">:</span>
      <span class="na">POSTGRES_DB</span><span class="pi">:</span> <span class="s">mister_smith</span>
      <span class="na">POSTGRES_USER</span><span class="pi">:</span> <span class="s">postgres</span>
      <span class="na">POSTGRES_PASSWORD</span><span class="pi">:</span> <span class="s">password</span>
    <span class="na">ports</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">5432:5432"</span>
    <span class="na">volumes</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">postgres_data:/var/lib/postgresql/data</span>
    <span class="na">healthcheck</span><span class="pi">:</span>
      <span class="na">test</span><span class="pi">:</span> <span class="pi">[</span><span class="s2">"</span><span class="s">CMD-SHELL"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">pg_isready</span><span class="nv"> </span><span class="s">-U</span><span class="nv"> </span><span class="s">postgres"</span><span class="pi">]</span>
      <span class="na">interval</span><span class="pi">:</span> <span class="s">10s</span>
      <span class="na">timeout</span><span class="pi">:</span> <span class="s">5s</span>
      <span class="na">retries</span><span class="pi">:</span> <span class="m">3</span>
    <span class="na">networks</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">mister-smith-net</span>

  <span class="na">orchestrator</span><span class="pi">:</span>
    <span class="na">build</span><span class="pi">:</span>
      <span class="na">context</span><span class="pi">:</span> <span class="s">.</span>
      <span class="na">dockerfile</span><span class="pi">:</span> <span class="s">Dockerfile.orchestrator</span>
      <span class="na">target</span><span class="pi">:</span> <span class="s">runtime</span>
    <span class="na">container_name</span><span class="pi">:</span> <span class="s">mister-smith-orchestrator</span>
    <span class="na">ports</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">8080:8080"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">9090:9090"</span>
    <span class="na">environment</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">RUST_LOG=debug</span>
      <span class="pi">-</span> <span class="s">NATS_URL=nats://nats:4222</span>
      <span class="pi">-</span> <span class="s">REDIS_URL=redis://redis:6379</span>
      <span class="pi">-</span> <span class="s">DATABASE_URL=********************************************/mister_smith</span>
      <span class="pi">-</span> <span class="s">AGENT_TYPE=orchestrator</span>
      <span class="pi">-</span> <span class="s">AGENT_TIER=standard</span>
    <span class="na">depends_on</span><span class="pi">:</span>
      <span class="na">nats</span><span class="pi">:</span>
        <span class="na">condition</span><span class="pi">:</span> <span class="s">service_healthy</span>
      <span class="na">redis</span><span class="pi">:</span>
        <span class="na">condition</span><span class="pi">:</span> <span class="s">service_healthy</span>
      <span class="na">postgres</span><span class="pi">:</span>
        <span class="na">condition</span><span class="pi">:</span> <span class="s">service_healthy</span>
    <span class="na">healthcheck</span><span class="pi">:</span>
      <span class="na">test</span><span class="pi">:</span> <span class="pi">[</span><span class="s2">"</span><span class="s">CMD"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">wget"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">--spider"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">-q"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">http://localhost:8080/health"</span><span class="pi">]</span>
      <span class="na">interval</span><span class="pi">:</span> <span class="s">30s</span>
      <span class="na">timeout</span><span class="pi">:</span> <span class="s">10s</span>
      <span class="na">retries</span><span class="pi">:</span> <span class="m">3</span>
    <span class="na">networks</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">mister-smith-net</span>

  <span class="na">worker</span><span class="pi">:</span>
    <span class="na">build</span><span class="pi">:</span>
      <span class="na">context</span><span class="pi">:</span> <span class="s">.</span>
      <span class="na">dockerfile</span><span class="pi">:</span> <span class="s">Dockerfile.worker</span>
      <span class="na">target</span><span class="pi">:</span> <span class="s">runtime</span>
    <span class="na">environment</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">RUST_LOG=debug</span>
      <span class="pi">-</span> <span class="s">NATS_URL=nats://nats:4222</span>
      <span class="pi">-</span> <span class="s">REDIS_URL=redis://redis:6379</span>
      <span class="pi">-</span> <span class="s">ORCHESTRATOR_URL=http://orchestrator:8080</span>
      <span class="pi">-</span> <span class="s">AGENT_TYPE=worker</span>
      <span class="pi">-</span> <span class="s">AGENT_TIER=standard</span>
      <span class="pi">-</span> <span class="s">WORKER_CONCURRENCY=2</span>
    <span class="na">depends_on</span><span class="pi">:</span>
      <span class="na">orchestrator</span><span class="pi">:</span>
        <span class="na">condition</span><span class="pi">:</span> <span class="s">service_healthy</span>
    <span class="na">healthcheck</span><span class="pi">:</span>
      <span class="na">test</span><span class="pi">:</span> <span class="pi">[</span><span class="s2">"</span><span class="s">CMD"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">curl"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">-f"</span><span class="pi">,</span> <span class="s2">"</span><span class="s">http://localhost:8081/health"</span><span class="pi">]</span>
      <span class="na">interval</span><span class="pi">:</span> <span class="s">15s</span>
      <span class="na">timeout</span><span class="pi">:</span> <span class="s">5s</span>
      <span class="na">retries</span><span class="pi">:</span> <span class="m">3</span>
    <span class="na">networks</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">mister-smith-net</span>
    <span class="na">deploy</span><span class="pi">:</span>
      <span class="na">replicas</span><span class="pi">:</span> <span class="m">2</span>

<span class="na">volumes</span><span class="pi">:</span>
  <span class="na">redis_data</span><span class="pi">:</span>
  <span class="na">postgres_data</span><span class="pi">:</span>

<span class="na">networks</span><span class="pi">:</span>
  <span class="na">mister-smith-net</span><span class="pi">:</span>
    <span class="na">driver</span><span class="pi">:</span> <span class="s">bridge</span>
</code></pre></div></div>

<h4 id="152-development-override-configuration">15.2 Development Override Configuration</h4>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># docker-compose.override.yml - Development-specific overrides</span>
<span class="na">version</span><span class="pi">:</span> <span class="s1">'</span><span class="s">3.8'</span>

<span class="na">services</span><span class="pi">:</span>
  <span class="na">orchestrator</span><span class="pi">:</span>
    <span class="na">volumes</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">./src:/app/src:ro</span>
      <span class="pi">-</span> <span class="s">./target/debug:/app/target/debug</span>
    <span class="na">environment</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">RUST_LOG=debug</span>
      <span class="pi">-</span> <span class="s">RUST_BACKTRACE=1</span>
    <span class="na">ports</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">5005:5005"</span>  <span class="c1"># Debug port</span>

  <span class="na">worker</span><span class="pi">:</span>
    <span class="na">volumes</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">./src:/app/src:ro</span>
      <span class="pi">-</span> <span class="s">./workspace:/app/workspace</span>
    <span class="na">environment</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">RUST_LOG=debug</span>
      <span class="pi">-</span> <span class="s">RUST_BACKTRACE=1</span>
      <span class="pi">-</span> <span class="s">WORKER_CONCURRENCY=1</span>
    <span class="na">ports</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">5006:5006"</span>  <span class="c1"># Debug port</span>
</code></pre></div></div>

<h4 id="153-monitoring-stack-extension">15.3 Monitoring Stack Extension</h4>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># docker-compose.monitoring.yml - Observability stack</span>
<span class="na">version</span><span class="pi">:</span> <span class="s1">'</span><span class="s">3.8'</span>

<span class="na">services</span><span class="pi">:</span>
  <span class="na">prometheus</span><span class="pi">:</span>
    <span class="na">image</span><span class="pi">:</span> <span class="s">prom/prometheus:v2.48.0</span>
    <span class="na">container_name</span><span class="pi">:</span> <span class="s">mister-smith-prometheus</span>
    <span class="na">ports</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">9091:9090"</span>
    <span class="na">volumes</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro</span>
      <span class="pi">-</span> <span class="s">prometheus_data:/prometheus</span>
    <span class="na">command</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s1">'</span><span class="s">--config.file=/etc/prometheus/prometheus.yml'</span>
      <span class="pi">-</span> <span class="s1">'</span><span class="s">--storage.tsdb.path=/prometheus'</span>
      <span class="pi">-</span> <span class="s1">'</span><span class="s">--web.console.libraries=/etc/prometheus/console_libraries'</span>
      <span class="pi">-</span> <span class="s1">'</span><span class="s">--web.console.templates=/etc/prometheus/consoles'</span>
    <span class="na">networks</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">mister-smith-net</span>

  <span class="na">grafana</span><span class="pi">:</span>
    <span class="na">image</span><span class="pi">:</span> <span class="s">grafana/grafana:10.2.0</span>
    <span class="na">container_name</span><span class="pi">:</span> <span class="s">mister-smith-grafana</span>
    <span class="na">ports</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">3000:3000"</span>
    <span class="na">environment</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">GF_SECURITY_ADMIN_PASSWORD=admin</span>
    <span class="na">volumes</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">grafana_data:/var/lib/grafana</span>
      <span class="pi">-</span> <span class="s">./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro</span>
      <span class="pi">-</span> <span class="s">./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro</span>
    <span class="na">networks</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">mister-smith-net</span>

  <span class="na">jaeger</span><span class="pi">:</span>
    <span class="na">image</span><span class="pi">:</span> <span class="s">jaegertracing/all-in-one:1.51</span>
    <span class="na">container_name</span><span class="pi">:</span> <span class="s">mister-smith-jaeger</span>
    <span class="na">ports</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">16686:16686"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">14268:14268"</span>
    <span class="na">environment</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">COLLECTOR_OTLP_ENABLED=true</span>
    <span class="na">networks</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">mister-smith-net</span>

<span class="na">volumes</span><span class="pi">:</span>
  <span class="na">prometheus_data</span><span class="pi">:</span>
  <span class="na">grafana_data</span><span class="pi">:</span>
</code></pre></div></div>

<h3 id="16-kubernetes-manifest-templates">16. Kubernetes Manifest Templates</h3>

<h4 id="161-namespace-definitions">16.1 Namespace Definitions</h4>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># namespaces.yaml - Namespace organization</span>
<span class="na">apiVersion</span><span class="pi">:</span> <span class="s">v1</span>
<span class="na">kind</span><span class="pi">:</span> <span class="s">Namespace</span>
<span class="na">metadata</span><span class="pi">:</span>
  <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-system</span>
  <span class="na">labels</span><span class="pi">:</span>
    <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-system</span>
    <span class="na">tier</span><span class="pi">:</span> <span class="s">system</span>
<span class="nn">---</span>
<span class="na">apiVersion</span><span class="pi">:</span> <span class="s">v1</span>
<span class="na">kind</span><span class="pi">:</span> <span class="s">Namespace</span>
<span class="na">metadata</span><span class="pi">:</span>
  <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-workload</span>
  <span class="na">labels</span><span class="pi">:</span>
    <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-workload</span>
    <span class="na">tier</span><span class="pi">:</span> <span class="s">workload</span>
<span class="nn">---</span>
<span class="na">apiVersion</span><span class="pi">:</span> <span class="s">v1</span>
<span class="na">kind</span><span class="pi">:</span> <span class="s">Namespace</span>
<span class="na">metadata</span><span class="pi">:</span>
  <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-data</span>
  <span class="na">labels</span><span class="pi">:</span>
    <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-data</span>
    <span class="na">tier</span><span class="pi">:</span> <span class="s">data</span>
<span class="nn">---</span>
<span class="na">apiVersion</span><span class="pi">:</span> <span class="s">v1</span>
<span class="na">kind</span><span class="pi">:</span> <span class="s">Namespace</span>
<span class="na">metadata</span><span class="pi">:</span>
  <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-monitoring</span>
  <span class="na">labels</span><span class="pi">:</span>
    <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-monitoring</span>
    <span class="na">tier</span><span class="pi">:</span> <span class="s">monitoring</span>
</code></pre></div></div>

<h4 id="162-orchestrator-deployment-template">16.2 Orchestrator Deployment Template</h4>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># orchestrator-deployment.yaml</span>
<span class="na">apiVersion</span><span class="pi">:</span> <span class="s">apps/v1</span>
<span class="na">kind</span><span class="pi">:</span> <span class="s">Deployment</span>
<span class="na">metadata</span><span class="pi">:</span>
  <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-orchestrator</span>
  <span class="na">namespace</span><span class="pi">:</span> <span class="s">mister-smith-system</span>
  <span class="na">labels</span><span class="pi">:</span>
    <span class="na">app</span><span class="pi">:</span> <span class="s">mister-smith-orchestrator</span>
    <span class="na">component</span><span class="pi">:</span> <span class="s">orchestrator</span>
    <span class="na">tier</span><span class="pi">:</span> <span class="s">system</span>
<span class="na">spec</span><span class="pi">:</span>
  <span class="na">replicas</span><span class="pi">:</span> <span class="m">3</span>
  <span class="na">strategy</span><span class="pi">:</span>
    <span class="na">type</span><span class="pi">:</span> <span class="s">RollingUpdate</span>
    <span class="na">rollingUpdate</span><span class="pi">:</span>
      <span class="na">maxUnavailable</span><span class="pi">:</span> <span class="m">1</span>
      <span class="na">maxSurge</span><span class="pi">:</span> <span class="m">1</span>
  <span class="na">selector</span><span class="pi">:</span>
    <span class="na">matchLabels</span><span class="pi">:</span>
      <span class="na">app</span><span class="pi">:</span> <span class="s">mister-smith-orchestrator</span>
  <span class="na">template</span><span class="pi">:</span>
    <span class="na">metadata</span><span class="pi">:</span>
      <span class="na">labels</span><span class="pi">:</span>
        <span class="na">app</span><span class="pi">:</span> <span class="s">mister-smith-orchestrator</span>
        <span class="na">component</span><span class="pi">:</span> <span class="s">orchestrator</span>
        <span class="na">tier</span><span class="pi">:</span> <span class="s">system</span>
      <span class="na">annotations</span><span class="pi">:</span>
        <span class="na">prometheus.io/scrape</span><span class="pi">:</span> <span class="s2">"</span><span class="s">true"</span>
        <span class="na">prometheus.io/port</span><span class="pi">:</span> <span class="s2">"</span><span class="s">8080"</span>
        <span class="na">prometheus.io/path</span><span class="pi">:</span> <span class="s2">"</span><span class="s">/metrics"</span>
    <span class="na">spec</span><span class="pi">:</span>
      <span class="na">serviceAccountName</span><span class="pi">:</span> <span class="s">mister-smith-orchestrator</span>
      <span class="na">securityContext</span><span class="pi">:</span>
        <span class="na">runAsNonRoot</span><span class="pi">:</span> <span class="kc">true</span>
        <span class="na">runAsUser</span><span class="pi">:</span> <span class="m">1001</span>
        <span class="na">runAsGroup</span><span class="pi">:</span> <span class="m">1001</span>
        <span class="na">fsGroup</span><span class="pi">:</span> <span class="m">1001</span>
      <span class="na">containers</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">orchestrator</span>
        <span class="na">image</span><span class="pi">:</span> <span class="s">mister-smith/orchestrator:latest</span>
        <span class="na">imagePullPolicy</span><span class="pi">:</span> <span class="s">Always</span>
        <span class="na">ports</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">containerPort</span><span class="pi">:</span> <span class="m">8080</span>
          <span class="na">name</span><span class="pi">:</span> <span class="s">http</span>
          <span class="na">protocol</span><span class="pi">:</span> <span class="s">TCP</span>
        <span class="pi">-</span> <span class="na">containerPort</span><span class="pi">:</span> <span class="m">9090</span>
          <span class="na">name</span><span class="pi">:</span> <span class="s">grpc</span>
          <span class="na">protocol</span><span class="pi">:</span> <span class="s">TCP</span>
        <span class="na">env</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">RUST_LOG</span>
          <span class="na">value</span><span class="pi">:</span> <span class="s2">"</span><span class="s">info"</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">AGENT_TYPE</span>
          <span class="na">value</span><span class="pi">:</span> <span class="s2">"</span><span class="s">orchestrator"</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">AGENT_TIER</span>
          <span class="na">value</span><span class="pi">:</span> <span class="s2">"</span><span class="s">standard"</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">NATS_URL</span>
          <span class="na">value</span><span class="pi">:</span> <span class="s2">"</span><span class="s">nats://nats:4222"</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">POD_NAME</span>
          <span class="na">valueFrom</span><span class="pi">:</span>
            <span class="na">fieldRef</span><span class="pi">:</span>
              <span class="na">fieldPath</span><span class="pi">:</span> <span class="s">metadata.name</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">POD_NAMESPACE</span>
          <span class="na">valueFrom</span><span class="pi">:</span>
            <span class="na">fieldRef</span><span class="pi">:</span>
              <span class="na">fieldPath</span><span class="pi">:</span> <span class="s">metadata.namespace</span>
        <span class="na">resources</span><span class="pi">:</span>
          <span class="na">requests</span><span class="pi">:</span>
            <span class="na">memory</span><span class="pi">:</span> <span class="s2">"</span><span class="s">512Mi"</span>
            <span class="na">cpu</span><span class="pi">:</span> <span class="s2">"</span><span class="s">500m"</span>
          <span class="na">limits</span><span class="pi">:</span>
            <span class="na">memory</span><span class="pi">:</span> <span class="s2">"</span><span class="s">1Gi"</span>
            <span class="na">cpu</span><span class="pi">:</span> <span class="s2">"</span><span class="s">1000m"</span>
        <span class="na">livenessProbe</span><span class="pi">:</span>
          <span class="na">httpGet</span><span class="pi">:</span>
            <span class="na">path</span><span class="pi">:</span> <span class="s">/health</span>
            <span class="na">port</span><span class="pi">:</span> <span class="m">8080</span>
          <span class="na">initialDelaySeconds</span><span class="pi">:</span> <span class="m">60</span>
          <span class="na">periodSeconds</span><span class="pi">:</span> <span class="m">30</span>
          <span class="na">timeoutSeconds</span><span class="pi">:</span> <span class="m">10</span>
          <span class="na">failureThreshold</span><span class="pi">:</span> <span class="m">3</span>
        <span class="na">readinessProbe</span><span class="pi">:</span>
          <span class="na">httpGet</span><span class="pi">:</span>
            <span class="na">path</span><span class="pi">:</span> <span class="s">/ready</span>
            <span class="na">port</span><span class="pi">:</span> <span class="m">8080</span>
          <span class="na">initialDelaySeconds</span><span class="pi">:</span> <span class="m">30</span>
          <span class="na">periodSeconds</span><span class="pi">:</span> <span class="m">10</span>
          <span class="na">timeoutSeconds</span><span class="pi">:</span> <span class="m">5</span>
          <span class="na">failureThreshold</span><span class="pi">:</span> <span class="m">3</span>
        <span class="na">startupProbe</span><span class="pi">:</span>
          <span class="na">httpGet</span><span class="pi">:</span>
            <span class="na">path</span><span class="pi">:</span> <span class="s">/startup</span>
            <span class="na">port</span><span class="pi">:</span> <span class="m">8080</span>
          <span class="na">initialDelaySeconds</span><span class="pi">:</span> <span class="m">10</span>
          <span class="na">periodSeconds</span><span class="pi">:</span> <span class="m">10</span>
          <span class="na">timeoutSeconds</span><span class="pi">:</span> <span class="m">5</span>
          <span class="na">failureThreshold</span><span class="pi">:</span> <span class="m">30</span>
        <span class="na">volumeMounts</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">config</span>
          <span class="na">mountPath</span><span class="pi">:</span> <span class="s">/app/config</span>
          <span class="na">readOnly</span><span class="pi">:</span> <span class="kc">true</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">logs</span>
          <span class="na">mountPath</span><span class="pi">:</span> <span class="s">/app/logs</span>
      <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">metrics-sidecar</span>
        <span class="na">image</span><span class="pi">:</span> <span class="s">prom/node-exporter:v1.7.0</span>
        <span class="na">args</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="s1">'</span><span class="s">--path.procfs=/host/proc'</span>
        <span class="pi">-</span> <span class="s1">'</span><span class="s">--path.sysfs=/host/sys'</span>
        <span class="pi">-</span> <span class="s1">'</span><span class="s">--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'</span>
        <span class="na">ports</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">containerPort</span><span class="pi">:</span> <span class="m">9100</span>
          <span class="na">name</span><span class="pi">:</span> <span class="s">metrics</span>
        <span class="na">resources</span><span class="pi">:</span>
          <span class="na">requests</span><span class="pi">:</span>
            <span class="na">memory</span><span class="pi">:</span> <span class="s2">"</span><span class="s">64Mi"</span>
            <span class="na">cpu</span><span class="pi">:</span> <span class="s2">"</span><span class="s">50m"</span>
          <span class="na">limits</span><span class="pi">:</span>
            <span class="na">memory</span><span class="pi">:</span> <span class="s2">"</span><span class="s">128Mi"</span>
            <span class="na">cpu</span><span class="pi">:</span> <span class="s2">"</span><span class="s">100m"</span>
        <span class="na">volumeMounts</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">proc</span>
          <span class="na">mountPath</span><span class="pi">:</span> <span class="s">/host/proc</span>
          <span class="na">readOnly</span><span class="pi">:</span> <span class="kc">true</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">sys</span>
          <span class="na">mountPath</span><span class="pi">:</span> <span class="s">/host/sys</span>
          <span class="na">readOnly</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">volumes</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">config</span>
        <span class="na">configMap</span><span class="pi">:</span>
          <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-orchestrator-config</span>
      <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">logs</span>
        <span class="na">emptyDir</span><span class="pi">:</span> <span class="pi">{}</span>
      <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">proc</span>
        <span class="na">hostPath</span><span class="pi">:</span>
          <span class="na">path</span><span class="pi">:</span> <span class="s">/proc</span>
      <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">sys</span>
        <span class="na">hostPath</span><span class="pi">:</span>
          <span class="na">path</span><span class="pi">:</span> <span class="s">/sys</span>
      <span class="na">affinity</span><span class="pi">:</span>
        <span class="na">podAntiAffinity</span><span class="pi">:</span>
          <span class="na">preferredDuringSchedulingIgnoredDuringExecution</span><span class="pi">:</span>
          <span class="pi">-</span> <span class="na">weight</span><span class="pi">:</span> <span class="m">100</span>
            <span class="na">podAffinityTerm</span><span class="pi">:</span>
              <span class="na">labelSelector</span><span class="pi">:</span>
                <span class="na">matchExpressions</span><span class="pi">:</span>
                <span class="pi">-</span> <span class="na">key</span><span class="pi">:</span> <span class="s">app</span>
                  <span class="na">operator</span><span class="pi">:</span> <span class="s">In</span>
                  <span class="na">values</span><span class="pi">:</span>
                  <span class="pi">-</span> <span class="s">mister-smith-orchestrator</span>
              <span class="na">topologyKey</span><span class="pi">:</span> <span class="s">kubernetes.io/hostname</span>
      <span class="na">tolerations</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="na">key</span><span class="pi">:</span> <span class="s2">"</span><span class="s">node.kubernetes.io/not-ready"</span>
        <span class="na">operator</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Exists"</span>
        <span class="na">effect</span><span class="pi">:</span> <span class="s2">"</span><span class="s">NoExecute"</span>
        <span class="na">tolerationSeconds</span><span class="pi">:</span> <span class="m">300</span>
      <span class="pi">-</span> <span class="na">key</span><span class="pi">:</span> <span class="s2">"</span><span class="s">node.kubernetes.io/unreachable"</span>
        <span class="na">operator</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Exists"</span>
        <span class="na">effect</span><span class="pi">:</span> <span class="s2">"</span><span class="s">NoExecute"</span>
        <span class="na">tolerationSeconds</span><span class="pi">:</span> <span class="m">300</span>
</code></pre></div></div>

<h4 id="163-worker-deployment-with-hpa">16.3 Worker Deployment with HPA</h4>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># worker-deployment.yaml</span>
<span class="na">apiVersion</span><span class="pi">:</span> <span class="s">apps/v1</span>
<span class="na">kind</span><span class="pi">:</span> <span class="s">Deployment</span>
<span class="na">metadata</span><span class="pi">:</span>
  <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-worker</span>
  <span class="na">namespace</span><span class="pi">:</span> <span class="s">mister-smith-workload</span>
  <span class="na">labels</span><span class="pi">:</span>
    <span class="na">app</span><span class="pi">:</span> <span class="s">mister-smith-worker</span>
    <span class="na">component</span><span class="pi">:</span> <span class="s">worker</span>
    <span class="na">tier</span><span class="pi">:</span> <span class="s">workload</span>
<span class="na">spec</span><span class="pi">:</span>
  <span class="na">replicas</span><span class="pi">:</span> <span class="m">2</span>
  <span class="na">strategy</span><span class="pi">:</span>
    <span class="na">type</span><span class="pi">:</span> <span class="s">RollingUpdate</span>
    <span class="na">rollingUpdate</span><span class="pi">:</span>
      <span class="na">maxUnavailable</span><span class="pi">:</span> <span class="m">0</span>
      <span class="na">maxSurge</span><span class="pi">:</span> <span class="m">2</span>
  <span class="na">selector</span><span class="pi">:</span>
    <span class="na">matchLabels</span><span class="pi">:</span>
      <span class="na">app</span><span class="pi">:</span> <span class="s">mister-smith-worker</span>
  <span class="na">template</span><span class="pi">:</span>
    <span class="na">metadata</span><span class="pi">:</span>
      <span class="na">labels</span><span class="pi">:</span>
        <span class="na">app</span><span class="pi">:</span> <span class="s">mister-smith-worker</span>
        <span class="na">component</span><span class="pi">:</span> <span class="s">worker</span>
        <span class="na">tier</span><span class="pi">:</span> <span class="s">workload</span>
      <span class="na">annotations</span><span class="pi">:</span>
        <span class="na">prometheus.io/scrape</span><span class="pi">:</span> <span class="s2">"</span><span class="s">true"</span>
        <span class="na">prometheus.io/port</span><span class="pi">:</span> <span class="s2">"</span><span class="s">8081"</span>
    <span class="na">spec</span><span class="pi">:</span>
      <span class="na">serviceAccountName</span><span class="pi">:</span> <span class="s">mister-smith-worker</span>
      <span class="na">securityContext</span><span class="pi">:</span>
        <span class="na">runAsNonRoot</span><span class="pi">:</span> <span class="kc">true</span>
        <span class="na">runAsUser</span><span class="pi">:</span> <span class="m">1001</span>
        <span class="na">runAsGroup</span><span class="pi">:</span> <span class="m">1001</span>
        <span class="na">fsGroup</span><span class="pi">:</span> <span class="m">1001</span>
      <span class="na">containers</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">worker</span>
        <span class="na">image</span><span class="pi">:</span> <span class="s">mister-smith/worker:latest</span>
        <span class="na">imagePullPolicy</span><span class="pi">:</span> <span class="s">Always</span>
        <span class="na">ports</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">containerPort</span><span class="pi">:</span> <span class="m">8081</span>
          <span class="na">name</span><span class="pi">:</span> <span class="s">http</span>
        <span class="na">env</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">RUST_LOG</span>
          <span class="na">value</span><span class="pi">:</span> <span class="s2">"</span><span class="s">info"</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">AGENT_TYPE</span>
          <span class="na">value</span><span class="pi">:</span> <span class="s2">"</span><span class="s">worker"</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">AGENT_TIER</span>
          <span class="na">valueFrom</span><span class="pi">:</span>
            <span class="na">configMapKeyRef</span><span class="pi">:</span>
              <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-worker-config</span>
              <span class="na">key</span><span class="pi">:</span> <span class="s">agent.tier</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">NATS_URL</span>
          <span class="na">value</span><span class="pi">:</span> <span class="s2">"</span><span class="s">nats://nats.mister-smith-system:4222"</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">WORKER_CONCURRENCY</span>
          <span class="na">valueFrom</span><span class="pi">:</span>
            <span class="na">configMapKeyRef</span><span class="pi">:</span>
              <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-worker-config</span>
              <span class="na">key</span><span class="pi">:</span> <span class="s">worker.concurrency</span>
        <span class="na">resources</span><span class="pi">:</span>
          <span class="na">requests</span><span class="pi">:</span>
            <span class="na">memory</span><span class="pi">:</span> <span class="s2">"</span><span class="s">256Mi"</span>
            <span class="na">cpu</span><span class="pi">:</span> <span class="s2">"</span><span class="s">250m"</span>
          <span class="na">limits</span><span class="pi">:</span>
            <span class="na">memory</span><span class="pi">:</span> <span class="s2">"</span><span class="s">512Mi"</span>
            <span class="na">cpu</span><span class="pi">:</span> <span class="s2">"</span><span class="s">500m"</span>
        <span class="na">livenessProbe</span><span class="pi">:</span>
          <span class="na">httpGet</span><span class="pi">:</span>
            <span class="na">path</span><span class="pi">:</span> <span class="s">/health</span>
            <span class="na">port</span><span class="pi">:</span> <span class="m">8081</span>
          <span class="na">initialDelaySeconds</span><span class="pi">:</span> <span class="m">30</span>
          <span class="na">periodSeconds</span><span class="pi">:</span> <span class="m">15</span>
          <span class="na">timeoutSeconds</span><span class="pi">:</span> <span class="m">5</span>
          <span class="na">failureThreshold</span><span class="pi">:</span> <span class="m">3</span>
        <span class="na">readinessProbe</span><span class="pi">:</span>
          <span class="na">httpGet</span><span class="pi">:</span>
            <span class="na">path</span><span class="pi">:</span> <span class="s">/ready</span>
            <span class="na">port</span><span class="pi">:</span> <span class="m">8081</span>
          <span class="na">initialDelaySeconds</span><span class="pi">:</span> <span class="m">15</span>
          <span class="na">periodSeconds</span><span class="pi">:</span> <span class="m">5</span>
          <span class="na">timeoutSeconds</span><span class="pi">:</span> <span class="m">3</span>
          <span class="na">failureThreshold</span><span class="pi">:</span> <span class="m">3</span>
        <span class="na">startupProbe</span><span class="pi">:</span>
          <span class="na">httpGet</span><span class="pi">:</span>
            <span class="na">path</span><span class="pi">:</span> <span class="s">/startup</span>
            <span class="na">port</span><span class="pi">:</span> <span class="m">8081</span>
          <span class="na">initialDelaySeconds</span><span class="pi">:</span> <span class="m">5</span>
          <span class="na">periodSeconds</span><span class="pi">:</span> <span class="m">5</span>
          <span class="na">timeoutSeconds</span><span class="pi">:</span> <span class="m">3</span>
          <span class="na">failureThreshold</span><span class="pi">:</span> <span class="m">20</span>
        <span class="na">volumeMounts</span><span class="pi">:</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">workspace</span>
          <span class="na">mountPath</span><span class="pi">:</span> <span class="s">/app/workspace</span>
        <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">temp</span>
          <span class="na">mountPath</span><span class="pi">:</span> <span class="s">/tmp</span>
      <span class="na">volumes</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">workspace</span>
        <span class="na">emptyDir</span><span class="pi">:</span>
          <span class="na">sizeLimit</span><span class="pi">:</span> <span class="s">1Gi</span>
      <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">temp</span>
        <span class="na">emptyDir</span><span class="pi">:</span>
          <span class="na">sizeLimit</span><span class="pi">:</span> <span class="s">512Mi</span>
<span class="nn">---</span>
<span class="na">apiVersion</span><span class="pi">:</span> <span class="s">autoscaling/v2</span>
<span class="na">kind</span><span class="pi">:</span> <span class="s">HorizontalPodAutoscaler</span>
<span class="na">metadata</span><span class="pi">:</span>
  <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-worker-hpa</span>
  <span class="na">namespace</span><span class="pi">:</span> <span class="s">mister-smith-workload</span>
<span class="na">spec</span><span class="pi">:</span>
  <span class="na">scaleTargetRef</span><span class="pi">:</span>
    <span class="na">apiVersion</span><span class="pi">:</span> <span class="s">apps/v1</span>
    <span class="na">kind</span><span class="pi">:</span> <span class="s">Deployment</span>
    <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-worker</span>
  <span class="na">minReplicas</span><span class="pi">:</span> <span class="m">2</span>
  <span class="na">maxReplicas</span><span class="pi">:</span> <span class="m">50</span>
  <span class="na">metrics</span><span class="pi">:</span>
  <span class="pi">-</span> <span class="na">type</span><span class="pi">:</span> <span class="s">Resource</span>
    <span class="na">resource</span><span class="pi">:</span>
      <span class="na">name</span><span class="pi">:</span> <span class="s">cpu</span>
      <span class="na">target</span><span class="pi">:</span>
        <span class="na">type</span><span class="pi">:</span> <span class="s">Utilization</span>
        <span class="na">averageUtilization</span><span class="pi">:</span> <span class="m">70</span>
  <span class="pi">-</span> <span class="na">type</span><span class="pi">:</span> <span class="s">Resource</span>
    <span class="na">resource</span><span class="pi">:</span>
      <span class="na">name</span><span class="pi">:</span> <span class="s">memory</span>
      <span class="na">target</span><span class="pi">:</span>
        <span class="na">type</span><span class="pi">:</span> <span class="s">Utilization</span>
        <span class="na">averageUtilization</span><span class="pi">:</span> <span class="m">80</span>
  <span class="pi">-</span> <span class="na">type</span><span class="pi">:</span> <span class="s">Pods</span>
    <span class="na">pods</span><span class="pi">:</span>
      <span class="na">metric</span><span class="pi">:</span>
        <span class="na">name</span><span class="pi">:</span> <span class="s">pending_tasks</span>
      <span class="na">target</span><span class="pi">:</span>
        <span class="na">type</span><span class="pi">:</span> <span class="s">AverageValue</span>
        <span class="na">averageValue</span><span class="pi">:</span> <span class="s2">"</span><span class="s">100"</span>
  <span class="na">behavior</span><span class="pi">:</span>
    <span class="na">scaleUp</span><span class="pi">:</span>
      <span class="na">stabilizationWindowSeconds</span><span class="pi">:</span> <span class="m">60</span>
      <span class="na">policies</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="na">type</span><span class="pi">:</span> <span class="s">Percent</span>
        <span class="na">value</span><span class="pi">:</span> <span class="m">100</span>
        <span class="na">periodSeconds</span><span class="pi">:</span> <span class="m">15</span>
      <span class="pi">-</span> <span class="na">type</span><span class="pi">:</span> <span class="s">Pods</span>
        <span class="na">value</span><span class="pi">:</span> <span class="m">4</span>
        <span class="na">periodSeconds</span><span class="pi">:</span> <span class="m">15</span>
      <span class="na">selectPolicy</span><span class="pi">:</span> <span class="s">Max</span>
    <span class="na">scaleDown</span><span class="pi">:</span>
      <span class="na">stabilizationWindowSeconds</span><span class="pi">:</span> <span class="m">300</span>
      <span class="na">policies</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="na">type</span><span class="pi">:</span> <span class="s">Percent</span>
        <span class="na">value</span><span class="pi">:</span> <span class="m">50</span>
        <span class="na">periodSeconds</span><span class="pi">:</span> <span class="m">60</span>
      <span class="na">selectPolicy</span><span class="pi">:</span> <span class="s">Min</span>
</code></pre></div></div>

<h3 id="17-environment-variable-naming-conventions">17. Environment Variable Naming Conventions</h3>

<h4 id="171-core-environment-variables">17.1 Core Environment Variables</h4>
<div class="language-bash highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c"># Core Agent Configuration</span>
<span class="nv">AGENT_TYPE</span><span class="o">=</span>orchestrator|worker|messaging
<span class="nv">AGENT_TIER</span><span class="o">=</span>minimal|standard|premium
<span class="nv">AGENT_ID</span><span class="o">=</span><span class="k">${</span><span class="nv">POD_NAME</span><span class="k">}</span>  <span class="c"># Auto-populated in Kubernetes</span>

<span class="c"># Claude CLI Integration</span>
<span class="nv">CLAUDE_PARALLEL_DEFAULT</span><span class="o">=</span>4
<span class="nv">CLAUDE_PARALLEL_MAX_AGENTS</span><span class="o">=</span>50
<span class="nv">CLAUDE_PARALLEL_CPU_BUDGET</span><span class="o">=</span><span class="s2">"4.0"</span>
<span class="nv">CLAUDE_PARALLEL_MEMORY_BUDGET</span><span class="o">=</span><span class="s2">"4Gi"</span>

<span class="c"># Messaging Configuration</span>
<span class="nv">NATS_URL</span><span class="o">=</span>nats://nats.mister-smith-system:4222
<span class="nv">NATS_CLUSTER_ID</span><span class="o">=</span>mister-smith-cluster
<span class="nv">NATS_CLIENT_ID</span><span class="o">=</span><span class="k">${</span><span class="nv">AGENT_TYPE</span><span class="k">}</span>-<span class="k">${</span><span class="nv">POD_NAME</span><span class="k">}</span>
<span class="nv">NATS_SUBJECT_PREFIX</span><span class="o">=</span>mister-smith

<span class="c"># Persistence Configuration</span>
<span class="nv">REDIS_URL</span><span class="o">=</span>redis://redis.mister-smith-data:6379
<span class="nv">DATABASE_URL</span><span class="o">=</span>postgresql://postgres:<EMAIL>-smith-data:5432/mister_smith

<span class="c"># Observability Configuration</span>
<span class="nv">METRICS_ENABLED</span><span class="o">=</span><span class="nb">true
</span><span class="nv">METRICS_PORT</span><span class="o">=</span>9090
<span class="nv">TRACING_ENABLED</span><span class="o">=</span><span class="nb">true
</span><span class="nv">JAEGER_ENDPOINT</span><span class="o">=</span>http://jaeger.mister-smith-monitoring:14268/api/traces

<span class="c"># Logging Configuration</span>
<span class="nv">RUST_LOG</span><span class="o">=</span>info
<span class="nv">LOG_FORMAT</span><span class="o">=</span>json
<span class="nv">LOG_LEVEL</span><span class="o">=</span>info

<span class="c"># Security Configuration</span>
<span class="nv">TLS_ENABLED</span><span class="o">=</span><span class="nb">true
</span><span class="nv">MTLS_ENABLED</span><span class="o">=</span><span class="nb">true
</span><span class="nv">CERT_PATH</span><span class="o">=</span>/etc/tls/certs
<span class="nv">KEY_PATH</span><span class="o">=</span>/etc/tls/private

<span class="c"># Resource Management</span>
<span class="nv">WORKER_CONCURRENCY</span><span class="o">=</span>4
<span class="nv">WORKER_TIMEOUT_SECONDS</span><span class="o">=</span>300
<span class="nv">ORCHESTRATOR_HEARTBEAT_INTERVAL</span><span class="o">=</span>30
<span class="nv">HEALTH_CHECK_INTERVAL</span><span class="o">=</span>15

<span class="c"># Development Configuration (override files only)</span>
<span class="nv">RUST_BACKTRACE</span><span class="o">=</span>1
<span class="nv">DEBUG_MODE</span><span class="o">=</span><span class="nb">true
</span><span class="nv">HOT_RELOAD_ENABLED</span><span class="o">=</span><span class="nb">true</span>
</code></pre></div></div>

<h3 id="18-resource-allocation-specifications">18. Resource Allocation Specifications</h3>

<h4 id="181-resource-tier-definitions">18.1 Resource Tier Definitions</h4>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># resource-tiers.yaml</span>
<span class="na">apiVersion</span><span class="pi">:</span> <span class="s">v1</span>
<span class="na">kind</span><span class="pi">:</span> <span class="s">ConfigMap</span>
<span class="na">metadata</span><span class="pi">:</span>
  <span class="na">name</span><span class="pi">:</span> <span class="s">mister-smith-resource-tiers</span>
  <span class="na">namespace</span><span class="pi">:</span> <span class="s">mister-smith-system</span>
<span class="na">data</span><span class="pi">:</span>
  <span class="na">minimal.yaml</span><span class="pi">:</span> <span class="pi">|</span>
    <span class="s">resources:</span>
      <span class="s">requests:</span>
        <span class="s">memory: "128Mi"</span>
        <span class="s">cpu: "100m"</span>
      <span class="s">limits:</span>
        <span class="s">memory: "256Mi"</span>
        <span class="s">cpu: "250m"</span>
    <span class="s">priorityClass: "best-effort"</span>
    
  <span class="na">standard.yaml</span><span class="pi">:</span> <span class="pi">|</span>
    <span class="s">resources:</span>
      <span class="s">requests:</span>
        <span class="s">memory: "512Mi"</span>
        <span class="s">cpu: "500m"</span>
      <span class="s">limits:</span>
        <span class="s">memory: "1Gi"</span>
        <span class="s">cpu: "1000m"</span>
    <span class="s">priorityClass: "normal"</span>
    
  <span class="na">premium.yaml</span><span class="pi">:</span> <span class="pi">|</span>
    <span class="s">resources:</span>
      <span class="s">requests:</span>
        <span class="s">memory: "2Gi"</span>
        <span class="s">cpu: "2000m"</span>
      <span class="s">limits:</span>
        <span class="s">memory: "4Gi"</span>
        <span class="s">cpu: "4000m"</span>
    <span class="s">priorityClass: "high"</span>
<span class="s">---</span>
<span class="na">apiVersion</span><span class="pi">:</span> <span class="s">scheduling.k8s.io/v1</span>
<span class="na">kind</span><span class="pi">:</span> <span class="s">PriorityClass</span>
<span class="na">metadata</span><span class="pi">:</span>
  <span class="na">name</span><span class="pi">:</span> <span class="s">best-effort</span>
<span class="na">value</span><span class="pi">:</span> <span class="m">100</span>
<span class="na">globalDefault</span><span class="pi">:</span> <span class="kc">false</span>
<span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Best</span><span class="nv"> </span><span class="s">effort</span><span class="nv"> </span><span class="s">priority</span><span class="nv"> </span><span class="s">class</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">minimal</span><span class="nv"> </span><span class="s">tier"</span>
<span class="nn">---</span>
<span class="na">apiVersion</span><span class="pi">:</span> <span class="s">scheduling.k8s.io/v1</span>
<span class="na">kind</span><span class="pi">:</span> <span class="s">PriorityClass</span>
<span class="na">metadata</span><span class="pi">:</span>
  <span class="na">name</span><span class="pi">:</span> <span class="s">normal</span>
<span class="na">value</span><span class="pi">:</span> <span class="m">500</span>
<span class="na">globalDefault</span><span class="pi">:</span> <span class="kc">true</span>
<span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Normal</span><span class="nv"> </span><span class="s">priority</span><span class="nv"> </span><span class="s">class</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">standard</span><span class="nv"> </span><span class="s">tier"</span>
<span class="nn">---</span>
<span class="na">apiVersion</span><span class="pi">:</span> <span class="s">scheduling.k8s.io/v1</span>
<span class="na">kind</span><span class="pi">:</span> <span class="s">PriorityClass</span>
<span class="na">metadata</span><span class="pi">:</span>
  <span class="na">name</span><span class="pi">:</span> <span class="s">high</span>
<span class="na">value</span><span class="pi">:</span> <span class="m">1000</span>
<span class="na">globalDefault</span><span class="pi">:</span> <span class="kc">false</span>
<span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">High</span><span class="nv"> </span><span class="s">priority</span><span class="nv"> </span><span class="s">class</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">premium</span><span class="nv"> </span><span class="s">tier"</span>
</code></pre></div></div>

<h3 id="19-health-check-implementation-templates">19. Health Check Implementation Templates</h3>

<h4 id="191-application-health-check-server-rust">19.1 Application Health Check Server (Rust)</h4>
<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// health.rs - Health check implementation</span>
<span class="k">use</span> <span class="nn">axum</span><span class="p">::{</span>
    <span class="nn">extract</span><span class="p">::</span><span class="n">State</span><span class="p">,</span>
    <span class="nn">http</span><span class="p">::</span><span class="n">StatusCode</span><span class="p">,</span>
    <span class="nn">response</span><span class="p">::</span><span class="n">Json</span><span class="p">,</span>
    <span class="nn">routing</span><span class="p">::</span><span class="n">get</span><span class="p">,</span>
    <span class="n">Router</span><span class="p">,</span>
<span class="p">};</span>
<span class="k">use</span> <span class="nn">serde_json</span><span class="p">::{</span><span class="n">json</span><span class="p">,</span> <span class="n">Value</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">sync</span><span class="p">::</span><span class="nb">Arc</span><span class="p">;</span>
<span class="k">use</span> <span class="nn">tokio</span><span class="p">::</span><span class="nn">time</span><span class="p">::{</span><span class="n">Duration</span><span class="p">,</span> <span class="n">Instant</span><span class="p">};</span>

<span class="nd">#[derive(Clone)]</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">HealthState</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="n">start_time</span><span class="p">:</span> <span class="n">Instant</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">nats_connected</span><span class="p">:</span> <span class="nb">Arc</span><span class="o">&lt;</span><span class="nn">tokio</span><span class="p">::</span><span class="nn">sync</span><span class="p">::</span><span class="n">RwLock</span><span class="o">&lt;</span><span class="nb">bool</span><span class="o">&gt;&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">redis_connected</span><span class="p">:</span> <span class="nb">Arc</span><span class="o">&lt;</span><span class="nn">tokio</span><span class="p">::</span><span class="nn">sync</span><span class="p">::</span><span class="n">RwLock</span><span class="o">&lt;</span><span class="nb">bool</span><span class="o">&gt;&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">db_connected</span><span class="p">:</span> <span class="nb">Arc</span><span class="o">&lt;</span><span class="nn">tokio</span><span class="p">::</span><span class="nn">sync</span><span class="p">::</span><span class="n">RwLock</span><span class="o">&lt;</span><span class="nb">bool</span><span class="o">&gt;&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">HealthState</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">new</span><span class="p">()</span> <span class="k">-&gt;</span> <span class="k">Self</span> <span class="p">{</span>
        <span class="k">Self</span> <span class="p">{</span>
            <span class="n">start_time</span><span class="p">:</span> <span class="nn">Instant</span><span class="p">::</span><span class="nf">now</span><span class="p">(),</span>
            <span class="n">nats_connected</span><span class="p">:</span> <span class="nn">Arc</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="nn">tokio</span><span class="p">::</span><span class="nn">sync</span><span class="p">::</span><span class="nn">RwLock</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="k">false</span><span class="p">)),</span>
            <span class="n">redis_connected</span><span class="p">:</span> <span class="nn">Arc</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="nn">tokio</span><span class="p">::</span><span class="nn">sync</span><span class="p">::</span><span class="nn">RwLock</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="k">false</span><span class="p">)),</span>
            <span class="n">db_connected</span><span class="p">:</span> <span class="nn">Arc</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="nn">tokio</span><span class="p">::</span><span class="nn">sync</span><span class="p">::</span><span class="nn">RwLock</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="k">false</span><span class="p">)),</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="k">pub</span> <span class="k">fn</span> <span class="nf">health_router</span><span class="p">()</span> <span class="k">-&gt;</span> <span class="n">Router</span><span class="o">&lt;</span><span class="n">HealthState</span><span class="o">&gt;</span> <span class="p">{</span>
    <span class="nn">Router</span><span class="p">::</span><span class="nf">new</span><span class="p">()</span>
        <span class="nf">.route</span><span class="p">(</span><span class="s">"/health"</span><span class="p">,</span> <span class="nf">get</span><span class="p">(</span><span class="n">health_check</span><span class="p">))</span>
        <span class="nf">.route</span><span class="p">(</span><span class="s">"/ready"</span><span class="p">,</span> <span class="nf">get</span><span class="p">(</span><span class="n">readiness_check</span><span class="p">))</span>
        <span class="nf">.route</span><span class="p">(</span><span class="s">"/startup"</span><span class="p">,</span> <span class="nf">get</span><span class="p">(</span><span class="n">startup_check</span><span class="p">))</span>
        <span class="nf">.route</span><span class="p">(</span><span class="s">"/metrics"</span><span class="p">,</span> <span class="nf">get</span><span class="p">(</span><span class="n">metrics_endpoint</span><span class="p">))</span>
<span class="p">}</span>

<span class="k">async</span> <span class="k">fn</span> <span class="nf">health_check</span><span class="p">(</span><span class="nf">State</span><span class="p">(</span><span class="n">state</span><span class="p">):</span> <span class="n">State</span><span class="o">&lt;</span><span class="n">HealthState</span><span class="o">&gt;</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Json</span><span class="o">&lt;</span><span class="n">Value</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">StatusCode</span><span class="o">&gt;</span> <span class="p">{</span>
    <span class="k">let</span> <span class="n">uptime</span> <span class="o">=</span> <span class="n">state</span><span class="py">.start_time</span><span class="nf">.elapsed</span><span class="p">()</span><span class="nf">.as_secs</span><span class="p">();</span>
    
    <span class="nf">Ok</span><span class="p">(</span><span class="nf">Json</span><span class="p">(</span><span class="nd">json!</span><span class="p">({</span>
        <span class="s">"status"</span><span class="p">:</span> <span class="s">"healthy"</span><span class="p">,</span>
        <span class="s">"uptime_seconds"</span><span class="p">:</span> <span class="n">uptime</span><span class="p">,</span>
        <span class="s">"timestamp"</span><span class="p">:</span> <span class="nn">chrono</span><span class="p">::</span><span class="nn">Utc</span><span class="p">::</span><span class="nf">now</span><span class="p">()</span><span class="nf">.to_rfc3339</span><span class="p">(),</span>
        <span class="s">"version"</span><span class="p">:</span> <span class="nd">env!</span><span class="p">(</span><span class="s">"CARGO_PKG_VERSION"</span><span class="p">),</span>
        <span class="s">"agent_type"</span><span class="p">:</span> <span class="nn">std</span><span class="p">::</span><span class="nn">env</span><span class="p">::</span><span class="nf">var</span><span class="p">(</span><span class="s">"AGENT_TYPE"</span><span class="p">)</span><span class="nf">.unwrap_or_default</span><span class="p">()</span>
    <span class="p">})))</span>
<span class="p">}</span>

<span class="k">async</span> <span class="k">fn</span> <span class="nf">readiness_check</span><span class="p">(</span><span class="nf">State</span><span class="p">(</span><span class="n">state</span><span class="p">):</span> <span class="n">State</span><span class="o">&lt;</span><span class="n">HealthState</span><span class="o">&gt;</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Json</span><span class="o">&lt;</span><span class="n">Value</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">StatusCode</span><span class="o">&gt;</span> <span class="p">{</span>
    <span class="k">let</span> <span class="n">nats_ok</span> <span class="o">=</span> <span class="o">*</span><span class="n">state</span><span class="py">.nats_connected</span><span class="nf">.read</span><span class="p">()</span><span class="k">.await</span><span class="p">;</span>
    <span class="k">let</span> <span class="n">redis_ok</span> <span class="o">=</span> <span class="o">*</span><span class="n">state</span><span class="py">.redis_connected</span><span class="nf">.read</span><span class="p">()</span><span class="k">.await</span><span class="p">;</span>
    <span class="k">let</span> <span class="n">db_ok</span> <span class="o">=</span> <span class="o">*</span><span class="n">state</span><span class="py">.db_connected</span><span class="nf">.read</span><span class="p">()</span><span class="k">.await</span><span class="p">;</span>
    
    <span class="k">let</span> <span class="n">ready</span> <span class="o">=</span> <span class="n">nats_ok</span> <span class="o">&amp;&amp;</span> <span class="n">redis_ok</span> <span class="o">&amp;&amp;</span> <span class="n">db_ok</span><span class="p">;</span>
    
    <span class="k">let</span> <span class="n">response</span> <span class="o">=</span> <span class="nd">json!</span><span class="p">({</span>
        <span class="s">"ready"</span><span class="p">:</span> <span class="n">ready</span><span class="p">,</span>
        <span class="s">"checks"</span><span class="p">:</span> <span class="p">{</span>
            <span class="s">"nats"</span><span class="p">:</span> <span class="n">nats_ok</span><span class="p">,</span>
            <span class="s">"redis"</span><span class="p">:</span> <span class="n">redis_ok</span><span class="p">,</span>
            <span class="s">"database"</span><span class="p">:</span> <span class="n">db_ok</span>
        <span class="p">},</span>
        <span class="s">"timestamp"</span><span class="p">:</span> <span class="nn">chrono</span><span class="p">::</span><span class="nn">Utc</span><span class="p">::</span><span class="nf">now</span><span class="p">()</span><span class="nf">.to_rfc3339</span><span class="p">()</span>
    <span class="p">});</span>
    
    <span class="k">if</span> <span class="n">ready</span> <span class="p">{</span>
        <span class="nf">Ok</span><span class="p">(</span><span class="nf">Json</span><span class="p">(</span><span class="n">response</span><span class="p">))</span>
    <span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
        <span class="nf">Err</span><span class="p">(</span><span class="nn">StatusCode</span><span class="p">::</span><span class="n">SERVICE_UNAVAILABLE</span><span class="p">)</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="k">async</span> <span class="k">fn</span> <span class="nf">startup_check</span><span class="p">(</span><span class="nf">State</span><span class="p">(</span><span class="n">state</span><span class="p">):</span> <span class="n">State</span><span class="o">&lt;</span><span class="n">HealthState</span><span class="o">&gt;</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Json</span><span class="o">&lt;</span><span class="n">Value</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">StatusCode</span><span class="o">&gt;</span> <span class="p">{</span>
    <span class="k">let</span> <span class="n">uptime</span> <span class="o">=</span> <span class="n">state</span><span class="py">.start_time</span><span class="nf">.elapsed</span><span class="p">();</span>
    <span class="k">let</span> <span class="n">startup_complete</span> <span class="o">=</span> <span class="n">uptime</span> <span class="o">&gt;</span> <span class="nn">Duration</span><span class="p">::</span><span class="nf">from_secs</span><span class="p">(</span><span class="mi">10</span><span class="p">);</span>
    
    <span class="k">let</span> <span class="n">response</span> <span class="o">=</span> <span class="nd">json!</span><span class="p">({</span>
        <span class="s">"started"</span><span class="p">:</span> <span class="n">startup_complete</span><span class="p">,</span>
        <span class="s">"uptime_seconds"</span><span class="p">:</span> <span class="n">uptime</span><span class="nf">.as_secs</span><span class="p">(),</span>
        <span class="s">"timestamp"</span><span class="p">:</span> <span class="nn">chrono</span><span class="p">::</span><span class="nn">Utc</span><span class="p">::</span><span class="nf">now</span><span class="p">()</span><span class="nf">.to_rfc3339</span><span class="p">()</span>
    <span class="p">});</span>
    
    <span class="k">if</span> <span class="n">startup_complete</span> <span class="p">{</span>
        <span class="nf">Ok</span><span class="p">(</span><span class="nf">Json</span><span class="p">(</span><span class="n">response</span><span class="p">))</span>
    <span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
        <span class="nf">Err</span><span class="p">(</span><span class="nn">StatusCode</span><span class="p">::</span><span class="n">SERVICE_UNAVAILABLE</span><span class="p">)</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="k">async</span> <span class="k">fn</span> <span class="nf">metrics_endpoint</span><span class="p">()</span> <span class="k">-&gt;</span> <span class="n">Json</span><span class="o">&lt;</span><span class="n">Value</span><span class="o">&gt;</span> <span class="p">{</span>
    <span class="c1">// Prometheus metrics would go here</span>
    <span class="nf">Json</span><span class="p">(</span><span class="nd">json!</span><span class="p">({</span>
        <span class="s">"metrics_endpoint"</span><span class="p">:</span> <span class="s">"/metrics"</span><span class="p">,</span>
        <span class="s">"format"</span><span class="p">:</span> <span class="s">"prometheus"</span>
    <span class="p">}))</span>
<span class="p">}</span>
</code></pre></div></div>

<hr />

<p>This deployment architecture specification provides comprehensive patterns and concrete implementation templates for deploying multi-agent systems at scale, including Docker best practices, Kubernetes manifests, and environment management suitable for production deployment of the Mister Smith AI Agent Framework.</p>

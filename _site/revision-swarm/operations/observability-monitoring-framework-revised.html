<h1 id="observability--monitoring-framework---revised">Observability &amp; Monitoring Framework - Revised</h1>
<h2 id="multi-agent-systems-monitoring-patterns">Multi-Agent Systems Monitoring Patterns</h2>

<h3 id="1-architecture-overview">1. Architecture Overview</h3>

<h4 id="11-core-observability-components-pattern">1.1 Core Observability Components Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ObservabilityArchitecture:
    COMPONENTS:
        instrumentation_layer
        collection_pipeline
        storage_backend
        analysis_engine
        visualization_layer
        alerting_system
</code></pre>

<h4 id="12-data-flow-pattern">1.2 Data Flow Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ObservabilityPipeline:
    STAGES:
        agent_instrumentation
        local_buffering
        centralized_collection
        processing_enrichment
        persistent_storage
        real_time_analysis
        user_presentation
</code></pre>

<h3 id="2-instrumentation-patterns">2. Instrumentation Patterns</h3>

<h4 id="21-agent-instrumentation-pattern">2.1 Agent Instrumentation Pattern</h4>
<pre><code class="language-pseudocode">PATTERN AgentInstrumentation:
    initialize_telemetry_context()
    configure_data_exporters()
    set_resource_attributes()
    enable_context_propagation()
    configure_sampling_strategy()
</code></pre>

<h4 id="22-context-propagation-pattern">2.2 Context Propagation Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ContextPropagation:
    inject_trace_context(outgoing_message)
    extract_trace_context(incoming_message)
    maintain_correlation_chain()
    preserve_causality_information()
</code></pre>

<h3 id="3-distributed-tracing-patterns">3. Distributed Tracing Patterns</h3>

<h4 id="31-trace-structure-pattern">3.1 Trace Structure Pattern</h4>
<pre><code class="language-pseudocode">PATTERN TraceStructure:
    COMPONENTS:
        trace_identifier
        span_collection
        timing_information
        status_indicators
        attribute_metadata
        event_markers
        relationship_links
</code></pre>

<h4 id="32-sampling-strategy-pattern">3.2 Sampling Strategy Pattern</h4>
<pre><code class="language-pseudocode">PATTERN AdaptiveSampling:
    DECISION_FACTORS:
        operation_criticality
        system_load_level
        error_presence
        latency_threshold
        business_importance
    
    SAMPLING_LOGIC:
        IF critical_operation: ALWAYS_SAMPLE
        ELIF error_detected: ALWAYS_SAMPLE
        ELIF high_load: MINIMAL_SAMPLING
        ELSE: ADAPTIVE_RATE
</code></pre>

<h4 id="33-cross-agent-tracing-pattern">3.3 Cross-Agent Tracing Pattern</h4>
<pre><code class="language-pseudocode">PATTERN CrossAgentTracing:
    create_client_span(source_agent)
    inject_context_headers(message)
    transmit_to_target(message)
    extract_context_on_receive()
    create_server_span(target_agent)
    link_span_hierarchy()
</code></pre>

<h4 id="34-trace-layers-pattern">3.4 Trace Layers Pattern</h4>
<pre><code class="language-pseudocode">PATTERN TraceLayers:
    CHILD_PROCESS_TRACING:
        spawn_agent_with_piped_output()
        create_tracing_span(agent_id)
        capture_stdout_to_tracing()
        capture_stderr_to_tracing()
        correlate_process_lifecycle()

    CONTEXT_PROPAGATION:
        inject_trace_headers(outgoing_message)
        extract_trace_headers(incoming_message)
        maintain_span_relationships()
        preserve_baggage_items()
</code></pre>

<h4 id="35-claude-cli-parallel-agent-tracing-pattern">3.5 Claude-CLI Parallel Agent Tracing Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ClaudeCLIParallelTracing:
    PARALLEL_AGENT_INSTRUMENTATION:
        # Handle multiple internal agents sharing one PID
        create_root_span(claude_cli_process_id)

        FOR each_internal_agent IN parallel_agents:
            create_child_span(internal_agent_id)
            add_span_attribute("agent_internal_id", internal_agent_id)
            add_span_attribute("agent_type", "task_patch_agent")
            add_span_attribute("parallel_group", parallel_execution_id)

        # Parse and correlate output lines
        parse_output_line("Task(Patch Agent &lt;n&gt; ...)")
        correlate_to_span(agent_internal_id)

    LOG_DISAMBIGUATION:
        # Multiple internal agents share PID but need separate traces
        span_attributes:
            agent_internal_id: "unique_identifier_per_internal_agent"
            process_id: "shared_claude_cli_pid"
            parallel_execution_id: "batch_identifier"
            agent_role: "task_agent" | "patch_agent"

    TRACE_CORRELATION:
        # Link parallel agent spans to parent orchestration
        parent_span: "orchestrator_task_span"
        child_spans: ["agent_0_span", "agent_1_span", ..., "agent_n_span"]
        correlation_key: "parallel_execution_batch_id"
</code></pre>

<h4 id="36-claude-cli-hook-system-tracing-pattern">3.6 Claude-CLI Hook System Tracing Pattern</h4>
<pre><code class="language-pseudocode">PATTERN HookSystemTracing:
    HOOK_EXECUTION_SPANS:
        # Create spans for each hook execution
        create_hook_span(hook_name, hook_type)
        add_span_attribute("hook_name", hook_name)
        add_span_attribute("hook_type", hook_type)  # startup, pre_task, post_task, on_error, on_file_change
        add_span_attribute("hook_outcome", outcome)  # success, failure, timeout
        add_span_attribute("hook_latency_ms", execution_time)
        add_span_attribute("hook_script_path", script_path)

    HOOK_LIFECYCLE_TRACING:
        # Trace complete hook lifecycle
        start_span("hook_execution")
        record_event("hook_started", {"hook_type": hook_type})

        # Execute hook and capture metrics
        execution_start = current_timestamp()
        hook_result = execute_hook_script(hook_payload)
        execution_end = current_timestamp()

        # Record outcome and timing
        add_span_attribute("execution_duration_ms", execution_end - execution_start)
        add_span_attribute("hook_exit_code", hook_result.exit_code)
        add_span_attribute("payload_size_bytes", payload_size)
        add_span_attribute("response_size_bytes", response_size)

        IF hook_result.success:
            record_event("hook_completed", {"mutations": hook_result.mutations})
        ELSE:
            record_event("hook_failed", {"error": hook_result.error})
            add_span_attribute("error_type", hook_result.error_type)

        end_span()

    HOOK_CORRELATION_PATTERN:
        # Link hook spans to triggering task/agent spans
        parent_span: "task_execution_span" | "agent_lifecycle_span"
        hook_span: "hook_execution_span"
        correlation_attributes:
            task_id: "triggering_task_identifier"
            agent_id: "executing_agent_identifier"
            hook_trigger: "pre_task" | "post_task" | "on_error" | "on_file_change"
</code></pre>

<h4 id="35-otlp-integration-pattern">3.5 OTLP Integration Pattern</h4>
<pre><code class="language-pseudocode">PATTERN OTLPConfiguration:
    EXPORTER_SETUP:
        protocol: binary_protobuf | grpc | http_json
        endpoint: collector_address
        compression: gzip | none
        retry_policy: exponential_backoff
    
    COLLECTOR_PIPELINE:
        receivers: [otlp_grpc, otlp_http]
        processors: [batch, memory_limiter]
        exporters: [backend_storage]
    
    PERFORMANCE_CONSIDERATIONS:
        binary_encoding: 2-3x_cpu_efficiency
        batch_processing: reduce_network_calls
        async_export: prevent_blocking
</code></pre>

<h3 id="4-metrics-collection-patterns">4. Metrics Collection Patterns</h3>

<h4 id="41-metric-types-pattern">4.1 Metric Types Pattern</h4>
<pre><code class="language-pseudocode">PATTERN MetricTypes:
    CATEGORIES:
        counters: cumulative_values
        gauges: point_in_time_values
        histograms: distribution_values
        summaries: statistical_aggregates
</code></pre>

<h4 id="42-agent-specific-metrics-pattern">4.2 Agent-Specific Metrics Pattern</h4>
<pre><code class="language-pseudocode">PATTERN AgentMetrics:
    COMMON_METRICS:
        operation_count
        operation_duration
        error_rate
        resource_utilization
        queue_depth
    
    SPECIALIZED_METRICS:
        researcher_agent: [sources_searched, relevance_scores]
        coder_agent: [lines_generated, complexity_scores]
        coordinator_agent: [agents_managed, coordination_latency]
</code></pre>

<h4 id="43-system-wide-metrics-pattern">4.3 System-Wide Metrics Pattern</h4>
<pre><code class="language-pseudocode">PATTERN SystemMetrics:
    CATEGORIES:
        swarm_coordination_metrics
        resource_utilization_metrics
        agent_lifecycle_metrics
        task_completion_metrics
        communication_overhead_metrics
</code></pre>

<h4 id="44-messaging-backplane-metrics-pattern">4.4 Messaging Backplane Metrics Pattern</h4>
<pre><code class="language-pseudocode">PATTERN BackplaneMetrics:
    CONSUMER_HEALTH:
        consumer_lag_ms
        pending_message_count
        ack_rate_per_second
        reconnection_count
    
    BROKER_PERFORMANCE:
        fsync_latency_p99
        disk_write_queue_depth
        memory_buffer_utilization
        active_connection_count
    
    MESSAGE_FLOW:
        publish_rate_per_topic
        fan_out_multiplier
        message_size_p95
        throughput_mb_per_second
    
    SYSTEM_HEALTH:
        broker_cpu_percentage
        jvm_gc_pause_ms
        network_retransmits
        partition_leader_changes
</code></pre>

<h4 id="45-critical-threshold-monitoring-pattern">4.5 Critical Threshold Monitoring Pattern</h4>
<pre><code class="language-pseudocode">PATTERN CriticalThresholds:
    MESSAGING_THRESHOLDS:
        jetstream_fsync_lag: 50M_msgs_per_day
        rabbitmq_queue_depth: 10000_messages
        redis_output_buffer: approaching_limit
        kafka_consumer_lag: 1000_msgs_or_30sec
    
    RESPONSE_ACTIONS:
        emit_threshold_alert()
        trigger_backpressure()
        scale_consumer_pool()
        initiate_flow_control()
</code></pre>

<h3 id="5-logging-patterns">5. Logging Patterns</h3>

<h4 id="51-structured-logging-pattern">5.1 Structured Logging Pattern</h4>
<pre><code class="language-pseudocode">PATTERN StructuredLogging:
    LOG_ENTRY_STRUCTURE:
        timestamp
        severity_level
        message_content
        trace_correlation
        agent_context
        operation_context
        environment_metadata
</code></pre>

<h4 id="52-log-level-classification-pattern">5.2 Log Level Classification Pattern</h4>
<pre><code class="language-pseudocode">PATTERN LogLevels:
    DEBUG: detailed_diagnostic_info
    INFO: general_operational_events
    WARNING: potential_issue_indicators
    ERROR: failure_notifications
    CRITICAL: system_critical_events
</code></pre>

<h4 id="53-log-aggregation-pattern">5.3 Log Aggregation Pattern</h4>
<pre><code class="language-pseudocode">PATTERN LogAggregation:
    PIPELINE_STAGES:
        collection_from_sources
        parsing_and_structuring
        enrichment_with_context
        correlation_by_identifiers
        aggregation_by_patterns
        storage_and_indexing
</code></pre>

<h4 id="54-multiline-log-handling-pattern">5.4 Multiline Log Handling Pattern</h4>
<pre><code class="language-pseudocode">PATTERN MultilineLogHandling:
    DETECTION_STRATEGIES:
        prefix_pattern_matching
        continuation_character_detection
        timeout_based_grouping
        structured_format_parsing
    
    PROCESSING_OPTIONS:
        buffer_until_complete()
        apply_max_wait_timeout()
        preserve_original_formatting()
        add_correlation_metadata()
    
    OUTPUT_FORMATS:
        single_log_entry: complete_text_block
        json_lines: structured_per_line
        otlp_format: single_log_record
</code></pre>

<h3 id="6-multi-agent-observability-patterns">6. Multi-Agent Observability Patterns</h3>

<h4 id="61-agent-state-tracking-pattern">6.1 Agent State Tracking Pattern</h4>
<pre><code class="language-pseudocode">PATTERN AgentStateObservability:
    STATES:
        initializing
        idle
        processing
        waiting
        error
        terminating
    
    STATE_TRANSITIONS:
        capture_state_change()
        measure_state_duration()
        detect_anomalous_transitions()
        maintain_state_history()
</code></pre>

<h4 id="62-swarm-coordination-observability-pattern">6.2 Swarm Coordination Observability Pattern</h4>
<pre><code class="language-pseudocode">PATTERN SwarmObservability:
    track_agent_assignments()
    monitor_task_distribution()
    measure_coordination_latency()
    analyze_communication_patterns()
    detect_coordination_anomalies()
</code></pre>

<h4 id="63-communication-pattern-analysis">6.3 Communication Pattern Analysis</h4>
<pre><code class="language-pseudocode">PATTERN CommunicationAnalysis:
    build_communication_graph()
    track_message_flow()
    measure_communication_latency()
    identify_bottlenecks()
    detect_anomalous_patterns()
</code></pre>

<h3 id="7-alerting-and-anomaly-detection-patterns">7. Alerting and Anomaly Detection Patterns</h3>

<h4 id="71-alert-rule-pattern">7.1 Alert Rule Pattern</h4>
<pre><code class="language-pseudocode">PATTERN AlertRules:
    RULE_COMPONENTS:
        condition_expression
        severity_level
        notification_channels
        remediation_actions
        suppression_windows
</code></pre>

<h4 id="72-anomaly-detection-pattern">7.2 Anomaly Detection Pattern</h4>
<pre><code class="language-pseudocode">PATTERN AnomalyDetection:
    DETECTION_METHODS:
        statistical_analysis
        pattern_recognition
        machine_learning_models
        threshold_monitoring
        behavioral_analysis
    
    RESPONSE_ACTIONS:
        emit_alert()
        trigger_investigation()
        initiate_auto_remediation()
        update_baselines()
</code></pre>

<h4 id="73-prometheus-alert-pattern">7.3 Prometheus Alert Pattern</h4>
<pre><code class="language-pseudocode">PATTERN PrometheusAlerts:
    ALERT_DEFINITION:
        name: descriptive_alert_name
        expression: promql_query
        duration: evaluation_period
        severity: critical | warning | info
        annotations: detailed_descriptions
    
    COMMON_ALERTS:
        agent_crash_loop:
            expr: rate(restarts_total[5m]) &gt; 1/min
            severity: critical
        
        high_consumer_lag:
            expr: consumer_lag &gt; 1000
            severity: warning
        
        resource_exhaustion:
            expr: memory_percent &gt; 90
            severity: critical
    
    ALERT_ROUTING:
        group_by: [alertname, cluster, agent]
        group_wait: 30s
        group_interval: 5m
        repeat_interval: 12h
</code></pre>

<h3 id="8-visualization-patterns">8. Visualization Patterns</h3>

<h4 id="81-dashboard-organization-pattern">8.1 Dashboard Organization Pattern</h4>
<pre><code class="language-pseudocode">PATTERN DashboardStructure:
    DASHBOARD_TYPES:
        system_overview
        agent_performance
        swarm_coordination
        error_analysis
        resource_utilization
    
    VISUALIZATION_TYPES:
        time_series_graphs
        distribution_histograms
        relationship_matrices
        status_indicators
        flow_diagrams
</code></pre>

<h4 id="82-real-time-monitoring-pattern">8.2 Real-Time Monitoring Pattern</h4>
<pre><code class="language-pseudocode">PATTERN RealTimeMonitoring:
    COMPONENTS:
        live_trace_visualization
        metric_sparklines
        log_streaming
        topology_mapping
        alert_notifications
</code></pre>

<h3 id="9-data-management-patterns">9. Data Management Patterns</h3>

<h4 id="91-retention-policy-pattern">9.1 Retention Policy Pattern</h4>
<pre><code class="language-pseudocode">PATTERN DataRetention:
    RETENTION_TIERS:
        hot_storage: recent_full_fidelity
        warm_storage: medium_term_sampled
        cold_storage: long_term_compressed
        archive: historical_metadata
    
    TRANSITION_RULES:
        age_based_migration()
        importance_based_retention()
        compliance_driven_archival()
</code></pre>

<h4 id="92-data-lifecycle-pattern">9.2 Data Lifecycle Pattern</h4>
<pre><code class="language-pseudocode">PATTERN DataLifecycle:
    ingest_and_validate()
    process_and_enrich()
    store_with_indexing()
    age_and_compress()
    archive_or_delete()
</code></pre>

<h3 id="10-performance-optimization-patterns">10. Performance Optimization Patterns</h3>

<h4 id="101-overhead-management-pattern">10.1 Overhead Management Pattern</h4>
<pre><code class="language-pseudocode">PATTERN OverheadOptimization:
    STRATEGIES:
        adaptive_sampling
        batch_processing
        asynchronous_export
        intelligent_filtering
        resource_pooling
</code></pre>

<h4 id="102-scalability-pattern">10.2 Scalability Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ObservabilityScaling:
    HORIZONTAL_SCALING:
        distribute_collection_load()
        shard_storage_backend()
        parallelize_processing()
    
    VERTICAL_SCALING:
        optimize_resource_allocation()
        implement_tiered_storage()
        cache_frequent_queries()
</code></pre>

<h3 id="11-integration-patterns">11. Integration Patterns</h3>

<h4 id="111-external-system-integration-pattern">11.1 External System Integration Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ExternalIntegration:
    INTEGRATION_POINTS:
        incident_management_systems
        analytics_platforms
        machine_learning_pipelines
        notification_services
        automation_tools
</code></pre>

<h4 id="112-data-export-pattern">11.2 Data Export Pattern</h4>
<pre><code class="language-pseudocode">PATTERN DataExport:
    configure_export_format()
    set_export_schedule()
    filter_export_data()
    transform_for_destination()
    verify_export_completion()
</code></pre>

<h3 id="12-security-and-compliance-patterns">12. Security and Compliance Patterns</h3>

<h4 id="121-observability-security-pattern">12.1 Observability Security Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ObservabilitySecurity:
    SECURITY_MEASURES:
        encrypt_data_transmission()
        authenticate_access()
        authorize_operations()
        audit_all_access()
        detect_sensitive_data()
        apply_data_masking()
</code></pre>

<h4 id="122-compliance-pattern">12.2 Compliance Pattern</h4>
<pre><code class="language-pseudocode">PATTERN ComplianceMonitoring:
    track_data_access()
    enforce_retention_policies()
    maintain_audit_trails()
    generate_compliance_reports()
    automate_policy_enforcement()
</code></pre>

<h3 id="13-implementation-considerations">13. Implementation Considerations</h3>

<h4 id="131-framework-selection-criteria">13.1 Framework Selection Criteria</h4>
<ul>
  <li>Language-agnostic instrumentation capabilities</li>
  <li>Support for distributed tracing standards</li>
  <li>Flexible metric collection options</li>
  <li>Structured logging support</li>
  <li>Extensible architecture</li>
</ul>

<h4 id="132-protocol-performance-considerations">13.2 Protocol Performance Considerations</h4>
<pre><code class="language-pseudocode">PATTERN ProtocolSelection:
    ENCODING_EFFICIENCY:
        binary_protobuf: 15_microsec_per_log
        json_encoding: 45_microsec_per_log
        size_ratio: 1:2_binary_vs_json
    
    TRANSPORT_OPTIONS:
        grpc_tonic: minimal_async_overhead
        http_binary: wide_compatibility
        direct_stdout: essentially_free
    
    OPTIMIZATION_STRATEGIES:
        use_non_blocking_appenders()
        batch_telemetry_exports()
        implement_circuit_breakers()
</code></pre>

<h4 id="133-deployment-considerations">13.3 Deployment Considerations</h4>
<ul>
  <li>Minimize instrumentation overhead</li>
  <li>Ensure high availability of monitoring</li>
  <li>Plan for data volume growth</li>
  <li>Implement proper access controls</li>
  <li>Enable disaster recovery</li>
  <li>Consider protocol efficiency for scale</li>
</ul>

<h3 id="14-best-practices-summary">14. Best Practices Summary</h3>

<h4 id="141-instrumentation-best-practices">14.1 Instrumentation Best Practices</h4>
<ul>
  <li>Instrument critical code paths</li>
  <li>Use consistent naming conventions</li>
  <li>Implement proper context propagation</li>
  <li>Apply appropriate sampling rates</li>
  <li>Minimize performance impact</li>
</ul>

<h4 id="142-data-management-best-practices">14.2 Data Management Best Practices</h4>
<ul>
  <li>Define clear retention policies</li>
  <li>Implement data compression strategies</li>
  <li>Use appropriate storage tiers</li>
  <li>Regular backup and archival</li>
  <li>Monitor storage growth trends</li>
</ul>

<h4 id="143-operational-best-practices">14.3 Operational Best Practices</h4>
<ul>
  <li>Establish baseline metrics</li>
  <li>Define meaningful alerts</li>
  <li>Regular review of dashboards</li>
  <li>Continuous optimization</li>
  <li>Document monitoring patterns</li>
</ul>

<hr />

<h2 id="15-concrete-specifications">15. Concrete Specifications</h2>

<h3 id="151-prometheus-metrics-schema">15.1 Prometheus Metrics Schema</h3>

<h4 id="1511-agent-lifecycle-metrics">15.1.1 Agent Lifecycle Metrics</h4>
<div class="language-prometheus highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c"># HELP agent_spawns_total Number of agent instances spawned</span>
<span class="c"># TYPE agent_spawns_total counter</span>
<span class="n">agent_spawns_total</span><span class="p">{</span><span class="na">agent_type</span><span class="o">=</span><span class="s2">"task_agent"</span><span class="p">,</span><span class="na">spawn_method</span><span class="o">=</span><span class="s2">"subprocess"</span><span class="p">}</span> <span class="mi">125</span>

<span class="c"># HELP agent_active_count Current number of active agents</span>
<span class="c"># TYPE agent_active_count gauge</span>
<span class="n">agent_active_count</span><span class="p">{</span><span class="na">agent_type</span><span class="o">=</span><span class="s2">"task_agent"</span><span class="p">,</span><span class="na">state</span><span class="o">=</span><span class="s2">"processing"</span><span class="p">}</span> <span class="mi">8</span>

<span class="c"># HELP agent_duration_seconds Time agents spend in each state</span>
<span class="c"># TYPE agent_duration_seconds histogram</span>
<span class="n">agent_duration_seconds_bucket</span><span class="p">{</span><span class="na">agent_type</span><span class="o">=</span><span class="s2">"patch_agent"</span><span class="p">,</span><span class="na">state</span><span class="o">=</span><span class="s2">"processing"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"1.0"</span><span class="p">}</span> <span class="mi">45</span>
<span class="n">agent_duration_seconds_bucket</span><span class="p">{</span><span class="na">agent_type</span><span class="o">=</span><span class="s2">"patch_agent"</span><span class="p">,</span><span class="na">state</span><span class="o">=</span><span class="s2">"processing"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"5.0"</span><span class="p">}</span> <span class="mi">89</span>
<span class="n">agent_duration_seconds_bucket</span><span class="p">{</span><span class="na">agent_type</span><span class="o">=</span><span class="s2">"patch_agent"</span><span class="p">,</span><span class="na">state</span><span class="o">=</span><span class="s2">"processing"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"10.0"</span><span class="p">}</span> <span class="mi">112</span>
<span class="n">agent_duration_seconds_bucket</span><span class="p">{</span><span class="na">agent_type</span><span class="o">=</span><span class="s2">"patch_agent"</span><span class="p">,</span><span class="na">state</span><span class="o">=</span><span class="s2">"processing"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"+Inf"</span><span class="p">}</span> <span class="mi">125</span>
<span class="n">agent_duration_seconds_sum</span><span class="p">{</span><span class="na">agent_type</span><span class="o">=</span><span class="s2">"patch_agent"</span><span class="p">,</span><span class="na">state</span><span class="o">=</span><span class="s2">"processing"</span><span class="p">}</span> <span class="mf">387.5</span>
<span class="n">agent_duration_seconds_count</span><span class="p">{</span><span class="na">agent_type</span><span class="o">=</span><span class="s2">"patch_agent"</span><span class="p">,</span><span class="na">state</span><span class="o">=</span><span class="s2">"processing"</span><span class="p">}</span> <span class="mi">125</span>

<span class="c"># HELP agent_memory_bytes Memory usage per agent</span>
<span class="c"># TYPE agent_memory_bytes gauge</span>
<span class="n">agent_memory_bytes</span><span class="p">{</span><span class="na">agent_id</span><span class="o">=</span><span class="s2">"agent-42"</span><span class="p">,</span><span class="na">agent_type</span><span class="o">=</span><span class="s2">"analysis_agent"</span><span class="p">}</span> <span class="mi">524288000</span>

<span class="c"># HELP agent_cpu_percent CPU utilization per agent</span>
<span class="c"># TYPE agent_cpu_percent gauge</span>
<span class="n">agent_cpu_percent</span><span class="p">{</span><span class="na">agent_id</span><span class="o">=</span><span class="s2">"agent-42"</span><span class="p">,</span><span class="na">agent_type</span><span class="o">=</span><span class="s2">"analysis_agent"</span><span class="p">}</span> <span class="mf">23.5</span>
</code></pre></div></div>

<h4 id="1512-task-execution-metrics">15.1.2 Task Execution Metrics</h4>
<div class="language-prometheus highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c"># HELP task_completions_total Number of completed tasks</span>
<span class="c"># TYPE task_completions_total counter</span>
<span class="n">task_completions_total</span><span class="p">{</span><span class="na">task_type</span><span class="o">=</span><span class="s2">"analysis"</span><span class="p">,</span><span class="na">status</span><span class="o">=</span><span class="s2">"success"</span><span class="p">,</span><span class="na">agent_type</span><span class="o">=</span><span class="s2">"analysis_agent"</span><span class="p">}</span> <span class="mi">1247</span>

<span class="c"># HELP task_errors_total Number of failed tasks</span>
<span class="c"># TYPE task_errors_total counter</span>
<span class="n">task_errors_total</span><span class="p">{</span><span class="na">task_type</span><span class="o">=</span><span class="s2">"code_generation"</span><span class="p">,</span><span class="na">error_type</span><span class="o">=</span><span class="s2">"timeout"</span><span class="p">,</span><span class="na">agent_type</span><span class="o">=</span><span class="s2">"coder_agent"</span><span class="p">}</span> <span class="mi">23</span>

<span class="c"># HELP task_duration_seconds Task execution duration distribution</span>
<span class="c"># TYPE task_duration_seconds histogram</span>
<span class="n">task_duration_seconds_bucket</span><span class="p">{</span><span class="na">task_type</span><span class="o">=</span><span class="s2">"analysis"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"5.0"</span><span class="p">}</span> <span class="mi">234</span>
<span class="n">task_duration_seconds_bucket</span><span class="p">{</span><span class="na">task_type</span><span class="o">=</span><span class="s2">"analysis"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"10.0"</span><span class="p">}</span> <span class="mi">567</span>
<span class="n">task_duration_seconds_bucket</span><span class="p">{</span><span class="na">task_type</span><span class="o">=</span><span class="s2">"analysis"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"30.0"</span><span class="p">}</span> <span class="mi">890</span>
<span class="n">task_duration_seconds_bucket</span><span class="p">{</span><span class="na">task_type</span><span class="o">=</span><span class="s2">"analysis"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"+Inf"</span><span class="p">}</span> <span class="mi">923</span>
<span class="n">task_duration_seconds_sum</span><span class="p">{</span><span class="na">task_type</span><span class="o">=</span><span class="s2">"analysis"</span><span class="p">}</span> <span class="mf">8934.2</span>
<span class="n">task_duration_seconds_count</span><span class="p">{</span><span class="na">task_type</span><span class="o">=</span><span class="s2">"analysis"</span><span class="p">}</span> <span class="mi">923</span>

<span class="c"># HELP task_queue_depth Current number of pending tasks</span>
<span class="c"># TYPE task_queue_depth gauge</span>
<span class="n">task_queue_depth</span><span class="p">{</span><span class="na">queue_type</span><span class="o">=</span><span class="s2">"priority"</span><span class="p">,</span><span class="na">agent_type</span><span class="o">=</span><span class="s2">"coordinator_agent"</span><span class="p">}</span> <span class="mi">15</span>
</code></pre></div></div>

<h4 id="1513-communication-metrics">15.1.3 Communication Metrics</h4>
<div class="language-prometheus highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c"># HELP messages_sent_total Number of messages sent between agents</span>
<span class="c"># TYPE messages_sent_total counter</span>
<span class="n">messages_sent_total</span><span class="p">{</span><span class="na">from_agent_type</span><span class="o">=</span><span class="s2">"coordinator"</span><span class="p">,</span><span class="na">to_agent_type</span><span class="o">=</span><span class="s2">"worker"</span><span class="p">,</span><span class="na">message_type</span><span class="o">=</span><span class="s2">"task_assignment"</span><span class="p">}</span> <span class="mi">1542</span>

<span class="c"># HELP message_latency_seconds Message delivery latency distribution</span>
<span class="c"># TYPE message_latency_seconds histogram</span>
<span class="n">message_latency_seconds_bucket</span><span class="p">{</span><span class="na">message_type</span><span class="o">=</span><span class="s2">"task_result"</span><span class="p">,</span><span class="na">transport</span><span class="o">=</span><span class="s2">"nats"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"0.001"</span><span class="p">}</span> <span class="mi">1234</span>
<span class="n">message_latency_seconds_bucket</span><span class="p">{</span><span class="na">message_type</span><span class="o">=</span><span class="s2">"task_result"</span><span class="p">,</span><span class="na">transport</span><span class="o">=</span><span class="s2">"nats"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"0.005"</span><span class="p">}</span> <span class="mi">2345</span>
<span class="n">message_latency_seconds_bucket</span><span class="p">{</span><span class="na">message_type</span><span class="o">=</span><span class="s2">"task_result"</span><span class="p">,</span><span class="na">transport</span><span class="o">=</span><span class="s2">"nats"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"0.01"</span><span class="p">}</span> <span class="mi">2456</span>
<span class="n">message_latency_seconds_bucket</span><span class="p">{</span><span class="na">message_type</span><span class="o">=</span><span class="s2">"task_result"</span><span class="p">,</span><span class="na">transport</span><span class="o">=</span><span class="s2">"nats"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"+Inf"</span><span class="p">}</span> <span class="mi">2467</span>
<span class="n">message_latency_seconds_sum</span><span class="p">{</span><span class="na">message_type</span><span class="o">=</span><span class="s2">"task_result"</span><span class="p">,</span><span class="na">transport</span><span class="o">=</span><span class="s2">"nats"</span><span class="p">}</span> <span class="mf">12.34</span>
<span class="n">message_latency_seconds_count</span><span class="p">{</span><span class="na">message_type</span><span class="o">=</span><span class="s2">"task_result"</span><span class="p">,</span><span class="na">transport</span><span class="o">=</span><span class="s2">"nats"</span><span class="p">}</span> <span class="mi">2467</span>

<span class="c"># HELP message_queue_depth Number of messages in transport queues</span>
<span class="c"># TYPE message_queue_depth gauge</span>
<span class="n">message_queue_depth</span><span class="p">{</span><span class="na">queue_name</span><span class="o">=</span><span class="s2">"task_assignments"</span><span class="p">,</span><span class="na">broker</span><span class="o">=</span><span class="s2">"nats"</span><span class="p">}</span> <span class="mi">8</span>

<span class="c"># HELP message_delivery_failures_total Failed message deliveries</span>
<span class="c"># TYPE message_delivery_failures_total counter</span>
<span class="n">message_delivery_failures_total</span><span class="p">{</span><span class="na">failure_reason</span><span class="o">=</span><span class="s2">"timeout"</span><span class="p">,</span><span class="na">transport</span><span class="o">=</span><span class="s2">"nats"</span><span class="p">}</span> <span class="mi">12</span>
</code></pre></div></div>

<h4 id="1514-claude-cli-integration-metrics">15.1.4 Claude-CLI Integration Metrics</h4>
<div class="language-prometheus highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c"># HELP claude_cli_processes_total Number of Claude CLI processes spawned</span>
<span class="c"># TYPE claude_cli_processes_total counter</span>
<span class="n">claude_cli_processes_total</span><span class="p">{</span><span class="na">command_type</span><span class="o">=</span><span class="s2">"parallel_agents"</span><span class="p">,</span><span class="na">batch_size</span><span class="o">=</span><span class="s2">"5"</span><span class="p">}</span> <span class="mi">45</span>

<span class="c"># HELP claude_cli_parallel_agents_active Current number of parallel internal agents</span>
<span class="c"># TYPE claude_cli_parallel_agents_active gauge</span>
<span class="n">claude_cli_parallel_agents_active</span><span class="p">{</span><span class="na">batch_id</span><span class="o">=</span><span class="s2">"batch-123"</span><span class="p">,</span><span class="na">process_id</span><span class="o">=</span><span class="s2">"12345"</span><span class="p">}</span> <span class="mi">5</span>

<span class="c"># HELP claude_cli_hook_executions_total Number of hook executions</span>
<span class="c"># TYPE claude_cli_hook_executions_total counter</span>
<span class="n">claude_cli_hook_executions_total</span><span class="p">{</span><span class="na">hook_type</span><span class="o">=</span><span class="s2">"post_task"</span><span class="p">,</span><span class="na">hook_name</span><span class="o">=</span><span class="s2">"validation"</span><span class="p">,</span><span class="na">status</span><span class="o">=</span><span class="s2">"success"</span><span class="p">}</span> <span class="mi">234</span>

<span class="c"># HELP claude_cli_hook_duration_seconds Hook execution duration</span>
<span class="c"># TYPE claude_cli_hook_duration_seconds histogram</span>
<span class="n">claude_cli_hook_duration_seconds_bucket</span><span class="p">{</span><span class="na">hook_type</span><span class="o">=</span><span class="s2">"pre_task"</span><span class="p">,</span><span class="na">hook_name</span><span class="o">=</span><span class="s2">"setup"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"0.1"</span><span class="p">}</span> <span class="mi">45</span>
<span class="n">claude_cli_hook_duration_seconds_bucket</span><span class="p">{</span><span class="na">hook_type</span><span class="o">=</span><span class="s2">"pre_task"</span><span class="p">,</span><span class="na">hook_name</span><span class="o">=</span><span class="s2">"setup"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"0.5"</span><span class="p">}</span> <span class="mi">67</span>
<span class="n">claude_cli_hook_duration_seconds_bucket</span><span class="p">{</span><span class="na">hook_type</span><span class="o">=</span><span class="s2">"pre_task"</span><span class="p">,</span><span class="na">hook_name</span><span class="o">=</span><span class="s2">"setup"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"1.0"</span><span class="p">}</span> <span class="mi">78</span>
<span class="n">claude_cli_hook_duration_seconds_bucket</span><span class="p">{</span><span class="na">hook_type</span><span class="o">=</span><span class="s2">"pre_task"</span><span class="p">,</span><span class="na">hook_name</span><span class="o">=</span><span class="s2">"setup"</span><span class="p">,</span><span class="na">le</span><span class="o">=</span><span class="s2">"+Inf"</span><span class="p">}</span> <span class="mi">80</span>
<span class="n">claude_cli_hook_duration_seconds_sum</span><span class="p">{</span><span class="na">hook_type</span><span class="o">=</span><span class="s2">"pre_task"</span><span class="p">,</span><span class="na">hook_name</span><span class="o">=</span><span class="s2">"setup"</span><span class="p">}</span> <span class="mf">23.45</span>
<span class="n">claude_cli_hook_duration_seconds_count</span><span class="p">{</span><span class="na">hook_type</span><span class="o">=</span><span class="s2">"pre_task"</span><span class="p">,</span><span class="na">hook_name</span><span class="o">=</span><span class="s2">"setup"</span><span class="p">}</span> <span class="mi">80</span>
</code></pre></div></div>

<h3 id="152-opentelemetry-tracing-schema">15.2 OpenTelemetry Tracing Schema</h3>

<h4 id="1521-agent-execution-trace-structure">15.2.1 Agent Execution Trace Structure</h4>
<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"traceID"</span><span class="p">:</span><span class="w"> </span><span class="s2">"4bf92f3577b34da6a3ce929d0e0e4736"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"spanID"</span><span class="p">:</span><span class="w"> </span><span class="s2">"00f067aa0ba902b7"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"parentSpanID"</span><span class="p">:</span><span class="w"> </span><span class="s2">"00f067aa0ba90000"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"operationName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agent.task_execution"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"startTime"</span><span class="p">:</span><span class="w"> </span><span class="mi">1609459200000000</span><span class="p">,</span><span class="w">
  </span><span class="nl">"duration"</span><span class="p">:</span><span class="w"> </span><span class="mi">5000000</span><span class="p">,</span><span class="w">
  </span><span class="nl">"tags"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"agent.id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agent-42"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"agent.type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"analysis_agent"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"agent.version"</span><span class="p">:</span><span class="w"> </span><span class="s2">"1.0.0"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"task.id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"task-12345"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"task.type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"code_analysis"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"task.priority"</span><span class="p">:</span><span class="w"> </span><span class="s2">"high"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"component"</span><span class="p">:</span><span class="w"> </span><span class="s2">"mister-smith-agent"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"span.kind"</span><span class="p">:</span><span class="w"> </span><span class="s2">"internal"</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"process"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"serviceName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"mister-smith-framework"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"tags"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"hostname"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agent-node-01"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"process.pid"</span><span class="p">:</span><span class="w"> </span><span class="s2">"12345"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"jaeger.version"</span><span class="p">:</span><span class="w"> </span><span class="s2">"1.29.0"</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"logs"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
    </span><span class="p">{</span><span class="w">
      </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="mi">1609459201000000</span><span class="p">,</span><span class="w">
      </span><span class="nl">"fields"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
        </span><span class="p">{</span><span class="nl">"key"</span><span class="p">:</span><span class="w"> </span><span class="s2">"event"</span><span class="p">,</span><span class="w"> </span><span class="nl">"value"</span><span class="p">:</span><span class="w"> </span><span class="s2">"task_started"</span><span class="p">},</span><span class="w">
        </span><span class="p">{</span><span class="nl">"key"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agent.state"</span><span class="p">,</span><span class="w"> </span><span class="nl">"value"</span><span class="p">:</span><span class="w"> </span><span class="s2">"processing"</span><span class="p">}</span><span class="w">
      </span><span class="p">]</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="p">{</span><span class="w">
      </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="mi">1609459205000000</span><span class="p">,</span><span class="w">
      </span><span class="nl">"fields"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
        </span><span class="p">{</span><span class="nl">"key"</span><span class="p">:</span><span class="w"> </span><span class="s2">"event"</span><span class="p">,</span><span class="w"> </span><span class="nl">"value"</span><span class="p">:</span><span class="w"> </span><span class="s2">"task_completed"</span><span class="p">},</span><span class="w">
        </span><span class="p">{</span><span class="nl">"key"</span><span class="p">:</span><span class="w"> </span><span class="s2">"result.status"</span><span class="p">,</span><span class="w"> </span><span class="nl">"value"</span><span class="p">:</span><span class="w"> </span><span class="s2">"success"</span><span class="p">},</span><span class="w">
        </span><span class="p">{</span><span class="nl">"key"</span><span class="p">:</span><span class="w"> </span><span class="s2">"result.items_processed"</span><span class="p">,</span><span class="w"> </span><span class="nl">"value"</span><span class="p">:</span><span class="w"> </span><span class="mi">47</span><span class="p">}</span><span class="w">
      </span><span class="p">]</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">]</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h4 id="1522-cross-agent-correlation-pattern">15.2.2 Cross-Agent Correlation Pattern</h4>
<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"orchestrator_span"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"traceID"</span><span class="p">:</span><span class="w"> </span><span class="s2">"4bf92f3577b34da6a3ce929d0e0e4736"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"spanID"</span><span class="p">:</span><span class="w"> </span><span class="s2">"00f067aa0ba90000"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"operationName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"orchestrator.task_distribution"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"tags"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">"component"</span><span class="p">:</span><span class="w"> </span><span class="s2">"task_orchestrator"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"task.batch_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"batch-123"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"parallel.agent_count"</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"child_spans"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
    </span><span class="p">{</span><span class="w">
      </span><span class="nl">"traceID"</span><span class="p">:</span><span class="w"> </span><span class="s2">"4bf92f3577b34da6a3ce929d0e0e4736"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"spanID"</span><span class="p">:</span><span class="w"> </span><span class="s2">"00f067aa0ba902b7"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"parentSpanID"</span><span class="p">:</span><span class="w"> </span><span class="s2">"00f067aa0ba90000"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"operationName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agent.parallel_execution"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"tags"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"agent.internal_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agent-0"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"parallel.execution_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"batch-123"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"agent.role"</span><span class="p">:</span><span class="w"> </span><span class="s2">"task_agent"</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">]</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h4 id="1523-hook-system-tracing-pattern">15.2.3 Hook System Tracing Pattern</h4>
<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"traceID"</span><span class="p">:</span><span class="w"> </span><span class="s2">"4bf92f3577b34da6a3ce929d0e0e4736"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"spanID"</span><span class="p">:</span><span class="w"> </span><span class="s2">"00f067aa0ba90abc"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"parentSpanID"</span><span class="p">:</span><span class="w"> </span><span class="s2">"00f067aa0ba902b7"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"operationName"</span><span class="p">:</span><span class="w"> </span><span class="s2">"hook.execution"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"tags"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"hook.name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"validation_hook"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"hook.type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"post_task"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"hook.script_path"</span><span class="p">:</span><span class="w"> </span><span class="s2">"/hooks/validate.sh"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"hook.trigger"</span><span class="p">:</span><span class="w"> </span><span class="s2">"task_completion"</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"events"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
    </span><span class="p">{</span><span class="w">
      </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="mi">1609459205100000</span><span class="p">,</span><span class="w">
      </span><span class="nl">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"hook_started"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"attributes"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"payload.size_bytes"</span><span class="p">:</span><span class="w"> </span><span class="mi">1024</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="p">{</span><span class="w">
      </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="mi">1609459205300000</span><span class="p">,</span><span class="w">
      </span><span class="nl">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"hook_completed"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"attributes"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"hook.exit_code"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
        </span><span class="nl">"response.size_bytes"</span><span class="p">:</span><span class="w"> </span><span class="mi">256</span><span class="p">,</span><span class="w">
        </span><span class="nl">"mutations.count"</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">]</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h3 id="153-structured-logging-schema">15.3 Structured Logging Schema</h3>

<h4 id="1531-agent-lifecycle-log-format">15.3.1 Agent Lifecycle Log Format</h4>
<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="s2">"2024-01-01T12:00:00.000Z"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"level"</span><span class="p">:</span><span class="w"> </span><span class="s2">"INFO"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"msg"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Agent spawned successfully"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"trace_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"4bf92f3577b34da6a3ce929d0e0e4736"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"span_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"00f067aa0ba902b7"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"resource"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"service.name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"mister-smith-agent"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"service.version"</span><span class="p">:</span><span class="w"> </span><span class="s2">"1.0.0"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"service.instance.id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agent-node-01"</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"attributes"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"agent.id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agent-42"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"agent.type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"analysis_agent"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"agent.state"</span><span class="p">:</span><span class="w"> </span><span class="s2">"initializing"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"spawn.method"</span><span class="p">:</span><span class="w"> </span><span class="s2">"subprocess"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"parent.agent_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"coordinator-01"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"memory.allocated_mb"</span><span class="p">:</span><span class="w"> </span><span class="mi">256</span><span class="p">,</span><span class="w">
    </span><span class="nl">"cpu.cores_allocated"</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h4 id="1532-task-execution-log-format">15.3.2 Task Execution Log Format</h4>
<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="s2">"2024-01-01T12:01:30.500Z"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"level"</span><span class="p">:</span><span class="w"> </span><span class="s2">"ERROR"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"msg"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Task execution failed due to timeout"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"trace_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"4bf92f3577b34da6a3ce929d0e0e4736"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"span_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"00f067aa0ba902b7"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"resource"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"service.name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"mister-smith-agent"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"service.version"</span><span class="p">:</span><span class="w"> </span><span class="s2">"1.0.0"</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"attributes"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"agent.id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agent-42"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"task.id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"task-12345"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"task.type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"code_analysis"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"task.priority"</span><span class="p">:</span><span class="w"> </span><span class="s2">"high"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"task.timeout_seconds"</span><span class="p">:</span><span class="w"> </span><span class="mi">30</span><span class="p">,</span><span class="w">
    </span><span class="nl">"task.duration_seconds"</span><span class="p">:</span><span class="w"> </span><span class="mf">30.1</span><span class="p">,</span><span class="w">
    </span><span class="nl">"error.type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"timeout"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"error.category"</span><span class="p">:</span><span class="w"> </span><span class="s2">"execution"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"retry.attempt"</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w">
    </span><span class="nl">"retry.max_attempts"</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"exception"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"TaskTimeoutException"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"message"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Task execution exceeded 30 second timeout"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"stacktrace"</span><span class="p">:</span><span class="w"> </span><span class="s2">"TaskTimeoutException: Task execution exceeded...</span><span class="se">\n</span><span class="s2">  at execute_task:142</span><span class="se">\n</span><span class="s2">  at agent_loop:89"</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h4 id="1533-communication-log-format">15.3.3 Communication Log Format</h4>
<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"timestamp"</span><span class="p">:</span><span class="w"> </span><span class="s2">"2024-01-01T12:02:15.250Z"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"level"</span><span class="p">:</span><span class="w"> </span><span class="s2">"WARN"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"msg"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Message delivery retry required"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"trace_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"4bf92f3577b34da6a3ce929d0e0e4736"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"span_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"00f067aa0ba90xyz"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"resource"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"service.name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"mister-smith-messaging"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"service.version"</span><span class="p">:</span><span class="w"> </span><span class="s2">"1.0.0"</span><span class="w">
  </span><span class="p">},</span><span class="w">
  </span><span class="nl">"attributes"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"message.id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"msg-67890"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"message.type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"task_assignment"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"message.size_bytes"</span><span class="p">:</span><span class="w"> </span><span class="mi">1024</span><span class="p">,</span><span class="w">
    </span><span class="nl">"sender.agent_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"coordinator-01"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"receiver.agent_id"</span><span class="p">:</span><span class="w"> </span><span class="s2">"worker-05"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"transport.broker"</span><span class="p">:</span><span class="w"> </span><span class="s2">"nats"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"transport.subject"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agents.tasks.assign"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"delivery.attempt"</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w">
    </span><span class="nl">"delivery.latency_ms"</span><span class="p">:</span><span class="w"> </span><span class="mi">150</span><span class="p">,</span><span class="w">
    </span><span class="nl">"failure.reason"</span><span class="p">:</span><span class="w"> </span><span class="s2">"connection_timeout"</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h3 id="154-health-check-endpoints">15.4 Health Check Endpoints</h3>

<h4 id="1541-basic-health-check">15.4.1 Basic Health Check</h4>
<div class="language-http highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="err">GET /health
Content-Type: application/json

{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "uptime_seconds": 3600,
  "version": "1.0.0",
  "checks": {
    "messaging": "healthy",
    "database": "healthy",
    "agents": "healthy"
  }
}
</span></code></pre></div></div>

<h4 id="1542-agent-specific-health-check">15.4.2 Agent-Specific Health Check</h4>
<div class="language-http highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="err">GET /health/agent/agent-42
Content-Type: application/json

{
  "agent_id": "agent-42",
  "status": "healthy",
  "state": "processing",
  "uptime_seconds": 1800,
  "current_task": {
    "task_id": "task-12345",
    "task_type": "analysis",
    "started_at": "2024-01-01T11:45:00.000Z",
    "duration_seconds": 900
  },
  "resources": {
    "memory_usage_mb": 245,
    "memory_limit_mb": 512,
    "cpu_usage_percent": 23.5
  },
  "last_heartbeat": "2024-01-01T12:00:00.000Z"
}
</span></code></pre></div></div>

<h4 id="1543-readiness-check">15.4.3 Readiness Check</h4>
<div class="language-http highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="err">GET /health/ready
Content-Type: application/json

{
  "status": "ready",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "ready_for_tasks": true,
  "available_agents": 8,
  "queue_depth": 5,
  "resource_availability": {
    "memory_available_mb": 2048,
    "cpu_available_percent": 65
  }
}
</span></code></pre></div></div>

<h3 id="155-alert-rule-definitions">15.5 Alert Rule Definitions</h3>

<h4 id="1551-critical-alerts-prometheus-alertmanager">15.5.1 Critical Alerts (Prometheus AlertManager)</h4>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">groups</span><span class="pi">:</span>
<span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">agent_critical_alerts</span>
  <span class="na">rules</span><span class="pi">:</span>
  <span class="pi">-</span> <span class="na">alert</span><span class="pi">:</span> <span class="s">AgentCrashLoop</span>
    <span class="na">expr</span><span class="pi">:</span> <span class="s">rate(agent_spawns_total[5m]) &gt; </span><span class="m">1</span>
    <span class="na">for</span><span class="pi">:</span> <span class="s">2m</span>
    <span class="na">labels</span><span class="pi">:</span>
      <span class="na">severity</span><span class="pi">:</span> <span class="s">critical</span>
    <span class="na">annotations</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Agent</span><span class="nv">  </span><span class="s">crash</span><span class="nv"> </span><span class="s">loop</span><span class="nv"> </span><span class="s">detected"</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Agent</span><span class="nv">  </span><span class="s">is</span><span class="nv"> </span><span class="s">restarting</span><span class="nv"> </span><span class="s">more</span><span class="nv"> </span><span class="s">than</span><span class="nv"> </span><span class="s">once</span><span class="nv"> </span><span class="s">per</span><span class="nv"> </span><span class="s">5</span><span class="nv"> </span><span class="s">minutes"</span>

  <span class="pi">-</span> <span class="na">alert</span><span class="pi">:</span> <span class="s">HighTaskErrorRate</span>
    <span class="na">expr</span><span class="pi">:</span> <span class="s">rate(task_errors_total[5m]) / rate(task_completions_total[5m]) &gt; </span><span class="m">0.05</span>
    <span class="na">for</span><span class="pi">:</span> <span class="s">5m</span>
    <span class="na">labels</span><span class="pi">:</span>
      <span class="na">severity</span><span class="pi">:</span> <span class="s">critical</span>
    <span class="na">annotations</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s2">"</span><span class="s">High</span><span class="nv"> </span><span class="s">task</span><span class="nv"> </span><span class="s">error</span><span class="nv"> </span><span class="s">rate</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">"</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Task</span><span class="nv"> </span><span class="s">error</span><span class="nv"> </span><span class="s">rate</span><span class="nv"> </span><span class="s">is</span><span class="nv">  </span><span class="s">over</span><span class="nv"> </span><span class="s">the</span><span class="nv"> </span><span class="s">last</span><span class="nv"> </span><span class="s">5</span><span class="nv"> </span><span class="s">minutes"</span>

  <span class="pi">-</span> <span class="na">alert</span><span class="pi">:</span> <span class="s">AgentMemoryExhaustion</span>
    <span class="na">expr</span><span class="pi">:</span> <span class="s">agent_memory_bytes / (1024*1024*1024) &gt; </span><span class="m">1.5</span>
    <span class="na">for</span><span class="pi">:</span> <span class="s">1m</span>
    <span class="na">labels</span><span class="pi">:</span>
      <span class="na">severity</span><span class="pi">:</span> <span class="s">critical</span>
    <span class="na">annotations</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Agent</span><span class="nv">  </span><span class="s">memory</span><span class="nv"> </span><span class="s">exhaustion"</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Agent</span><span class="nv">  </span><span class="s">is</span><span class="nv"> </span><span class="s">using</span><span class="nv"> </span><span class="s">GB</span><span class="nv"> </span><span class="s">memory"</span>

  <span class="pi">-</span> <span class="na">alert</span><span class="pi">:</span> <span class="s">MessageQueueBackup</span>
    <span class="na">expr</span><span class="pi">:</span> <span class="s">message_queue_depth &gt; </span><span class="m">1000</span>
    <span class="na">for</span><span class="pi">:</span> <span class="s">5m</span>
    <span class="na">labels</span><span class="pi">:</span>
      <span class="na">severity</span><span class="pi">:</span> <span class="s">critical</span>
    <span class="na">annotations</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Message</span><span class="nv"> </span><span class="s">queue</span><span class="nv">  </span><span class="s">backup"</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Queue</span><span class="nv"> </span><span class="s">depth</span><span class="nv"> </span><span class="s">is</span><span class="nv">  </span><span class="s">messages"</span>
</code></pre></div></div>

<h4 id="1552-warning-alerts">15.5.2 Warning Alerts</h4>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">agent_warning_alerts</span>
  <span class="na">rules</span><span class="pi">:</span>
  <span class="pi">-</span> <span class="na">alert</span><span class="pi">:</span> <span class="s">HighTaskLatency</span>
    <span class="na">expr</span><span class="pi">:</span> <span class="s">histogram_quantile(0.95, task_duration_seconds) &gt; </span><span class="m">30</span>
    <span class="na">for</span><span class="pi">:</span> <span class="s">10m</span>
    <span class="na">labels</span><span class="pi">:</span>
      <span class="na">severity</span><span class="pi">:</span> <span class="s">warning</span>
    <span class="na">annotations</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s2">"</span><span class="s">High</span><span class="nv"> </span><span class="s">task</span><span class="nv"> </span><span class="s">latency</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">"</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">95th</span><span class="nv"> </span><span class="s">percentile</span><span class="nv"> </span><span class="s">latency</span><span class="nv"> </span><span class="s">is</span><span class="nv"> </span><span class="s">s"</span>

  <span class="pi">-</span> <span class="na">alert</span><span class="pi">:</span> <span class="s">AgentSaturation</span>
    <span class="na">expr</span><span class="pi">:</span> <span class="s">agent_cpu_percent &gt; </span><span class="m">80</span>
    <span class="na">for</span><span class="pi">:</span> <span class="s">15m</span>
    <span class="na">labels</span><span class="pi">:</span>
      <span class="na">severity</span><span class="pi">:</span> <span class="s">warning</span>
    <span class="na">annotations</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Agent</span><span class="nv">  </span><span class="s">high</span><span class="nv"> </span><span class="s">CPU</span><span class="nv"> </span><span class="s">usage"</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">CPU</span><span class="nv"> </span><span class="s">usage</span><span class="nv"> </span><span class="s">is</span><span class="nv"> </span><span class="s">%"</span>

  <span class="pi">-</span> <span class="na">alert</span><span class="pi">:</span> <span class="s">CommunicationDelay</span>
    <span class="na">expr</span><span class="pi">:</span> <span class="s">histogram_quantile(0.99, message_latency_seconds) &gt; </span><span class="m">5</span>
    <span class="na">for</span><span class="pi">:</span> <span class="s">10m</span>
    <span class="na">labels</span><span class="pi">:</span>
      <span class="na">severity</span><span class="pi">:</span> <span class="s">warning</span>
    <span class="na">annotations</span><span class="pi">:</span>
      <span class="na">summary</span><span class="pi">:</span> <span class="s2">"</span><span class="s">High</span><span class="nv"> </span><span class="s">message</span><span class="nv"> </span><span class="s">latency</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">"</span>
      <span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">99th</span><span class="nv"> </span><span class="s">percentile</span><span class="nv"> </span><span class="s">message</span><span class="nv"> </span><span class="s">latency</span><span class="nv"> </span><span class="s">is</span><span class="nv"> </span><span class="s">s"</span>
</code></pre></div></div>

<h3 id="156-dashboard-configurations">15.6 Dashboard Configurations</h3>

<h4 id="1561-system-overview-dashboard-grafana-json">15.6.1 System Overview Dashboard (Grafana JSON)</h4>
<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"dashboard"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Mister Smith - System Overview"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"panels"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Active Agents"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"stat"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"targets"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"sum(agent_active_count)"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"legendFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Total Active Agents"</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">],</span><span class="w">
        </span><span class="nl">"fieldConfig"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">"defaults"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
            </span><span class="nl">"color"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nl">"mode"</span><span class="p">:</span><span class="w"> </span><span class="s2">"thresholds"</span><span class="p">},</span><span class="w">
            </span><span class="nl">"thresholds"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
              </span><span class="nl">"steps"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
                </span><span class="p">{</span><span class="nl">"color"</span><span class="p">:</span><span class="w"> </span><span class="s2">"green"</span><span class="p">,</span><span class="w"> </span><span class="nl">"value"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">},</span><span class="w">
                </span><span class="p">{</span><span class="nl">"color"</span><span class="p">:</span><span class="w"> </span><span class="s2">"yellow"</span><span class="p">,</span><span class="w"> </span><span class="nl">"value"</span><span class="p">:</span><span class="w"> </span><span class="mi">50</span><span class="p">},</span><span class="w">
                </span><span class="p">{</span><span class="nl">"color"</span><span class="p">:</span><span class="w"> </span><span class="s2">"red"</span><span class="p">,</span><span class="w"> </span><span class="nl">"value"</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="p">}</span><span class="w">
              </span><span class="p">]</span><span class="w">
            </span><span class="p">}</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Task Throughput"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"graph"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"targets"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"rate(task_completions_total[5m]) * 60"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"legendFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Tasks/minute - "</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">]</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Error Rate"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"graph"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"targets"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"rate(task_errors_total[5m]) / rate(task_completions_total[5m]) * 100"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"legendFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Error % - "</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">]</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Resource Utilization"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"heatmap"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"targets"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agent_cpu_percent"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"legendFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">]</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">]</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h4 id="1562-agent-performance-dashboard-configuration">15.6.2 Agent Performance Dashboard Configuration</h4>
<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"dashboard"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Mister Smith - Agent Performance"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"panels"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Agent Lifecycle Timeline"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"timeline"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"targets"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"agent_duration_seconds"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"legendFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">" - "</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">]</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Task Duration Distribution"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"histogram"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"targets"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"histogram_quantile(0.50, task_duration_seconds)"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"legendFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"50th percentile"</span><span class="w">
          </span><span class="p">},</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"histogram_quantile(0.95, task_duration_seconds)"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"legendFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"95th percentile"</span><span class="w">
          </span><span class="p">},</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"histogram_quantile(0.99, task_duration_seconds)"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"legendFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"99th percentile"</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">]</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Agent State Distribution"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"piechart"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"targets"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"sum by (state) (agent_active_count)"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"legendFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">]</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">]</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h3 id="157-performance-baselines">15.7 Performance Baselines</h3>

<h4 id="1571-agent-performance-baselines">15.7.1 Agent Performance Baselines</h4>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">agent_performance_baselines</span><span class="pi">:</span>
  <span class="na">task_completion</span><span class="pi">:</span>
    <span class="na">p95_duration_seconds</span><span class="pi">:</span> <span class="m">10</span>
    <span class="na">p99_duration_seconds</span><span class="pi">:</span> <span class="m">30</span>
    <span class="na">success_rate_percent</span><span class="pi">:</span> <span class="m">99</span>
  
  <span class="na">agent_spawn</span><span class="pi">:</span>
    <span class="na">p95_duration_seconds</span><span class="pi">:</span> <span class="m">2</span>
    <span class="na">p99_duration_seconds</span><span class="pi">:</span> <span class="m">5</span>
    <span class="na">success_rate_percent</span><span class="pi">:</span> <span class="m">99.5</span>
  
  <span class="na">resource_usage</span><span class="pi">:</span>
    <span class="na">memory_baseline_mb</span><span class="pi">:</span> <span class="m">256</span>
    <span class="na">memory_max_mb</span><span class="pi">:</span> <span class="m">512</span>
    <span class="na">cpu_baseline_percent</span><span class="pi">:</span> <span class="m">15</span>
    <span class="na">cpu_max_percent</span><span class="pi">:</span> <span class="m">80</span>

<span class="na">communication_baselines</span><span class="pi">:</span>
  <span class="na">message_delivery</span><span class="pi">:</span>
    <span class="na">p95_latency_ms</span><span class="pi">:</span> <span class="m">50</span>
    <span class="na">p99_latency_ms</span><span class="pi">:</span> <span class="m">100</span>
    <span class="na">delivery_success_rate</span><span class="pi">:</span> <span class="m">99.9</span>
  
  <span class="na">queue_processing</span><span class="pi">:</span>
    <span class="na">processing_time_ms</span><span class="pi">:</span> <span class="m">25</span>
    <span class="na">max_queue_depth</span><span class="pi">:</span> <span class="m">100</span>
  
  <span class="na">context_propagation</span><span class="pi">:</span>
    <span class="na">overhead_ms</span><span class="pi">:</span> <span class="m">1</span>
    <span class="na">success_rate</span><span class="pi">:</span> <span class="m">99.99</span>

<span class="na">system_baselines</span><span class="pi">:</span>
  <span class="na">throughput</span><span class="pi">:</span>
    <span class="na">tasks_per_minute</span><span class="pi">:</span> <span class="m">100</span>
    <span class="na">burst_capacity</span><span class="pi">:</span> <span class="m">500</span>
  
  <span class="na">utilization</span><span class="pi">:</span>
    <span class="na">optimal_range_percent</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">70</span><span class="pi">,</span> <span class="nv">85</span><span class="pi">]</span>
    <span class="na">max_sustainable_percent</span><span class="pi">:</span> <span class="m">90</span>
  
  <span class="na">error_rates</span><span class="pi">:</span>
    <span class="na">baseline_error_rate</span><span class="pi">:</span> <span class="m">0.01</span>
    <span class="na">alert_threshold</span><span class="pi">:</span> <span class="m">0.05</span>
    <span class="na">critical_threshold</span><span class="pi">:</span> <span class="m">0.10</span>
</code></pre></div></div>

<h3 id="158-otlp-configuration">15.8 OTLP Configuration</h3>

<h4 id="1581-opentelemetry-collector-configuration">15.8.1 OpenTelemetry Collector Configuration</h4>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">receivers</span><span class="pi">:</span>
  <span class="na">otlp</span><span class="pi">:</span>
    <span class="na">protocols</span><span class="pi">:</span>
      <span class="na">grpc</span><span class="pi">:</span>
        <span class="na">endpoint</span><span class="pi">:</span> <span class="s">0.0.0.0:4317</span>
      <span class="na">http</span><span class="pi">:</span>
        <span class="na">endpoint</span><span class="pi">:</span> <span class="s">0.0.0.0:4318</span>

<span class="na">processors</span><span class="pi">:</span>
  <span class="na">batch</span><span class="pi">:</span>
    <span class="na">send_batch_size</span><span class="pi">:</span> <span class="m">1024</span>
    <span class="na">timeout</span><span class="pi">:</span> <span class="s">1s</span>
  
  <span class="na">memory_limiter</span><span class="pi">:</span>
    <span class="na">limit_mib</span><span class="pi">:</span> <span class="m">512</span>
  
  <span class="na">resource</span><span class="pi">:</span>
    <span class="na">attributes</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="na">key</span><span class="pi">:</span> <span class="s">service.name</span>
        <span class="na">value</span><span class="pi">:</span> <span class="s">mister-smith-framework</span>
        <span class="na">action</span><span class="pi">:</span> <span class="s">upsert</span>

<span class="na">exporters</span><span class="pi">:</span>
  <span class="na">prometheus</span><span class="pi">:</span>
    <span class="na">endpoint</span><span class="pi">:</span> <span class="s2">"</span><span class="s">0.0.0.0:8889"</span>
    <span class="na">namespace</span><span class="pi">:</span> <span class="s">mister_smith</span>
  
  <span class="na">jaeger</span><span class="pi">:</span>
    <span class="na">endpoint</span><span class="pi">:</span> <span class="s">jaeger:14250</span>
    <span class="na">tls</span><span class="pi">:</span>
      <span class="na">insecure</span><span class="pi">:</span> <span class="kc">true</span>
  
  <span class="na">logging</span><span class="pi">:</span>
    <span class="na">loglevel</span><span class="pi">:</span> <span class="s">debug</span>

<span class="na">service</span><span class="pi">:</span>
  <span class="na">pipelines</span><span class="pi">:</span>
    <span class="na">traces</span><span class="pi">:</span>
      <span class="na">receivers</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">otlp</span><span class="pi">]</span>
      <span class="na">processors</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">memory_limiter</span><span class="pi">,</span> <span class="nv">batch</span><span class="pi">]</span>
      <span class="na">exporters</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">jaeger</span><span class="pi">,</span> <span class="nv">logging</span><span class="pi">]</span>
    
    <span class="na">metrics</span><span class="pi">:</span>
      <span class="na">receivers</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">otlp</span><span class="pi">]</span>
      <span class="na">processors</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">memory_limiter</span><span class="pi">,</span> <span class="nv">batch</span><span class="pi">,</span> <span class="nv">resource</span><span class="pi">]</span>
      <span class="na">exporters</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">prometheus</span><span class="pi">,</span> <span class="nv">logging</span><span class="pi">]</span>
    
    <span class="na">logs</span><span class="pi">:</span>
      <span class="na">receivers</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">otlp</span><span class="pi">]</span>
      <span class="na">processors</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">memory_limiter</span><span class="pi">,</span> <span class="nv">batch</span><span class="pi">]</span>
      <span class="na">exporters</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">logging</span><span class="pi">]</span>
</code></pre></div></div>

<hr />

<p>This framework provides comprehensive observability patterns for multi-agent systems without specifying particular technologies or implementations, suitable for specialized research agents to implement with their chosen technology stacks.</p>

<h1 id="security-framework---foundational-patterns">Security Framework - Foundational Patterns</h1>

<h2 id="framework-authority">Framework Authority</h2>
<p>This document implements specifications from the canonical tech-framework.md located at /Users/<USER>/<PERSON>-<PERSON>/<PERSON>-<PERSON>/tech-framework.md</p>

<p>As stated in the canonical framework: “Agents: use this framework as the canonical source.”</p>

<h2 id="purpose">Purpose</h2>
<p>Foundational security patterns for agent implementation focusing on basic authentication, authorization, TLS setup, and secrets management. This document provides pseudocode patterns and configurations for learning and implementation by agents.</p>

<h2 id="core-security-components">Core Security Components</h2>

<h3 id="1-basic-authentication-pattern">1. Basic Authentication Pattern</h3>

<p><strong>Pseudocode Pattern:</strong></p>
<pre><code class="language-pseudocode">// Basic JWT Authentication Flow
function authenticate_request(request):
    token = extract_bearer_token(request.headers)
    if not token:
        return error(401, "No authentication provided")
    
    claims = verify_jwt_token(token, public_key)
    if not claims or claims.expired:
        return error(401, "Invalid or expired token")
    
    request.context.user_id = claims.subject
    return success()

// Token Generation Pattern
function generate_auth_token(user_id, expires_in):
    claims = {
        subject: user_id,
        issued_at: current_timestamp(),
        expires_at: current_timestamp() + expires_in
    }
    return sign_jwt(claims, private_key)
</code></pre>

<p><strong>Configuration Pattern:</strong></p>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">authentication</span><span class="pi">:</span>
  <span class="na">jwt</span><span class="pi">:</span>
    <span class="na">algorithm</span><span class="pi">:</span> <span class="s">RS256</span>
    <span class="na">public_key_path</span><span class="pi">:</span> <span class="s">/path/to/public.pem</span>
    <span class="na">private_key_path</span><span class="pi">:</span> <span class="s">/path/to/private.pem</span>
    <span class="na">token_expiry</span><span class="pi">:</span> <span class="m">3600</span>  <span class="c1"># seconds</span>
</code></pre></div></div>

<h3 id="2-simple-authorization-pattern">2. Simple Authorization Pattern</h3>

<p><strong>Pseudocode Pattern:</strong></p>
<pre><code class="language-pseudocode">// Role-Based Access Control Pattern
function check_permission(user_id, resource, action):
    user_roles = get_user_roles(user_id)
    
    for role in user_roles:
        permissions = get_role_permissions(role)
        if has_permission(permissions, resource, action):
            return allow()
    
    return deny()

// Permission Definition Structure
permissions = {
    "reader": ["read:*"],
    "writer": ["read:*", "write:own"],
    "admin": ["read:*", "write:*", "delete:*"]
}
</code></pre>

<p><strong>Configuration Pattern:</strong></p>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">authorization</span><span class="pi">:</span>
  <span class="na">type</span><span class="pi">:</span> <span class="s">role_based</span>
  <span class="na">default_role</span><span class="pi">:</span> <span class="s">reader</span>
  <span class="na">roles</span><span class="pi">:</span>
    <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">reader</span>
      <span class="na">permissions</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">read</span><span class="pi">]</span>
    <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">writer</span>  
      <span class="na">permissions</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">read</span><span class="pi">,</span> <span class="nv">write</span><span class="pi">]</span>
    <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">admin</span>
      <span class="na">permissions</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">read</span><span class="pi">,</span> <span class="nv">write</span><span class="pi">,</span> <span class="nv">delete</span><span class="pi">,</span> <span class="nv">admin</span><span class="pi">]</span>
</code></pre></div></div>

<h3 id="3-tls-configuration-pattern">3. TLS Configuration Pattern</h3>

<p><strong>Pseudocode Pattern:</strong></p>
<pre><code class="language-pseudocode">// TLS Server Setup
function create_tls_server(cert_path, key_path):
    tls_config = {
        certificate: load_certificate(cert_path),
        private_key: load_private_key(key_path),
        min_version: "TLS_1_2",
        cipher_suites: ["TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"]
    }
    
    return create_server_with_tls(tls_config)

// TLS Client Configuration
function create_tls_client(ca_cert_path):
    tls_config = {
        ca_certificate: load_certificate(ca_cert_path),
        verify_hostname: true,
        min_version: "TLS_1_2"
    }
    
    return create_client_with_tls(tls_config)
</code></pre>

<p><strong>Configuration Pattern:</strong></p>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">tls</span><span class="pi">:</span>
  <span class="na">server</span><span class="pi">:</span>
    <span class="na">cert_path</span><span class="pi">:</span> <span class="s">/certs/server.crt</span>
    <span class="na">key_path</span><span class="pi">:</span> <span class="s">/certs/server.key</span>
    <span class="na">min_version</span><span class="pi">:</span> <span class="s">TLS1.2</span>
  <span class="na">client</span><span class="pi">:</span>
    <span class="na">ca_cert_path</span><span class="pi">:</span> <span class="s">/certs/ca.crt</span>
    <span class="na">verify_hostname</span><span class="pi">:</span> <span class="kc">true</span>
</code></pre></div></div>

<h3 id="4-basic-secrets-management">4. Basic Secrets Management</h3>

<p><strong>Pseudocode Pattern:</strong></p>
<pre><code class="language-pseudocode">// Environment-Based Secrets
function load_secrets():
    secrets = {
        database_url: get_env("DATABASE_URL"),
        api_key: get_env("API_KEY"),
        jwt_secret: get_env("JWT_SECRET")
    }
    
    // Validate required secrets
    for key, value in secrets:
        if not value:
            error("Missing required secret: " + key)
    
    return secrets

// File-Based Secrets Pattern
function load_secrets_from_file(path):
    if not file_exists(path):
        error("Secrets file not found")
    
    // Ensure proper file permissions
    if not check_file_permissions(path, "600"):
        error("Insecure secrets file permissions")
    
    return parse_secrets_file(path)
</code></pre>

<p><strong>Configuration Pattern:</strong></p>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">secrets</span><span class="pi">:</span>
  <span class="na">source</span><span class="pi">:</span> <span class="s">environment</span>  <span class="c1"># or 'file'</span>
  <span class="na">file_path</span><span class="pi">:</span> <span class="s">/secrets/app.secrets</span>
  <span class="na">required</span><span class="pi">:</span>
    <span class="pi">-</span> <span class="s">DATABASE_URL</span>
    <span class="pi">-</span> <span class="s">JWT_SECRET</span>
    <span class="pi">-</span> <span class="s">API_KEY</span>
</code></pre></div></div>

<h3 id="5-basic-security-middleware">5. Basic Security Middleware</h3>

<p><strong>Pseudocode Pattern:</strong></p>
<pre><code class="language-pseudocode">// Security Headers Middleware
function security_headers_middleware(request, response, next):
    response.headers.add("X-Content-Type-Options", "nosniff")
    response.headers.add("X-Frame-Options", "DENY")
    response.headers.add("X-XSS-Protection", "1; mode=block")
    response.headers.add("Strict-Transport-Security", "max-age=31536000")
    
    return next(request, response)

// Rate Limiting Pattern
function rate_limit_middleware(request, response, next):
    client_id = get_client_identifier(request)
    
    if exceeded_rate_limit(client_id):
        return error(429, "Rate limit exceeded")
    
    increment_request_count(client_id)
    return next(request, response)
</code></pre>

<h3 id="6-basic-audit-logging">6. Basic Audit Logging</h3>

<p><strong>Pseudocode Pattern:</strong></p>
<pre><code class="language-pseudocode">// Security Event Logging
function log_security_event(event_type, details):
    event = {
        timestamp: current_timestamp(),
        event_type: event_type,
        details: details,
        source_ip: get_request_ip(),
        user_id: get_current_user_id()
    }
    
    append_to_audit_log(event)

// Common Security Events to Log
security_events = [
    "authentication_success",
    "authentication_failure", 
    "authorization_denied",
    "invalid_token",
    "rate_limit_exceeded",
    "suspicious_activity"
]
</code></pre>

<h3 id="7-nats-security-patterns">7. NATS Security Patterns</h3>

<p><strong>Pseudocode Pattern - mTLS Configuration:</strong></p>
<pre><code class="language-pseudocode">// Initialize secure NATS connection with mTLS
function init_secure_nats(tenant_id):
    // Load certificates from secure storage
    ca_cert = load_file("/secrets/ca.crt")
    client_cert = load_file("/secrets/client.crt")
    client_key = load_file("/secrets/client.key")
    
    // Configure connection with tenant-specific identity
    options = {
        require_tls: true,
        ca_certificate: ca_cert,
        client_certificate: client_cert,
        client_key: client_key,
        client_name: "tenant_" + tenant_id + "_agent"
    }
    
    return connect_nats(NATS_URL, options)

// Apply rate limiting and backpressure
function configure_nats_limits(connection):
    connection.subscription_capacity = 1000  // Bounded buffer
    connection.ping_interval = 10  // seconds
    connection.reconnect_buffer_size = 8388608  // 8MB
</code></pre>

<p><strong>Configuration Pattern - Server mTLS:</strong></p>
<div class="language-hocon highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># NATS server mTLS configuration</span><span class="w">
</span><span class="nl">tls</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">cert_file</span><span class="p">:</span><span class="w"> </span><span class="s2">"./certs/nats-server.crt"</span><span class="w">
  </span><span class="nl">key_file</span><span class="p">:</span><span class="w">  </span><span class="s2">"./certs/nats-server.key"</span><span class="w">
  </span><span class="nl">ca_file</span><span class="p">:</span><span class="w">   </span><span class="s2">"./certs/ca.crt"</span><span class="w">
  </span><span class="nl">verify</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">  </span><span class="c1"># Enforce client certificates</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="nl">cluster</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">tls</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">cert_file</span><span class="p">:</span><span class="w"> </span><span class="s2">"./certs/cluster.crt"</span><span class="w">
    </span><span class="nl">key_file</span><span class="p">:</span><span class="w">  </span><span class="s2">"./certs/cluster.key"</span><span class="w">
    </span><span class="nl">ca_file</span><span class="p">:</span><span class="w">   </span><span class="s2">"./certs/ca.crt"</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<p><strong>Account-Based Tenant Isolation Pattern:</strong></p>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># NATS account isolation pattern</span>
<span class="na">account_isolation</span><span class="pi">:</span>
  <span class="na">principle</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Each</span><span class="nv"> </span><span class="s">tenant</span><span class="nv"> </span><span class="s">gets</span><span class="nv"> </span><span class="s">isolated</span><span class="nv"> </span><span class="s">NATS</span><span class="nv"> </span><span class="s">account"</span>
  <span class="na">benefits</span><span class="pi">:</span>
    <span class="pi">-</span> <span class="s">Complete namespace separation</span>
    <span class="pi">-</span> <span class="s">No subject prefix complexity</span>
    <span class="pi">-</span> <span class="s">Built-in multi-tenancy support</span>
  <span class="na">implementation</span><span class="pi">:</span> <span class="pi">|</span>
    <span class="s">nsc add account --name tenantA</span>
    <span class="s">nsc edit account --name tenantA \</span>
      <span class="s">--js-mem-storage 512M \</span>
      <span class="s">--js-disk-storage 1G \</span>
      <span class="s">--js-streams 10 \</span>
      <span class="s">--js-consumer 50</span>
</code></pre></div></div>

<p><strong>Fine-Grained ACL Configuration:</strong></p>
<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="err">//</span><span class="w"> </span><span class="err">Per-user</span><span class="w"> </span><span class="err">permission</span><span class="w"> </span><span class="err">model</span><span class="w">
</span><span class="p">{</span><span class="w">
  </span><span class="nl">"users"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
    </span><span class="p">{</span><span class="w">
      </span><span class="nl">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"admin"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"permissions"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"publish"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w"> </span><span class="s2">"&gt;"</span><span class="w"> </span><span class="p">],</span><span class="w">     </span><span class="err">//</span><span class="w"> </span><span class="err">Full</span><span class="w"> </span><span class="err">access</span><span class="w">
        </span><span class="nl">"subscribe"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w"> </span><span class="s2">"&gt;"</span><span class="w"> </span><span class="p">]</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">},</span><span class="w">
    </span><span class="p">{</span><span class="w">
      </span><span class="nl">"user"</span><span class="p">:</span><span class="w"> </span><span class="s2">"tenantA_bot"</span><span class="p">,</span><span class="w">
      </span><span class="nl">"permissions"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
        </span><span class="nl">"publish"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"allow"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"tenantA.&gt;"</span><span class="p">]</span><span class="w"> </span><span class="p">},</span><span class="w">
        </span><span class="nl">"subscribe"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nl">"allow"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"tenantA.&gt;"</span><span class="p">]</span><span class="w"> </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">]</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<p><strong>Resource Quota Enforcement:</strong></p>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># Per-account resource limits</span>
<span class="na">jetstream_limits</span><span class="pi">:</span>
  <span class="na">per_account</span><span class="pi">:</span>
    <span class="na">max_memory</span><span class="pi">:</span> <span class="s">512M</span>
    <span class="na">max_disk</span><span class="pi">:</span> <span class="s">1G</span>
    <span class="na">max_streams</span><span class="pi">:</span> <span class="m">10</span>
    <span class="na">max_consumers</span><span class="pi">:</span> <span class="m">100</span>
  <span class="na">per_stream</span><span class="pi">:</span>
    <span class="na">max_bytes</span><span class="pi">:</span> <span class="s">configurable</span>
    <span class="na">max_msgs</span><span class="pi">:</span> <span class="s">configurable</span>
    <span class="na">max_age</span><span class="pi">:</span> <span class="s">configurable</span>
    <span class="na">discard_policy</span><span class="pi">:</span> <span class="s">old_on_full</span>
  <span class="na">connection_limits</span><span class="pi">:</span>
    <span class="na">max_connections</span><span class="pi">:</span> <span class="m">100</span>
    <span class="na">max_subscriptions</span><span class="pi">:</span> <span class="m">1000</span>
    <span class="na">max_payload_size</span><span class="pi">:</span> <span class="s">1MB</span>
</code></pre></div></div>

<p><strong>Key Rotation Pattern:</strong></p>
<pre><code class="language-pseudocode">// Zero-downtime key rotation state machine
key_rotation_states = [
    "KEY_A_ACTIVE",
    "STAGING_NEW_KEY",
    "RELOADING_CONFIG",
    "KEY_B_ACTIVE"
]

// Signal handler for hot reload
function handle_sighup_signal():
    if signal_received == SIGHUP:
        // Atomically swap API keys in memory
        rotate_keys()
        reload_tls_certificates()
        update_active_connections()
</code></pre>

<p><strong>Critical NATS Security Patterns:</strong></p>
<ol>
  <li><strong>Never share accounts between tenants</strong> - Use NATS accounts for true isolation</li>
  <li><strong>Always enforce mTLS</strong> - Both client and cluster connections must verify certificates</li>
  <li><strong>Apply least privilege</strong> - Restrict subjects to minimum required patterns</li>
  <li><strong>Set resource quotas</strong> - Prevent any tenant from exhausting cluster resources</li>
  <li><strong>Rotate secrets regularly</strong> - Use SIGHUP for zero-downtime key rotation</li>
  <li><strong>Monitor wildcard usage</strong> - Detect and prevent unauthorized subject access</li>
</ol>

<h2 id="implementation-guidelines">Implementation Guidelines</h2>

<h3 id="authentication-flow">Authentication Flow</h3>
<ol>
  <li>Extract authentication token from request</li>
  <li>Verify token signature and expiration</li>
  <li>Extract user identity from token claims</li>
  <li>Attach identity to request context</li>
</ol>

<h3 id="authorization-flow">Authorization Flow</h3>
<ol>
  <li>Identify resource and action from request</li>
  <li>Retrieve user roles/permissions</li>
  <li>Check if user has required permission</li>
  <li>Allow or deny based on permission check</li>
</ol>

<h3 id="tls-setup-flow">TLS Setup Flow</h3>
<ol>
  <li>Generate or obtain TLS certificates</li>
  <li>Configure minimum TLS version (1.2+)</li>
  <li>Select secure cipher suites</li>
  <li>Enable hostname verification for clients</li>
</ol>

<h3 id="secrets-management-flow">Secrets Management Flow</h3>
<ol>
  <li>Define required secrets</li>
  <li>Load from environment or secure file</li>
  <li>Validate all required secrets present</li>
  <li>Use secrets for service configuration</li>
</ol>

<h3 id="nats-security-flow">NATS Security Flow</h3>
<ol>
  <li>Generate or obtain mTLS certificates for NATS</li>
  <li>Create isolated accounts for each tenant</li>
  <li>Configure ACLs for subject-based access control</li>
  <li>Set resource quotas to prevent resource exhaustion</li>
  <li>Implement key rotation handlers for hot reload</li>
</ol>

<h2 id="security-checklist-for-agents">Security Checklist for Agents</h2>

<ul class="task-list">
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Implement authentication before processing requests</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Check authorization for protected resources</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Enable TLS for all network communication</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Store secrets securely (environment variables or encrypted files)</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Add security headers to HTTP responses</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Implement rate limiting for API endpoints</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Log security-relevant events for audit trails</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Validate and sanitize all input data</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Use secure random number generation for tokens</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Set appropriate timeouts for authentication tokens</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Configure NATS with mTLS for secure messaging</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Implement account-based isolation for multi-tenant NATS</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Set resource quotas for NATS accounts and streams</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Configure fine-grained ACLs for NATS subjects</li>
  <li class="task-list-item"><input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />Implement key rotation for zero-downtime secret updates</li>
</ul>

<h2 id="configuration-templates">Configuration Templates</h2>

<h3 id="basic-security-configuration">Basic Security Configuration</h3>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">security</span><span class="pi">:</span>
  <span class="na">authentication</span><span class="pi">:</span>
    <span class="na">enabled</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">type</span><span class="pi">:</span> <span class="s">jwt</span>
    <span class="na">token_expiry</span><span class="pi">:</span> <span class="m">3600</span>
  
  <span class="na">authorization</span><span class="pi">:</span>
    <span class="na">enabled</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">type</span><span class="pi">:</span> <span class="s">role_based</span>
    <span class="na">default_role</span><span class="pi">:</span> <span class="s">reader</span>
  
  <span class="na">tls</span><span class="pi">:</span>
    <span class="na">enabled</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">min_version</span><span class="pi">:</span> <span class="s">TLS1.2</span>
  
  <span class="na">rate_limiting</span><span class="pi">:</span>
    <span class="na">enabled</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">requests_per_minute</span><span class="pi">:</span> <span class="m">60</span>
  
  <span class="na">audit_logging</span><span class="pi">:</span>
    <span class="na">enabled</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">log_path</span><span class="pi">:</span> <span class="s">/logs/security.log</span>
  
  <span class="na">nats</span><span class="pi">:</span>
    <span class="na">enabled</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">mtls</span><span class="pi">:</span>
      <span class="na">cert_path</span><span class="pi">:</span> <span class="s">/certs/nats-client.crt</span>
      <span class="na">key_path</span><span class="pi">:</span> <span class="s">/certs/nats-client.key</span>
      <span class="na">ca_path</span><span class="pi">:</span> <span class="s">/certs/ca.crt</span>
    <span class="na">account_isolation</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">resource_quotas</span><span class="pi">:</span>
      <span class="na">max_memory</span><span class="pi">:</span> <span class="s">512M</span>
      <span class="na">max_disk</span><span class="pi">:</span> <span class="s">1G</span>
      <span class="na">max_connections</span><span class="pi">:</span> <span class="m">100</span>
</code></pre></div></div>

<h2 id="11-hook-execution-sandbox-pattern">11. Hook Execution Sandbox Pattern</h2>

<h3 id="111-non-root-user-execution-pattern">11.1 Non-Root User Execution Pattern</h3>

<p><strong>Pseudocode Pattern:</strong></p>
<pre><code class="language-pseudocode">// Hook execution with privilege isolation
function execute_hook_safely(hook_script, payload):
    // Ensure hook runs under non-root user
    execution_user = get_non_privileged_user()  // e.g., "claude-hook-runner"

    // Create isolated execution environment
    sandbox_config = {
        user: execution_user,
        working_directory: "/tmp/hook-sandbox",
        environment_variables: filter_safe_env_vars(),
        resource_limits: {
            max_memory: "128M",
            max_cpu_time: "30s",
            max_file_descriptors: 64,
            max_processes: 1
        },
        filesystem_access: {
            read_only: ["/usr", "/lib", "/bin"],
            read_write: ["/tmp/hook-sandbox"],
            no_access: ["/etc", "/root", "/home"]
        }
    }

    // Execute with timeout and resource constraints
    result = execute_with_sandbox(hook_script, payload, sandbox_config)

    // Clean up sandbox environment
    cleanup_sandbox_directory()

    return result

// User privilege management
function setup_hook_user():
    // Create dedicated user for hook execution
    create_user("claude-hook-runner", {
        home_directory: "/var/lib/claude-hooks",
        shell: "/bin/bash",
        groups: ["claude-hooks"],
        no_login: false,
        system_user: true
    })

    // Set up hook directory permissions
    set_directory_permissions("/var/lib/claude-hooks", {
        owner: "claude-hook-runner",
        group: "claude-hooks",
        permissions: "750"
    })
</code></pre>

<p><strong>Configuration Pattern:</strong></p>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="na">hook_security</span><span class="pi">:</span>
  <span class="na">execution_user</span><span class="pi">:</span> <span class="s">claude-hook-runner</span>
  <span class="na">sandbox_directory</span><span class="pi">:</span> <span class="s">/tmp/hook-sandbox</span>
  <span class="na">resource_limits</span><span class="pi">:</span>
    <span class="na">max_memory_mb</span><span class="pi">:</span> <span class="m">128</span>
    <span class="na">max_cpu_seconds</span><span class="pi">:</span> <span class="m">30</span>
    <span class="na">max_file_descriptors</span><span class="pi">:</span> <span class="m">64</span>
    <span class="na">max_processes</span><span class="pi">:</span> <span class="m">1</span>

  <span class="na">filesystem_isolation</span><span class="pi">:</span>
    <span class="na">read_only_paths</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">/usr</span>
      <span class="pi">-</span> <span class="s">/lib</span>
      <span class="pi">-</span> <span class="s">/bin</span>
      <span class="pi">-</span> <span class="s">/etc/passwd</span>
    <span class="na">read_write_paths</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">/tmp/hook-sandbox</span>
    <span class="na">blocked_paths</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">/etc</span>
      <span class="pi">-</span> <span class="s">/root</span>
      <span class="pi">-</span> <span class="s">/home</span>
      <span class="pi">-</span> <span class="s">/var/lib/claude-hooks/.ssh</span>

  <span class="na">network_isolation</span><span class="pi">:</span>
    <span class="na">allow_outbound</span><span class="pi">:</span> <span class="kc">false</span>
    <span class="na">allow_localhost</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">blocked_ports</span><span class="pi">:</span> <span class="pi">[</span><span class="nv">22</span><span class="pi">,</span> <span class="nv">3389</span><span class="pi">,</span> <span class="nv">5432</span><span class="pi">,</span> <span class="nv">27017</span><span class="pi">]</span>
</code></pre></div></div>

<h3 id="112-hook-script-validation-pattern">11.2 Hook Script Validation Pattern</h3>

<p><strong>Pseudocode Pattern:</strong></p>
<pre><code class="language-pseudocode">// Validate hook scripts before execution
function validate_hook_script(script_path):
    // Check file permissions
    file_info = get_file_info(script_path)
    if file_info.owner != "claude-hook-runner":
        return error("Hook script must be owned by claude-hook-runner")

    if file_info.permissions &amp; WORLD_WRITABLE:
        return error("Hook script cannot be world-writable")

    // Validate script content
    script_content = read_file(script_path)

    // Check for dangerous patterns
    dangerous_patterns = [
        "sudo", "su -", "chmod 777", "rm -rf /",
        "curl.*|.*sh", "wget.*|.*sh", "eval",
        "/etc/passwd", "/etc/shadow"
    ]

    for pattern in dangerous_patterns:
        if matches_pattern(script_content, pattern):
            return error("Hook script contains dangerous pattern: " + pattern)

    // Validate shebang
    if not script_content.starts_with("#!/"):
        return error("Hook script must have valid shebang")

    return success()

// Hook directory security
function secure_hook_directory(hook_dir):
    // Ensure proper ownership and permissions
    set_ownership(hook_dir, "claude-hook-runner", "claude-hooks")
    set_permissions(hook_dir, "750")  // rwxr-x---

    // Validate all hook scripts
    for script in list_files(hook_dir):
        validate_hook_script(script)
        set_permissions(script, "750")  // rwxr-x---
</code></pre>

<h2 id="pattern-implementation-notes">Pattern Implementation Notes</h2>

<ul>
  <li>All patterns are foundational and can be extended as needed</li>
  <li>Focus on understanding core security concepts before adding complexity</li>
  <li>Use standard libraries for cryptographic operations</li>
  <li>Test security configurations in isolated environments</li>
  <li>Follow the principle of least privilege for all access control</li>
</ul>

<h1 id="concrete-implementations">CONCRETE IMPLEMENTATIONS</h1>

<p>This section provides production-ready implementations that leave zero security implementation decisions for consumers. All security parameters, algorithms, and procedures are specified concretely.</p>

<h2 id="1-certificate-management-implementation">1. Certificate Management Implementation</h2>

<h3 id="11-certificate-generation-scripts">1.1 Certificate Generation Scripts</h3>

<p><strong>Complete Certificate Authority Setup:</strong></p>
<div class="language-bash highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c">#!/bin/bash</span>
<span class="c"># generate_ca.sh - Complete CA setup for Mister Smith Framework</span>

<span class="nb">set</span> <span class="nt">-euo</span> pipefail

<span class="nv">CA_DIR</span><span class="o">=</span><span class="s2">"/etc/mister-smith/certs"</span>
<span class="nv">CA_KEY_SIZE</span><span class="o">=</span>4096
<span class="nv">CERT_VALIDITY_DAYS</span><span class="o">=</span>3650
<span class="nv">SERVER_CERT_VALIDITY_DAYS</span><span class="o">=</span>90
<span class="nv">CLIENT_CERT_VALIDITY_DAYS</span><span class="o">=</span>365

<span class="c"># Create directory structure</span>
<span class="nb">mkdir</span> <span class="nt">-p</span> <span class="nv">$CA_DIR</span>/<span class="o">{</span>ca,server,client,crl<span class="o">}</span>
<span class="nb">cd</span> <span class="nv">$CA_DIR</span>

<span class="c"># Generate CA private key (RSA 4096-bit)</span>
openssl genrsa <span class="nt">-out</span> ca/ca-key.pem <span class="nv">$CA_KEY_SIZE</span>

<span class="c"># Generate CA certificate (10 years)</span>
openssl req <span class="nt">-new</span> <span class="nt">-x509</span> <span class="nt">-days</span> <span class="nv">$CERT_VALIDITY_DAYS</span> <span class="nt">-key</span> ca/ca-key.pem <span class="se">\</span>
    <span class="nt">-out</span> ca/ca-cert.pem <span class="se">\</span>
    <span class="nt">-subj</span> <span class="s2">"/C=US/ST=CA/L=San Francisco/O=Mister Smith Framework/OU=Security/CN=Mister Smith CA"</span>

<span class="c"># Generate server private key</span>
openssl genrsa <span class="nt">-out</span> server/server-key.pem 4096

<span class="c"># Generate server certificate signing request</span>
openssl req <span class="nt">-new</span> <span class="nt">-key</span> server/server-key.pem <span class="nt">-out</span> server/server.csr <span class="se">\</span>
    <span class="nt">-subj</span> <span class="s2">"/C=US/ST=CA/L=San Francisco/O=Mister Smith Framework/OU=Services/CN=mister-smith.local"</span>

<span class="c"># Create server certificate extensions</span>
<span class="nb">cat</span> <span class="o">&gt;</span> server/server-ext.cnf <span class="o">&lt;&lt;</span> <span class="no">EOF</span><span class="sh">
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = mister-smith.local
DNS.2 = localhost
DNS.3 = *.mister-smith.local
IP.1 = 127.0.0.1
IP.2 = ::1
</span><span class="no">EOF

</span><span class="c"># Sign server certificate (90 days)</span>
openssl x509 <span class="nt">-req</span> <span class="nt">-in</span> server/server.csr <span class="nt">-CA</span> ca/ca-cert.pem <span class="nt">-CAkey</span> ca/ca-key.pem <span class="se">\</span>
    <span class="nt">-out</span> server/server-cert.pem <span class="nt">-days</span> <span class="nv">$SERVER_CERT_VALIDITY_DAYS</span> <span class="se">\</span>
    <span class="nt">-extensions</span> v3_req <span class="nt">-extfile</span> server/server-ext.cnf <span class="nt">-CAcreateserial</span>

<span class="c"># Generate client private key</span>
openssl genrsa <span class="nt">-out</span> client/client-key.pem 4096

<span class="c"># Generate client certificate signing request</span>
openssl req <span class="nt">-new</span> <span class="nt">-key</span> client/client-key.pem <span class="nt">-out</span> client/client.csr <span class="se">\</span>
    <span class="nt">-subj</span> <span class="s2">"/C=US/ST=CA/L=San Francisco/O=Mister Smith Framework/OU=Clients/CN=mister-smith-client"</span>

<span class="c"># Create client certificate extensions</span>
<span class="nb">cat</span> <span class="o">&gt;</span> client/client-ext.cnf <span class="o">&lt;&lt;</span> <span class="no">EOF</span><span class="sh">
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature
extendedKeyUsage = clientAuth
</span><span class="no">EOF

</span><span class="c"># Sign client certificate (365 days)</span>
openssl x509 <span class="nt">-req</span> <span class="nt">-in</span> client/client.csr <span class="nt">-CA</span> ca/ca-cert.pem <span class="nt">-CAkey</span> ca/ca-key.pem <span class="se">\</span>
    <span class="nt">-out</span> client/client-cert.pem <span class="nt">-days</span> <span class="nv">$CLIENT_CERT_VALIDITY_DAYS</span> <span class="se">\</span>
    <span class="nt">-extensions</span> v3_req <span class="nt">-extfile</span> client/client-ext.cnf <span class="nt">-CAcreateserial</span>

<span class="c"># Set proper permissions</span>
<span class="nb">chmod </span>600 ca/ca-key.pem server/server-key.pem client/client-key.pem
<span class="nb">chmod </span>644 ca/ca-cert.pem server/server-cert.pem client/client-cert.pem

<span class="nb">echo</span> <span class="s2">"Certificates generated successfully in </span><span class="nv">$CA_DIR</span><span class="s2">"</span>
</code></pre></div></div>

<p><strong>Certificate Rotation Script:</strong></p>
<div class="language-bash highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c">#!/bin/bash</span>
<span class="c"># rotate_certs.sh - Zero-downtime certificate rotation</span>

<span class="nb">set</span> <span class="nt">-euo</span> pipefail

<span class="nv">CA_DIR</span><span class="o">=</span><span class="s2">"/etc/mister-smith/certs"</span>
<span class="nv">BACKUP_DIR</span><span class="o">=</span><span class="s2">"/etc/mister-smith/certs/backup/</span><span class="si">$(</span><span class="nb">date</span> +%Y%m%d_%H%M%S<span class="si">)</span><span class="s2">"</span>

<span class="c"># Create backup</span>
<span class="nb">mkdir</span> <span class="nt">-p</span> <span class="nv">$BACKUP_DIR</span>
<span class="nb">cp</span> <span class="nt">-r</span> <span class="nv">$CA_DIR</span>/<span class="k">*</span> <span class="nv">$BACKUP_DIR</span>/

<span class="c"># Check certificate expiration (warn at 30 days)</span>
check_expiration<span class="o">()</span> <span class="o">{</span>
    <span class="nb">local </span><span class="nv">cert_file</span><span class="o">=</span><span class="nv">$1</span>
    <span class="nb">local </span><span class="nv">threshold_days</span><span class="o">=</span>30
    
    <span class="nv">expiry_date</span><span class="o">=</span><span class="si">$(</span>openssl x509 <span class="nt">-in</span> <span class="nv">$cert_file</span> <span class="nt">-noout</span> <span class="nt">-enddate</span> | <span class="nb">cut</span> <span class="nt">-d</span><span class="o">=</span> <span class="nt">-f2</span><span class="si">)</span>
    <span class="nv">expiry_timestamp</span><span class="o">=</span><span class="si">$(</span><span class="nb">date</span> <span class="nt">-d</span> <span class="s2">"</span><span class="nv">$expiry_date</span><span class="s2">"</span> +%s<span class="si">)</span>
    <span class="nv">current_timestamp</span><span class="o">=</span><span class="si">$(</span><span class="nb">date</span> +%s<span class="si">)</span>
    <span class="nv">days_until_expiry</span><span class="o">=</span><span class="k">$((</span> <span class="o">(</span>expiry_timestamp <span class="o">-</span> current_timestamp<span class="o">)</span> <span class="o">/</span> <span class="m">86400</span> <span class="k">))</span>
    
    <span class="k">if</span> <span class="o">[</span> <span class="nv">$days_until_expiry</span> <span class="nt">-le</span> <span class="nv">$threshold_days</span> <span class="o">]</span><span class="p">;</span> <span class="k">then
        </span><span class="nb">echo</span> <span class="s2">"WARNING: Certificate </span><span class="nv">$cert_file</span><span class="s2"> expires in </span><span class="nv">$days_until_expiry</span><span class="s2"> days"</span>
        <span class="k">return </span>1
    <span class="k">fi
    return </span>0
<span class="o">}</span>

<span class="c"># Rotate server certificate</span>
rotate_server_cert<span class="o">()</span> <span class="o">{</span>
    <span class="nb">echo</span> <span class="s2">"Rotating server certificate..."</span>
    
    <span class="c"># Generate new server key and certificate</span>
    openssl genrsa <span class="nt">-out</span> <span class="nv">$CA_DIR</span>/server/server-key-new.pem 4096
    openssl req <span class="nt">-new</span> <span class="nt">-key</span> <span class="nv">$CA_DIR</span>/server/server-key-new.pem <span class="nt">-out</span> <span class="nv">$CA_DIR</span>/server/server-new.csr <span class="se">\</span>
        <span class="nt">-subj</span> <span class="s2">"/C=US/ST=CA/L=San Francisco/O=Mister Smith Framework/OU=Services/CN=mister-smith.local"</span>
    
    openssl x509 <span class="nt">-req</span> <span class="nt">-in</span> <span class="nv">$CA_DIR</span>/server/server-new.csr <span class="nt">-CA</span> <span class="nv">$CA_DIR</span>/ca/ca-cert.pem <span class="se">\</span>
        <span class="nt">-CAkey</span> <span class="nv">$CA_DIR</span>/ca/ca-key.pem <span class="nt">-out</span> <span class="nv">$CA_DIR</span>/server/server-cert-new.pem <span class="se">\</span>
        <span class="nt">-days</span> 90 <span class="nt">-extensions</span> v3_req <span class="nt">-extfile</span> <span class="nv">$CA_DIR</span>/server/server-ext.cnf <span class="nt">-CAcreateserial</span>
    
    <span class="c"># Atomic replacement</span>
    <span class="nb">mv</span> <span class="nv">$CA_DIR</span>/server/server-cert-new.pem <span class="nv">$CA_DIR</span>/server/server-cert.pem
    <span class="nb">mv</span> <span class="nv">$CA_DIR</span>/server/server-key-new.pem <span class="nv">$CA_DIR</span>/server/server-key.pem
    
    <span class="c"># Send SIGHUP to services for hot reload</span>
    systemctl reload mister-smith-api
    systemctl reload nats-server
    
    <span class="nb">echo</span> <span class="s2">"Server certificate rotated successfully"</span>
<span class="o">}</span>

<span class="c"># Check and rotate if needed</span>
<span class="k">if</span> <span class="o">!</span> check_expiration <span class="nv">$CA_DIR</span>/server/server-cert.pem<span class="p">;</span> <span class="k">then
    </span>rotate_server_cert
<span class="k">fi</span>
</code></pre></div></div>

<h3 id="12-rustls-certificate-management-implementation">1.2 Rustls Certificate Management Implementation</h3>

<p><strong>Complete Certificate Manager:</strong></p>
<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// certificate_manager.rs</span>
<span class="k">use</span> <span class="nn">rustls</span><span class="p">::{</span><span class="n">Certificate</span><span class="p">,</span> <span class="n">PrivateKey</span><span class="p">,</span> <span class="n">ServerConfig</span><span class="p">,</span> <span class="n">ClientConfig</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">rustls_pemfile</span><span class="p">::{</span><span class="n">certs</span><span class="p">,</span> <span class="n">pkcs8_private_keys</span><span class="p">,</span> <span class="n">rsa_private_keys</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">fs</span><span class="p">::</span><span class="n">File</span><span class="p">;</span>
<span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">io</span><span class="p">::</span><span class="n">BufReader</span><span class="p">;</span>
<span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">path</span><span class="p">::</span><span class="n">Path</span><span class="p">;</span>
<span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">sync</span><span class="p">::</span><span class="nb">Arc</span><span class="p">;</span>
<span class="k">use</span> <span class="nn">tokio</span><span class="p">::</span><span class="nn">time</span><span class="p">::{</span><span class="n">Duration</span><span class="p">,</span> <span class="n">interval</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">tracing</span><span class="p">::{</span><span class="n">info</span><span class="p">,</span> <span class="n">warn</span><span class="p">,</span> <span class="n">error</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">anyhow</span><span class="p">::{</span><span class="nb">Result</span><span class="p">,</span> <span class="n">Context</span><span class="p">};</span>

<span class="nd">#[derive(Clone)]</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">CertificateManager</span> <span class="p">{</span>
    <span class="n">ca_cert_path</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="n">server_cert_path</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="n">server_key_path</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="n">client_cert_path</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="n">client_key_path</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">CertificateManager</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">new</span><span class="p">()</span> <span class="k">-&gt;</span> <span class="k">Self</span> <span class="p">{</span>
        <span class="k">Self</span> <span class="p">{</span>
            <span class="n">ca_cert_path</span><span class="p">:</span> <span class="s">"/etc/mister-smith/certs/ca/ca-cert.pem"</span><span class="nf">.to_string</span><span class="p">(),</span>
            <span class="n">server_cert_path</span><span class="p">:</span> <span class="s">"/etc/mister-smith/certs/server/server-cert.pem"</span><span class="nf">.to_string</span><span class="p">(),</span>
            <span class="n">server_key_path</span><span class="p">:</span> <span class="s">"/etc/mister-smith/certs/server/server-key.pem"</span><span class="nf">.to_string</span><span class="p">(),</span>
            <span class="n">client_cert_path</span><span class="p">:</span> <span class="s">"/etc/mister-smith/certs/client/client-cert.pem"</span><span class="nf">.to_string</span><span class="p">(),</span>
            <span class="n">client_key_path</span><span class="p">:</span> <span class="s">"/etc/mister-smith/certs/client/client-key.pem"</span><span class="nf">.to_string</span><span class="p">(),</span>
        <span class="p">}</span>
    <span class="p">}</span>

    <span class="cd">/// Load certificates from PEM files</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">load_certificates</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">path</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nb">Vec</span><span class="o">&lt;</span><span class="n">Certificate</span><span class="o">&gt;&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">file</span> <span class="o">=</span> <span class="nn">File</span><span class="p">::</span><span class="nf">open</span><span class="p">(</span><span class="n">path</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"Failed to open certificate file: {}"</span><span class="p">,</span> <span class="n">path</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">reader</span> <span class="o">=</span> <span class="nn">BufReader</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">file</span><span class="p">);</span>
        
        <span class="k">let</span> <span class="n">certs</span> <span class="o">=</span> <span class="nf">certs</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="n">reader</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to parse certificates"</span><span class="p">)</span><span class="o">?</span>
            <span class="nf">.into_iter</span><span class="p">()</span>
            <span class="nf">.map</span><span class="p">(</span><span class="n">Certificate</span><span class="p">)</span>
            <span class="nf">.collect</span><span class="p">();</span>

        <span class="k">if</span> <span class="n">certs</span><span class="nf">.is_empty</span><span class="p">()</span> <span class="p">{</span>
            <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span><span class="s">"No certificates found in file: {}"</span><span class="p">,</span> <span class="n">path</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Loaded {} certificates from {}"</span><span class="p">,</span> <span class="n">certs</span><span class="nf">.len</span><span class="p">(),</span> <span class="n">path</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(</span><span class="n">certs</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Load private key from PEM file</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">load_private_key</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">path</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">PrivateKey</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">file</span> <span class="o">=</span> <span class="nn">File</span><span class="p">::</span><span class="nf">open</span><span class="p">(</span><span class="n">path</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"Failed to open private key file: {}"</span><span class="p">,</span> <span class="n">path</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">reader</span> <span class="o">=</span> <span class="nn">BufReader</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">file</span><span class="p">);</span>

        <span class="c1">// Try PKCS8 format first</span>
        <span class="k">if</span> <span class="k">let</span> <span class="nf">Ok</span><span class="p">(</span><span class="k">mut</span> <span class="n">keys</span><span class="p">)</span> <span class="o">=</span> <span class="nf">pkcs8_private_keys</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="n">reader</span><span class="p">)</span> <span class="p">{</span>
            <span class="k">if</span> <span class="o">!</span><span class="n">keys</span><span class="nf">.is_empty</span><span class="p">()</span> <span class="p">{</span>
                <span class="nd">info!</span><span class="p">(</span><span class="s">"Loaded PKCS8 private key from {}"</span><span class="p">,</span> <span class="n">path</span><span class="p">);</span>
                <span class="k">return</span> <span class="nf">Ok</span><span class="p">(</span><span class="nf">PrivateKey</span><span class="p">(</span><span class="n">keys</span><span class="nf">.remove</span><span class="p">(</span><span class="mi">0</span><span class="p">)));</span>
            <span class="p">}</span>
        <span class="p">}</span>

        <span class="c1">// Reset reader and try RSA format</span>
        <span class="k">let</span> <span class="n">file</span> <span class="o">=</span> <span class="nn">File</span><span class="p">::</span><span class="nf">open</span><span class="p">(</span><span class="n">path</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">reader</span> <span class="o">=</span> <span class="nn">BufReader</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">file</span><span class="p">);</span>
        
        <span class="k">let</span> <span class="k">mut</span> <span class="n">keys</span> <span class="o">=</span> <span class="nf">rsa_private_keys</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="n">reader</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to parse RSA private key"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="k">if</span> <span class="n">keys</span><span class="nf">.is_empty</span><span class="p">()</span> <span class="p">{</span>
            <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span><span class="s">"No private keys found in file: {}"</span><span class="p">,</span> <span class="n">path</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Loaded RSA private key from {}"</span><span class="p">,</span> <span class="n">path</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(</span><span class="nf">PrivateKey</span><span class="p">(</span><span class="n">keys</span><span class="nf">.remove</span><span class="p">(</span><span class="mi">0</span><span class="p">)))</span>
    <span class="p">}</span>

    <span class="cd">/// Create TLS server configuration with mTLS</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">create_server_config</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nb">Arc</span><span class="o">&lt;</span><span class="n">ServerConfig</span><span class="o">&gt;&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">certs</span> <span class="o">=</span> <span class="k">self</span><span class="nf">.load_certificates</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.server_cert_path</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
        <span class="k">let</span> <span class="n">key</span> <span class="o">=</span> <span class="k">self</span><span class="nf">.load_private_key</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.server_key_path</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
        <span class="k">let</span> <span class="n">ca_certs</span> <span class="o">=</span> <span class="k">self</span><span class="nf">.load_certificates</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.ca_cert_path</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="k">let</span> <span class="k">mut</span> <span class="n">root_store</span> <span class="o">=</span> <span class="nn">rustls</span><span class="p">::</span><span class="nn">RootCertStore</span><span class="p">::</span><span class="nf">empty</span><span class="p">();</span>
        <span class="k">for</span> <span class="n">cert</span> <span class="k">in</span> <span class="n">ca_certs</span> <span class="p">{</span>
            <span class="n">root_store</span><span class="nf">.add</span><span class="p">(</span><span class="o">&amp;</span><span class="n">cert</span><span class="p">)</span>
                <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to add CA certificate to root store"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
        <span class="p">}</span>

        <span class="k">let</span> <span class="n">client_cert_verifier</span> <span class="o">=</span> <span class="nn">rustls</span><span class="p">::</span><span class="nn">server</span><span class="p">::</span><span class="nn">AllowAnyAuthenticatedClient</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">root_store</span><span class="p">);</span>

        <span class="k">let</span> <span class="n">config</span> <span class="o">=</span> <span class="nn">ServerConfig</span><span class="p">::</span><span class="nf">builder</span><span class="p">()</span>
            <span class="nf">.with_cipher_suites</span><span class="p">(</span><span class="o">&amp;</span><span class="p">[</span>
                <span class="nn">rustls</span><span class="p">::</span><span class="nn">cipher_suite</span><span class="p">::</span><span class="n">TLS13_AES_256_GCM_SHA384</span><span class="p">,</span>
                <span class="nn">rustls</span><span class="p">::</span><span class="nn">cipher_suite</span><span class="p">::</span><span class="n">TLS13_CHACHA20_POLY1305_SHA256</span><span class="p">,</span>
                <span class="nn">rustls</span><span class="p">::</span><span class="nn">cipher_suite</span><span class="p">::</span><span class="n">TLS13_AES_128_GCM_SHA256</span><span class="p">,</span>
            <span class="p">])</span>
            <span class="nf">.with_kx_groups</span><span class="p">(</span><span class="o">&amp;</span><span class="p">[</span>
                <span class="o">&amp;</span><span class="nn">rustls</span><span class="p">::</span><span class="nn">kx_group</span><span class="p">::</span><span class="n">X25519</span><span class="p">,</span>
                <span class="o">&amp;</span><span class="nn">rustls</span><span class="p">::</span><span class="nn">kx_group</span><span class="p">::</span><span class="n">SECP384R1</span><span class="p">,</span>
                <span class="o">&amp;</span><span class="nn">rustls</span><span class="p">::</span><span class="nn">kx_group</span><span class="p">::</span><span class="n">SECP256R1</span><span class="p">,</span>
            <span class="p">])</span>
            <span class="nf">.with_protocol_versions</span><span class="p">(</span><span class="o">&amp;</span><span class="p">[</span><span class="o">&amp;</span><span class="nn">rustls</span><span class="p">::</span><span class="nn">version</span><span class="p">::</span><span class="n">TLS13</span><span class="p">])</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to configure TLS parameters"</span><span class="p">)</span><span class="o">?</span>
            <span class="nf">.with_client_cert_verifier</span><span class="p">(</span><span class="n">client_cert_verifier</span><span class="p">)</span>
            <span class="nf">.with_single_cert</span><span class="p">(</span><span class="n">certs</span><span class="p">,</span> <span class="n">key</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to configure server certificate"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Created TLS server configuration with mTLS"</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(</span><span class="nn">Arc</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">config</span><span class="p">))</span>
    <span class="p">}</span>

    <span class="cd">/// Create TLS client configuration</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">create_client_config</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nb">Arc</span><span class="o">&lt;</span><span class="n">ClientConfig</span><span class="o">&gt;&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">certs</span> <span class="o">=</span> <span class="k">self</span><span class="nf">.load_certificates</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.client_cert_path</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
        <span class="k">let</span> <span class="n">key</span> <span class="o">=</span> <span class="k">self</span><span class="nf">.load_private_key</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.client_key_path</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
        <span class="k">let</span> <span class="n">ca_certs</span> <span class="o">=</span> <span class="k">self</span><span class="nf">.load_certificates</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.ca_cert_path</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="k">let</span> <span class="k">mut</span> <span class="n">root_store</span> <span class="o">=</span> <span class="nn">rustls</span><span class="p">::</span><span class="nn">RootCertStore</span><span class="p">::</span><span class="nf">empty</span><span class="p">();</span>
        <span class="k">for</span> <span class="n">cert</span> <span class="k">in</span> <span class="n">ca_certs</span> <span class="p">{</span>
            <span class="n">root_store</span><span class="nf">.add</span><span class="p">(</span><span class="o">&amp;</span><span class="n">cert</span><span class="p">)</span>
                <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to add CA certificate to root store"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
        <span class="p">}</span>

        <span class="k">let</span> <span class="n">config</span> <span class="o">=</span> <span class="nn">ClientConfig</span><span class="p">::</span><span class="nf">builder</span><span class="p">()</span>
            <span class="nf">.with_cipher_suites</span><span class="p">(</span><span class="o">&amp;</span><span class="p">[</span>
                <span class="nn">rustls</span><span class="p">::</span><span class="nn">cipher_suite</span><span class="p">::</span><span class="n">TLS13_AES_256_GCM_SHA384</span><span class="p">,</span>
                <span class="nn">rustls</span><span class="p">::</span><span class="nn">cipher_suite</span><span class="p">::</span><span class="n">TLS13_CHACHA20_POLY1305_SHA256</span><span class="p">,</span>
                <span class="nn">rustls</span><span class="p">::</span><span class="nn">cipher_suite</span><span class="p">::</span><span class="n">TLS13_AES_128_GCM_SHA256</span><span class="p">,</span>
            <span class="p">])</span>
            <span class="nf">.with_kx_groups</span><span class="p">(</span><span class="o">&amp;</span><span class="p">[</span>
                <span class="o">&amp;</span><span class="nn">rustls</span><span class="p">::</span><span class="nn">kx_group</span><span class="p">::</span><span class="n">X25519</span><span class="p">,</span>
                <span class="o">&amp;</span><span class="nn">rustls</span><span class="p">::</span><span class="nn">kx_group</span><span class="p">::</span><span class="n">SECP384R1</span><span class="p">,</span>
                <span class="o">&amp;</span><span class="nn">rustls</span><span class="p">::</span><span class="nn">kx_group</span><span class="p">::</span><span class="n">SECP256R1</span><span class="p">,</span>
            <span class="p">])</span>
            <span class="nf">.with_protocol_versions</span><span class="p">(</span><span class="o">&amp;</span><span class="p">[</span><span class="o">&amp;</span><span class="nn">rustls</span><span class="p">::</span><span class="nn">version</span><span class="p">::</span><span class="n">TLS13</span><span class="p">])</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to configure TLS parameters"</span><span class="p">)</span><span class="o">?</span>
            <span class="nf">.with_root_certificates</span><span class="p">(</span><span class="n">root_store</span><span class="p">)</span>
            <span class="nf">.with_single_cert</span><span class="p">(</span><span class="n">certs</span><span class="p">,</span> <span class="n">key</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to configure client certificate"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Created TLS client configuration"</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(</span><span class="nn">Arc</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">config</span><span class="p">))</span>
    <span class="p">}</span>

    <span class="cd">/// Check certificate expiration</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">check_certificate_expiration</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">cert_path</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Duration</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">use</span> <span class="nn">x509_parser</span><span class="p">::</span><span class="nn">prelude</span><span class="p">::</span><span class="o">*</span><span class="p">;</span>
        
        <span class="k">let</span> <span class="n">cert_data</span> <span class="o">=</span> <span class="nn">std</span><span class="p">::</span><span class="nn">fs</span><span class="p">::</span><span class="nf">read</span><span class="p">(</span><span class="n">cert_path</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"Failed to read certificate: {}"</span><span class="p">,</span> <span class="n">cert_path</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>
            
        <span class="k">let</span> <span class="n">pem</span> <span class="o">=</span> <span class="nn">pem</span><span class="p">::</span><span class="nf">parse</span><span class="p">(</span><span class="o">&amp;</span><span class="n">cert_data</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to parse PEM certificate"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
            
        <span class="k">let</span> <span class="n">x509</span> <span class="o">=</span> <span class="nn">X509Certificate</span><span class="p">::</span><span class="nf">from_der</span><span class="p">(</span><span class="o">&amp;</span><span class="n">pem</span><span class="py">.contents</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to parse X509 certificate"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="k">let</span> <span class="n">expiry_time</span> <span class="o">=</span> <span class="n">x509</span><span class="na">.1</span><span class="nf">.validity</span><span class="p">()</span><span class="py">.not_after</span><span class="nf">.timestamp</span><span class="p">()</span> <span class="k">as</span> <span class="nb">u64</span><span class="p">;</span>
        <span class="k">let</span> <span class="n">current_time</span> <span class="o">=</span> <span class="nn">std</span><span class="p">::</span><span class="nn">time</span><span class="p">::</span><span class="nn">SystemTime</span><span class="p">::</span><span class="nf">now</span><span class="p">()</span>
            <span class="nf">.duration_since</span><span class="p">(</span><span class="nn">std</span><span class="p">::</span><span class="nn">time</span><span class="p">::</span><span class="n">UNIX_EPOCH</span><span class="p">)</span>
            <span class="nf">.unwrap</span><span class="p">()</span>
            <span class="nf">.as_secs</span><span class="p">();</span>

        <span class="k">if</span> <span class="n">expiry_time</span> <span class="o">&lt;=</span> <span class="n">current_time</span> <span class="p">{</span>
            <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span><span class="s">"Certificate has expired: {}"</span><span class="p">,</span> <span class="n">cert_path</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="k">let</span> <span class="n">remaining</span> <span class="o">=</span> <span class="nn">Duration</span><span class="p">::</span><span class="nf">from_secs</span><span class="p">(</span><span class="n">expiry_time</span> <span class="o">-</span> <span class="n">current_time</span><span class="p">);</span>
        
        <span class="k">if</span> <span class="n">remaining</span><span class="nf">.as_secs</span><span class="p">()</span> <span class="o">&lt;</span> <span class="mi">30</span> <span class="o">*</span> <span class="mi">24</span> <span class="o">*</span> <span class="mi">60</span> <span class="o">*</span> <span class="mi">60</span> <span class="p">{</span> <span class="c1">// 30 days</span>
            <span class="nd">warn!</span><span class="p">(</span><span class="s">"Certificate expires in {} days: {}"</span><span class="p">,</span> 
                <span class="n">remaining</span><span class="nf">.as_secs</span><span class="p">()</span> <span class="o">/</span> <span class="p">(</span><span class="mi">24</span> <span class="o">*</span> <span class="mi">60</span> <span class="o">*</span> <span class="mi">60</span><span class="p">),</span> <span class="n">cert_path</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="nf">Ok</span><span class="p">(</span><span class="n">remaining</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Start certificate monitoring task</span>
    <span class="k">pub</span> <span class="k">async</span> <span class="k">fn</span> <span class="nf">start_monitoring</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">)</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">manager</span> <span class="o">=</span> <span class="k">self</span><span class="nf">.clone</span><span class="p">();</span>
        <span class="nn">tokio</span><span class="p">::</span><span class="nf">spawn</span><span class="p">(</span><span class="k">async</span> <span class="k">move</span> <span class="p">{</span>
            <span class="k">let</span> <span class="k">mut</span> <span class="n">interval</span> <span class="o">=</span> <span class="nf">interval</span><span class="p">(</span><span class="nn">Duration</span><span class="p">::</span><span class="nf">from_hours</span><span class="p">(</span><span class="mi">24</span><span class="p">));</span>
            
            <span class="k">loop</span> <span class="p">{</span>
                <span class="n">interval</span><span class="nf">.tick</span><span class="p">()</span><span class="k">.await</span><span class="p">;</span>
                
                <span class="c1">// Check server certificate expiration</span>
                <span class="k">if</span> <span class="k">let</span> <span class="nf">Err</span><span class="p">(</span><span class="n">e</span><span class="p">)</span> <span class="o">=</span> <span class="n">manager</span><span class="nf">.check_certificate_expiration</span><span class="p">(</span><span class="o">&amp;</span><span class="n">manager</span><span class="py">.server_cert_path</span><span class="p">)</span> <span class="p">{</span>
                    <span class="nd">error!</span><span class="p">(</span><span class="s">"Server certificate check failed: {}"</span><span class="p">,</span> <span class="n">e</span><span class="p">);</span>
                <span class="p">}</span>
                
                <span class="c1">// Check client certificate expiration</span>
                <span class="k">if</span> <span class="k">let</span> <span class="nf">Err</span><span class="p">(</span><span class="n">e</span><span class="p">)</span> <span class="o">=</span> <span class="n">manager</span><span class="nf">.check_certificate_expiration</span><span class="p">(</span><span class="o">&amp;</span><span class="n">manager</span><span class="py">.client_cert_path</span><span class="p">)</span> <span class="p">{</span>
                    <span class="nd">error!</span><span class="p">(</span><span class="s">"Client certificate check failed: {}"</span><span class="p">,</span> <span class="n">e</span><span class="p">);</span>
                <span class="p">}</span>
            <span class="p">}</span>
        <span class="p">});</span>
        
        <span class="nd">info!</span><span class="p">(</span><span class="s">"Started certificate monitoring task"</span><span class="p">);</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="nd">#[cfg(test)]</span>
<span class="k">mod</span> <span class="n">tests</span> <span class="p">{</span>
    <span class="k">use</span> <span class="k">super</span><span class="p">::</span><span class="o">*</span><span class="p">;</span>
    <span class="k">use</span> <span class="nn">tempfile</span><span class="p">::</span><span class="n">TempDir</span><span class="p">;</span>

    <span class="nd">#[tokio::test]</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">test_certificate_manager</span><span class="p">()</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">temp_dir</span> <span class="o">=</span> <span class="nn">TempDir</span><span class="p">::</span><span class="nf">new</span><span class="p">()</span><span class="nf">.unwrap</span><span class="p">();</span>
        <span class="c1">// Add comprehensive tests for certificate operations</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h2 id="2-jwt-authentication-implementation">2. JWT Authentication Implementation</h2>

<h3 id="21-jwt-service-implementation">2.1 JWT Service Implementation</h3>

<p><strong>Complete JWT Authentication Service:</strong></p>
<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// jwt_service.rs</span>
<span class="k">use</span> <span class="nn">jwt_simple</span><span class="p">::</span><span class="nn">prelude</span><span class="p">::</span><span class="o">*</span><span class="p">;</span>
<span class="k">use</span> <span class="nn">serde</span><span class="p">::{</span><span class="n">Deserialize</span><span class="p">,</span> <span class="n">Serialize</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">collections</span><span class="p">::</span><span class="n">HashSet</span><span class="p">;</span>
<span class="k">use</span> <span class="nn">anyhow</span><span class="p">::{</span><span class="nb">Result</span><span class="p">,</span> <span class="n">Context</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">tracing</span><span class="p">::{</span><span class="n">info</span><span class="p">,</span> <span class="n">warn</span><span class="p">,</span> <span class="n">error</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">uuid</span><span class="p">::</span><span class="n">Uuid</span><span class="p">;</span>

<span class="cd">/// Standard security parameters - DO NOT MODIFY</span>
<span class="k">const</span> <span class="n">ACCESS_TOKEN_DURATION</span><span class="p">:</span> <span class="nb">u64</span> <span class="o">=</span> <span class="mi">15</span> <span class="o">*</span> <span class="mi">60</span><span class="p">;</span> <span class="c1">// 15 minutes</span>
<span class="k">const</span> <span class="n">REFRESH_TOKEN_DURATION</span><span class="p">:</span> <span class="nb">u64</span> <span class="o">=</span> <span class="mi">7</span> <span class="o">*</span> <span class="mi">24</span> <span class="o">*</span> <span class="mi">60</span> <span class="o">*</span> <span class="mi">60</span><span class="p">;</span> <span class="c1">// 7 days</span>
<span class="k">const</span> <span class="n">API_KEY_DURATION</span><span class="p">:</span> <span class="nb">u64</span> <span class="o">=</span> <span class="mi">90</span> <span class="o">*</span> <span class="mi">24</span> <span class="o">*</span> <span class="mi">60</span> <span class="o">*</span> <span class="mi">60</span><span class="p">;</span> <span class="c1">// 90 days</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">enum</span> <span class="n">Role</span> <span class="p">{</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"readonly"</span><span class="nd">)]</span>
    <span class="n">ReadOnly</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"user"</span><span class="nd">)]</span>
    <span class="n">User</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"moderator"</span><span class="nd">)]</span>
    <span class="n">Moderator</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"admin"</span><span class="nd">)]</span>
    <span class="n">Admin</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"system"</span><span class="nd">)]</span>
    <span class="n">System</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">Role</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">permissions</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Vec</span><span class="o">&lt;&amp;</span><span class="k">'static</span> <span class="nb">str</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">match</span> <span class="k">self</span> <span class="p">{</span>
            <span class="nn">Role</span><span class="p">::</span><span class="n">ReadOnly</span> <span class="k">=&gt;</span> <span class="nd">vec!</span><span class="p">[</span><span class="s">"read:own"</span><span class="p">],</span>
            <span class="nn">Role</span><span class="p">::</span><span class="n">User</span> <span class="k">=&gt;</span> <span class="nd">vec!</span><span class="p">[</span><span class="s">"read:own"</span><span class="p">,</span> <span class="s">"write:own"</span><span class="p">],</span>
            <span class="nn">Role</span><span class="p">::</span><span class="n">Moderator</span> <span class="k">=&gt;</span> <span class="nd">vec!</span><span class="p">[</span><span class="s">"read:own"</span><span class="p">,</span> <span class="s">"write:own"</span><span class="p">,</span> <span class="s">"read:team"</span><span class="p">,</span> <span class="s">"write:team"</span><span class="p">],</span>
            <span class="nn">Role</span><span class="p">::</span><span class="n">Admin</span> <span class="k">=&gt;</span> <span class="nd">vec!</span><span class="p">[</span><span class="s">"read:*"</span><span class="p">,</span> <span class="s">"write:*"</span><span class="p">,</span> <span class="s">"delete:*"</span><span class="p">],</span>
            <span class="nn">Role</span><span class="p">::</span><span class="n">System</span> <span class="k">=&gt;</span> <span class="nd">vec!</span><span class="p">[</span><span class="s">"read:*"</span><span class="p">,</span> <span class="s">"write:*"</span><span class="p">,</span> <span class="s">"delete:*"</span><span class="p">,</span> <span class="s">"admin:*"</span><span class="p">],</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">UserClaims</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="n">user_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">tenant_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">roles</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">Role</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">permissions</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">token_type</span><span class="p">:</span> <span class="n">TokenType</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">session_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
<span class="p">}</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">enum</span> <span class="n">TokenType</span> <span class="p">{</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"access"</span><span class="nd">)]</span>
    <span class="n">Access</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"refresh"</span><span class="nd">)]</span>
    <span class="n">Refresh</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"api_key"</span><span class="nd">)]</span>
    <span class="n">ApiKey</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">pub</span> <span class="k">struct</span> <span class="n">JwtService</span> <span class="p">{</span>
    <span class="n">access_key</span><span class="p">:</span> <span class="n">ES384KeyPair</span><span class="p">,</span>
    <span class="n">refresh_key</span><span class="p">:</span> <span class="n">ES384KeyPair</span><span class="p">,</span>
    <span class="n">api_key</span><span class="p">:</span> <span class="n">ES384KeyPair</span><span class="p">,</span>
    <span class="n">issuer</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="n">audience</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">JwtService</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">new</span><span class="p">()</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="k">Self</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="c1">// Generate separate key pairs for different token types</span>
        <span class="k">let</span> <span class="n">access_key</span> <span class="o">=</span> <span class="nn">ES384KeyPair</span><span class="p">::</span><span class="nf">generate</span><span class="p">();</span>
        <span class="k">let</span> <span class="n">refresh_key</span> <span class="o">=</span> <span class="nn">ES384KeyPair</span><span class="p">::</span><span class="nf">generate</span><span class="p">();</span>
        <span class="k">let</span> <span class="n">api_key</span> <span class="o">=</span> <span class="nn">ES384KeyPair</span><span class="p">::</span><span class="nf">generate</span><span class="p">();</span>

        <span class="nf">Ok</span><span class="p">(</span><span class="k">Self</span> <span class="p">{</span>
            <span class="n">access_key</span><span class="p">,</span>
            <span class="n">refresh_key</span><span class="p">,</span>
            <span class="n">api_key</span><span class="p">,</span>
            <span class="n">issuer</span><span class="p">:</span> <span class="s">"mister-smith-framework"</span><span class="nf">.to_string</span><span class="p">(),</span>
            <span class="n">audience</span><span class="p">:</span> <span class="s">"mister-smith-services"</span><span class="nf">.to_string</span><span class="p">(),</span>
        <span class="p">})</span>
    <span class="p">}</span>

    <span class="cd">/// Generate access token (15 minutes expiration)</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">generate_access_token</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">user_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span> <span class="n">tenant_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span> <span class="n">roles</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">Role</span><span class="o">&gt;</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">permissions</span> <span class="o">=</span> <span class="n">roles</span><span class="nf">.iter</span><span class="p">()</span>
            <span class="nf">.flat_map</span><span class="p">(|</span><span class="n">role</span><span class="p">|</span> <span class="n">role</span><span class="nf">.permissions</span><span class="p">())</span>
            <span class="nf">.map</span><span class="p">(|</span><span class="n">p</span><span class="p">|</span> <span class="n">p</span><span class="nf">.to_string</span><span class="p">())</span>
            <span class="nf">.collect</span><span class="p">();</span>

        <span class="k">let</span> <span class="n">user_claims</span> <span class="o">=</span> <span class="n">UserClaims</span> <span class="p">{</span>
            <span class="n">user_id</span><span class="p">,</span>
            <span class="n">tenant_id</span><span class="p">,</span>
            <span class="n">roles</span><span class="p">,</span>
            <span class="n">permissions</span><span class="p">,</span>
            <span class="n">token_type</span><span class="p">:</span> <span class="nn">TokenType</span><span class="p">::</span><span class="n">Access</span><span class="p">,</span>
            <span class="n">session_id</span><span class="p">:</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">(),</span>
        <span class="p">};</span>

        <span class="k">let</span> <span class="n">claims</span> <span class="o">=</span> <span class="nn">Claims</span><span class="p">::</span><span class="nf">with_custom_claims</span><span class="p">(</span><span class="n">user_claims</span><span class="p">,</span> <span class="nn">Duration</span><span class="p">::</span><span class="nf">from_secs</span><span class="p">(</span><span class="n">ACCESS_TOKEN_DURATION</span><span class="p">))</span>
            <span class="nf">.with_issuer</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.issuer</span><span class="p">)</span>
            <span class="nf">.with_audience</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.audience</span><span class="p">)</span>
            <span class="nf">.with_subject</span><span class="p">(</span><span class="o">&amp;</span><span class="n">user_id</span><span class="nf">.to_string</span><span class="p">());</span>

        <span class="k">let</span> <span class="n">token</span> <span class="o">=</span> <span class="k">self</span><span class="py">.access_key</span><span class="nf">.sign</span><span class="p">(</span><span class="n">claims</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to sign access token"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Generated access token for user: {}"</span><span class="p">,</span> <span class="n">user_id</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(</span><span class="n">token</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Generate refresh token (7 days expiration)</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">generate_refresh_token</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">user_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span> <span class="n">tenant_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span> <span class="n">session_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">user_claims</span> <span class="o">=</span> <span class="n">UserClaims</span> <span class="p">{</span>
            <span class="n">user_id</span><span class="p">,</span>
            <span class="n">tenant_id</span><span class="p">,</span>
            <span class="n">roles</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[],</span> <span class="c1">// Refresh tokens don't carry permissions</span>
            <span class="n">permissions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[],</span>
            <span class="n">token_type</span><span class="p">:</span> <span class="nn">TokenType</span><span class="p">::</span><span class="n">Refresh</span><span class="p">,</span>
            <span class="n">session_id</span><span class="p">,</span>
        <span class="p">};</span>

        <span class="k">let</span> <span class="n">claims</span> <span class="o">=</span> <span class="nn">Claims</span><span class="p">::</span><span class="nf">with_custom_claims</span><span class="p">(</span><span class="n">user_claims</span><span class="p">,</span> <span class="nn">Duration</span><span class="p">::</span><span class="nf">from_secs</span><span class="p">(</span><span class="n">REFRESH_TOKEN_DURATION</span><span class="p">))</span>
            <span class="nf">.with_issuer</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.issuer</span><span class="p">)</span>
            <span class="nf">.with_audience</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.audience</span><span class="p">)</span>
            <span class="nf">.with_subject</span><span class="p">(</span><span class="o">&amp;</span><span class="n">user_id</span><span class="nf">.to_string</span><span class="p">());</span>

        <span class="k">let</span> <span class="n">token</span> <span class="o">=</span> <span class="k">self</span><span class="py">.refresh_key</span><span class="nf">.sign</span><span class="p">(</span><span class="n">claims</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to sign refresh token"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Generated refresh token for user: {}"</span><span class="p">,</span> <span class="n">user_id</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(</span><span class="n">token</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Generate API key (90 days expiration)</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">generate_api_key</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">user_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span> <span class="n">tenant_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span> <span class="n">roles</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">Role</span><span class="o">&gt;</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">permissions</span> <span class="o">=</span> <span class="n">roles</span><span class="nf">.iter</span><span class="p">()</span>
            <span class="nf">.flat_map</span><span class="p">(|</span><span class="n">role</span><span class="p">|</span> <span class="n">role</span><span class="nf">.permissions</span><span class="p">())</span>
            <span class="nf">.map</span><span class="p">(|</span><span class="n">p</span><span class="p">|</span> <span class="n">p</span><span class="nf">.to_string</span><span class="p">())</span>
            <span class="nf">.collect</span><span class="p">();</span>

        <span class="k">let</span> <span class="n">user_claims</span> <span class="o">=</span> <span class="n">UserClaims</span> <span class="p">{</span>
            <span class="n">user_id</span><span class="p">,</span>
            <span class="n">tenant_id</span><span class="p">,</span>
            <span class="n">roles</span><span class="p">,</span>
            <span class="n">permissions</span><span class="p">,</span>
            <span class="n">token_type</span><span class="p">:</span> <span class="nn">TokenType</span><span class="p">::</span><span class="n">ApiKey</span><span class="p">,</span>
            <span class="n">session_id</span><span class="p">:</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">(),</span>
        <span class="p">};</span>

        <span class="k">let</span> <span class="n">claims</span> <span class="o">=</span> <span class="nn">Claims</span><span class="p">::</span><span class="nf">with_custom_claims</span><span class="p">(</span><span class="n">user_claims</span><span class="p">,</span> <span class="nn">Duration</span><span class="p">::</span><span class="nf">from_secs</span><span class="p">(</span><span class="n">API_KEY_DURATION</span><span class="p">))</span>
            <span class="nf">.with_issuer</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.issuer</span><span class="p">)</span>
            <span class="nf">.with_audience</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.audience</span><span class="p">)</span>
            <span class="nf">.with_subject</span><span class="p">(</span><span class="o">&amp;</span><span class="n">user_id</span><span class="nf">.to_string</span><span class="p">());</span>

        <span class="k">let</span> <span class="n">token</span> <span class="o">=</span> <span class="k">self</span><span class="py">.api_key</span><span class="nf">.sign</span><span class="p">(</span><span class="n">claims</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to sign API key"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Generated API key for user: {}"</span><span class="p">,</span> <span class="n">user_id</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(</span><span class="n">token</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Verify access token</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">verify_access_token</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">token</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Claims</span><span class="o">&lt;</span><span class="n">UserClaims</span><span class="o">&gt;&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">public_key</span> <span class="o">=</span> <span class="k">self</span><span class="py">.access_key</span><span class="nf">.public_key</span><span class="p">();</span>
        
        <span class="k">let</span> <span class="k">mut</span> <span class="n">options</span> <span class="o">=</span> <span class="nn">VerificationOptions</span><span class="p">::</span><span class="nf">default</span><span class="p">();</span>
        <span class="n">options</span><span class="py">.allowed_issuers</span> <span class="o">=</span> <span class="nf">Some</span><span class="p">(</span><span class="nn">HashSet</span><span class="p">::</span><span class="nf">from</span><span class="p">([</span><span class="k">self</span><span class="py">.issuer</span><span class="nf">.clone</span><span class="p">()]));</span>
        <span class="n">options</span><span class="py">.allowed_audiences</span> <span class="o">=</span> <span class="nf">Some</span><span class="p">(</span><span class="nn">HashSet</span><span class="p">::</span><span class="nf">from</span><span class="p">([</span><span class="k">self</span><span class="py">.audience</span><span class="nf">.clone</span><span class="p">()]));</span>

        <span class="k">let</span> <span class="n">claims</span> <span class="o">=</span> <span class="n">public_key</span><span class="py">.verify_token</span><span class="p">::</span><span class="o">&lt;</span><span class="n">UserClaims</span><span class="o">&gt;</span><span class="p">(</span><span class="n">token</span><span class="p">,</span> <span class="nf">Some</span><span class="p">(</span><span class="n">options</span><span class="p">))</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to verify access token"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Verify token type</span>
        <span class="k">if</span> <span class="o">!</span><span class="nd">matches!</span><span class="p">(</span><span class="n">claims</span><span class="py">.custom.token_type</span><span class="p">,</span> <span class="nn">TokenType</span><span class="p">::</span><span class="n">Access</span><span class="p">)</span> <span class="p">{</span>
            <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span><span class="s">"Invalid token type for access token"</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="nf">Ok</span><span class="p">(</span><span class="n">claims</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Verify refresh token</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">verify_refresh_token</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">token</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Claims</span><span class="o">&lt;</span><span class="n">UserClaims</span><span class="o">&gt;&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">public_key</span> <span class="o">=</span> <span class="k">self</span><span class="py">.refresh_key</span><span class="nf">.public_key</span><span class="p">();</span>
        
        <span class="k">let</span> <span class="k">mut</span> <span class="n">options</span> <span class="o">=</span> <span class="nn">VerificationOptions</span><span class="p">::</span><span class="nf">default</span><span class="p">();</span>
        <span class="n">options</span><span class="py">.allowed_issuers</span> <span class="o">=</span> <span class="nf">Some</span><span class="p">(</span><span class="nn">HashSet</span><span class="p">::</span><span class="nf">from</span><span class="p">([</span><span class="k">self</span><span class="py">.issuer</span><span class="nf">.clone</span><span class="p">()]));</span>
        <span class="n">options</span><span class="py">.allowed_audiences</span> <span class="o">=</span> <span class="nf">Some</span><span class="p">(</span><span class="nn">HashSet</span><span class="p">::</span><span class="nf">from</span><span class="p">([</span><span class="k">self</span><span class="py">.audience</span><span class="nf">.clone</span><span class="p">()]));</span>

        <span class="k">let</span> <span class="n">claims</span> <span class="o">=</span> <span class="n">public_key</span><span class="py">.verify_token</span><span class="p">::</span><span class="o">&lt;</span><span class="n">UserClaims</span><span class="o">&gt;</span><span class="p">(</span><span class="n">token</span><span class="p">,</span> <span class="nf">Some</span><span class="p">(</span><span class="n">options</span><span class="p">))</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to verify refresh token"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Verify token type</span>
        <span class="k">if</span> <span class="o">!</span><span class="nd">matches!</span><span class="p">(</span><span class="n">claims</span><span class="py">.custom.token_type</span><span class="p">,</span> <span class="nn">TokenType</span><span class="p">::</span><span class="n">Refresh</span><span class="p">)</span> <span class="p">{</span>
            <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span><span class="s">"Invalid token type for refresh token"</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="nf">Ok</span><span class="p">(</span><span class="n">claims</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Verify API key</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">verify_api_key</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">token</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Claims</span><span class="o">&lt;</span><span class="n">UserClaims</span><span class="o">&gt;&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">public_key</span> <span class="o">=</span> <span class="k">self</span><span class="py">.api_key</span><span class="nf">.public_key</span><span class="p">();</span>
        
        <span class="k">let</span> <span class="k">mut</span> <span class="n">options</span> <span class="o">=</span> <span class="nn">VerificationOptions</span><span class="p">::</span><span class="nf">default</span><span class="p">();</span>
        <span class="n">options</span><span class="py">.allowed_issuers</span> <span class="o">=</span> <span class="nf">Some</span><span class="p">(</span><span class="nn">HashSet</span><span class="p">::</span><span class="nf">from</span><span class="p">([</span><span class="k">self</span><span class="py">.issuer</span><span class="nf">.clone</span><span class="p">()]));</span>
        <span class="n">options</span><span class="py">.allowed_audiences</span> <span class="o">=</span> <span class="nf">Some</span><span class="p">(</span><span class="nn">HashSet</span><span class="p">::</span><span class="nf">from</span><span class="p">([</span><span class="k">self</span><span class="py">.audience</span><span class="nf">.clone</span><span class="p">()]));</span>
        <span class="c1">// API keys have longer validity, so allow more clock skew</span>
        <span class="n">options</span><span class="py">.time_tolerance</span> <span class="o">=</span> <span class="nf">Some</span><span class="p">(</span><span class="nn">Duration</span><span class="p">::</span><span class="nf">from_mins</span><span class="p">(</span><span class="mi">30</span><span class="p">));</span>

        <span class="k">let</span> <span class="n">claims</span> <span class="o">=</span> <span class="n">public_key</span><span class="py">.verify_token</span><span class="p">::</span><span class="o">&lt;</span><span class="n">UserClaims</span><span class="o">&gt;</span><span class="p">(</span><span class="n">token</span><span class="p">,</span> <span class="nf">Some</span><span class="p">(</span><span class="n">options</span><span class="p">))</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to verify API key"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Verify token type</span>
        <span class="k">if</span> <span class="o">!</span><span class="nd">matches!</span><span class="p">(</span><span class="n">claims</span><span class="py">.custom.token_type</span><span class="p">,</span> <span class="nn">TokenType</span><span class="p">::</span><span class="n">ApiKey</span><span class="p">)</span> <span class="p">{</span>
            <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span><span class="s">"Invalid token type for API key"</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="nf">Ok</span><span class="p">(</span><span class="n">claims</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Check if user has permission for resource and action</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">check_permission</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">claims</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Claims</span><span class="o">&lt;</span><span class="n">UserClaims</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">resource</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">,</span> <span class="n">action</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">bool</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">required_permission</span> <span class="o">=</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"{}:{}"</span><span class="p">,</span> <span class="n">action</span><span class="p">,</span> <span class="n">resource</span><span class="p">);</span>
        <span class="k">let</span> <span class="n">wildcard_permission</span> <span class="o">=</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"{}:*"</span><span class="p">,</span> <span class="n">action</span><span class="p">);</span>
        <span class="k">let</span> <span class="n">super_wildcard</span> <span class="o">=</span> <span class="s">"admin:*"</span><span class="p">;</span>

        <span class="n">claims</span><span class="py">.custom.permissions</span><span class="nf">.iter</span><span class="p">()</span><span class="nf">.any</span><span class="p">(|</span><span class="n">perm</span><span class="p">|</span> <span class="p">{</span>
            <span class="n">perm</span> <span class="o">==</span> <span class="o">&amp;</span><span class="n">required_permission</span> <span class="p">||</span> 
            <span class="n">perm</span> <span class="o">==</span> <span class="o">&amp;</span><span class="n">wildcard_permission</span> <span class="p">||</span> 
            <span class="n">perm</span> <span class="o">==</span> <span class="n">super_wildcard</span>
        <span class="p">})</span>
    <span class="p">}</span>

    <span class="cd">/// Refresh access token using refresh token</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">refresh_access_token</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">refresh_token</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">,</span> <span class="n">new_roles</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">Vec</span><span class="o">&lt;</span><span class="n">Role</span><span class="o">&gt;&gt;</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">refresh_claims</span> <span class="o">=</span> <span class="k">self</span><span class="nf">.verify_refresh_token</span><span class="p">(</span><span class="n">refresh_token</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
        
        <span class="c1">// Use provided roles or fetch from user store</span>
        <span class="k">let</span> <span class="n">roles</span> <span class="o">=</span> <span class="n">new_roles</span><span class="nf">.unwrap_or_else</span><span class="p">(||</span> <span class="nd">vec!</span><span class="p">[</span><span class="nn">Role</span><span class="p">::</span><span class="n">User</span><span class="p">]);</span>
        
        <span class="k">self</span><span class="nf">.generate_access_token</span><span class="p">(</span>
            <span class="n">refresh_claims</span><span class="py">.custom.user_id</span><span class="p">,</span>
            <span class="n">refresh_claims</span><span class="py">.custom.tenant_id</span><span class="p">,</span>
            <span class="n">roles</span>
        <span class="p">)</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="cd">/// JWT Authentication Middleware</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">JwtMiddleware</span> <span class="p">{</span>
    <span class="n">jwt_service</span><span class="p">:</span> <span class="n">JwtService</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">JwtMiddleware</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">new</span><span class="p">(</span><span class="n">jwt_service</span><span class="p">:</span> <span class="n">JwtService</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="k">Self</span> <span class="p">{</span>
        <span class="k">Self</span> <span class="p">{</span> <span class="n">jwt_service</span> <span class="p">}</span>
    <span class="p">}</span>

    <span class="cd">/// Extract and verify JWT from Authorization header</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">authenticate_request</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">auth_header</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;&amp;</span><span class="nb">str</span><span class="o">&gt;</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Claims</span><span class="o">&lt;</span><span class="n">UserClaims</span><span class="o">&gt;&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">auth_header</span> <span class="o">=</span> <span class="n">auth_header</span>
            <span class="nf">.ok_or_else</span><span class="p">(||</span> <span class="nn">anyhow</span><span class="p">::</span><span class="nd">anyhow!</span><span class="p">(</span><span class="s">"Missing Authorization header"</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>

        <span class="k">let</span> <span class="n">token</span> <span class="o">=</span> <span class="n">auth_header</span><span class="nf">.strip_prefix</span><span class="p">(</span><span class="s">"Bearer "</span><span class="p">)</span>
            <span class="nf">.ok_or_else</span><span class="p">(||</span> <span class="nn">anyhow</span><span class="p">::</span><span class="nd">anyhow!</span><span class="p">(</span><span class="s">"Invalid Authorization header format"</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Try access token first</span>
        <span class="k">if</span> <span class="k">let</span> <span class="nf">Ok</span><span class="p">(</span><span class="n">claims</span><span class="p">)</span> <span class="o">=</span> <span class="k">self</span><span class="py">.jwt_service</span><span class="nf">.verify_access_token</span><span class="p">(</span><span class="n">token</span><span class="p">)</span> <span class="p">{</span>
            <span class="k">return</span> <span class="nf">Ok</span><span class="p">(</span><span class="n">claims</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="c1">// Try API key if access token fails</span>
        <span class="k">self</span><span class="py">.jwt_service</span><span class="nf">.verify_api_key</span><span class="p">(</span><span class="n">token</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Invalid or expired token"</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Check authorization for specific resource and action</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">authorize_request</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">claims</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Claims</span><span class="o">&lt;</span><span class="n">UserClaims</span><span class="o">&gt;</span><span class="p">,</span> <span class="n">resource</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">,</span> <span class="n">action</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">if</span> <span class="o">!</span><span class="k">self</span><span class="py">.jwt_service</span><span class="nf">.check_permission</span><span class="p">(</span><span class="n">claims</span><span class="p">,</span> <span class="n">resource</span><span class="p">,</span> <span class="n">action</span><span class="p">)</span> <span class="p">{</span>
            <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span><span class="s">"Insufficient permissions for {} on {}"</span><span class="p">,</span> <span class="n">action</span><span class="p">,</span> <span class="n">resource</span><span class="p">);</span>
        <span class="p">}</span>
        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="nd">#[cfg(test)]</span>
<span class="k">mod</span> <span class="n">tests</span> <span class="p">{</span>
    <span class="k">use</span> <span class="k">super</span><span class="p">::</span><span class="o">*</span><span class="p">;</span>

    <span class="nd">#[tokio::test]</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">test_jwt_lifecycle</span><span class="p">()</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">jwt_service</span> <span class="o">=</span> <span class="nn">JwtService</span><span class="p">::</span><span class="nf">new</span><span class="p">()</span><span class="nf">.unwrap</span><span class="p">();</span>
        <span class="k">let</span> <span class="n">user_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>
        <span class="k">let</span> <span class="n">tenant_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>
        <span class="k">let</span> <span class="n">roles</span> <span class="o">=</span> <span class="nd">vec!</span><span class="p">[</span><span class="nn">Role</span><span class="p">::</span><span class="n">User</span><span class="p">];</span>

        <span class="c1">// Test access token generation and verification</span>
        <span class="k">let</span> <span class="n">access_token</span> <span class="o">=</span> <span class="n">jwt_service</span><span class="nf">.generate_access_token</span><span class="p">(</span><span class="n">user_id</span><span class="p">,</span> <span class="n">tenant_id</span><span class="p">,</span> <span class="n">roles</span><span class="nf">.clone</span><span class="p">())</span><span class="nf">.unwrap</span><span class="p">();</span>
        <span class="k">let</span> <span class="n">claims</span> <span class="o">=</span> <span class="n">jwt_service</span><span class="nf">.verify_access_token</span><span class="p">(</span><span class="o">&amp;</span><span class="n">access_token</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">();</span>
        <span class="nd">assert_eq!</span><span class="p">(</span><span class="n">claims</span><span class="py">.custom.user_id</span><span class="p">,</span> <span class="n">user_id</span><span class="p">);</span>

        <span class="c1">// Test permission checking</span>
        <span class="nd">assert!</span><span class="p">(</span><span class="n">jwt_service</span><span class="nf">.check_permission</span><span class="p">(</span><span class="o">&amp;</span><span class="n">claims</span><span class="p">,</span> <span class="s">"own"</span><span class="p">,</span> <span class="s">"read"</span><span class="p">));</span>
        <span class="nd">assert!</span><span class="p">(</span><span class="o">!</span><span class="n">jwt_service</span><span class="nf">.check_permission</span><span class="p">(</span><span class="o">&amp;</span><span class="n">claims</span><span class="p">,</span> <span class="s">"all"</span><span class="p">,</span> <span class="s">"delete"</span><span class="p">));</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h2 id="3-authorization-implementation">3. Authorization Implementation</h2>

<h3 id="31-rbac-policy-engine">3.1 RBAC Policy Engine</h3>

<p><strong>Complete RBAC Implementation:</strong></p>
<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// rbac_engine.rs</span>
<span class="k">use</span> <span class="nn">serde</span><span class="p">::{</span><span class="n">Deserialize</span><span class="p">,</span> <span class="n">Serialize</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">collections</span><span class="p">::{</span><span class="n">HashMap</span><span class="p">,</span> <span class="n">HashSet</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">uuid</span><span class="p">::</span><span class="n">Uuid</span><span class="p">;</span>
<span class="k">use</span> <span class="nn">anyhow</span><span class="p">::{</span><span class="nb">Result</span><span class="p">,</span> <span class="n">Context</span><span class="p">};</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">Resource</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="n">id</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">resource_type</span><span class="p">:</span> <span class="n">ResourceType</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">owner_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">tenant_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">team_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">enum</span> <span class="n">ResourceType</span> <span class="p">{</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"user"</span><span class="nd">)]</span>
    <span class="n">User</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"project"</span><span class="nd">)]</span>
    <span class="n">Project</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"document"</span><span class="nd">)]</span>
    <span class="n">Document</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"system"</span><span class="nd">)]</span>
    <span class="n">System</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"configuration"</span><span class="nd">)]</span>
    <span class="n">Configuration</span><span class="p">,</span>
<span class="p">}</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">enum</span> <span class="n">Action</span> <span class="p">{</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"read"</span><span class="nd">)]</span>
    <span class="n">Read</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"write"</span><span class="nd">)]</span>
    <span class="n">Write</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"delete"</span><span class="nd">)]</span>
    <span class="n">Delete</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"admin"</span><span class="nd">)]</span>
    <span class="n">Admin</span><span class="p">,</span>
<span class="p">}</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">Permission</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="n">action</span><span class="p">:</span> <span class="n">Action</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">resource_pattern</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">conditions</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">Condition</span><span class="o">&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">enum</span> <span class="n">Condition</span> <span class="p">{</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"owner_only"</span><span class="nd">)]</span>
    <span class="n">OwnerOnly</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"same_tenant"</span><span class="nd">)]</span>
    <span class="n">SameTenant</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"same_team"</span><span class="nd">)]</span>
    <span class="n">SameTeam</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"business_hours"</span><span class="nd">)]</span>
    <span class="n">BusinessHours</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">pub</span> <span class="k">struct</span> <span class="n">RbacEngine</span> <span class="p">{</span>
    <span class="n">role_permissions</span><span class="p">:</span> <span class="n">HashMap</span><span class="o">&lt;</span><span class="n">Role</span><span class="p">,</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">Permission</span><span class="o">&gt;&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">RbacEngine</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">new</span><span class="p">()</span> <span class="k">-&gt;</span> <span class="k">Self</span> <span class="p">{</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">role_permissions</span> <span class="o">=</span> <span class="nn">HashMap</span><span class="p">::</span><span class="nf">new</span><span class="p">();</span>
        
        <span class="c1">// ReadOnly role permissions</span>
        <span class="n">role_permissions</span><span class="nf">.insert</span><span class="p">(</span><span class="nn">Role</span><span class="p">::</span><span class="n">ReadOnly</span><span class="p">,</span> <span class="nd">vec!</span><span class="p">[</span>
            <span class="n">Permission</span> <span class="p">{</span>
                <span class="n">action</span><span class="p">:</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Read</span><span class="p">,</span>
                <span class="n">resource_pattern</span><span class="p">:</span> <span class="s">"*"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="n">conditions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span><span class="nn">Condition</span><span class="p">::</span><span class="n">OwnerOnly</span><span class="p">,</span> <span class="nn">Condition</span><span class="p">::</span><span class="n">SameTenant</span><span class="p">],</span>
            <span class="p">},</span>
        <span class="p">]);</span>

        <span class="c1">// User role permissions</span>
        <span class="n">role_permissions</span><span class="nf">.insert</span><span class="p">(</span><span class="nn">Role</span><span class="p">::</span><span class="n">User</span><span class="p">,</span> <span class="nd">vec!</span><span class="p">[</span>
            <span class="n">Permission</span> <span class="p">{</span>
                <span class="n">action</span><span class="p">:</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Read</span><span class="p">,</span>
                <span class="n">resource_pattern</span><span class="p">:</span> <span class="s">"*"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="n">conditions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span><span class="nn">Condition</span><span class="p">::</span><span class="n">OwnerOnly</span><span class="p">,</span> <span class="nn">Condition</span><span class="p">::</span><span class="n">SameTenant</span><span class="p">],</span>
            <span class="p">},</span>
            <span class="n">Permission</span> <span class="p">{</span>
                <span class="n">action</span><span class="p">:</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Write</span><span class="p">,</span>
                <span class="n">resource_pattern</span><span class="p">:</span> <span class="s">"*"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="n">conditions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span><span class="nn">Condition</span><span class="p">::</span><span class="n">OwnerOnly</span><span class="p">,</span> <span class="nn">Condition</span><span class="p">::</span><span class="n">SameTenant</span><span class="p">],</span>
            <span class="p">},</span>
        <span class="p">]);</span>

        <span class="c1">// Moderator role permissions</span>
        <span class="n">role_permissions</span><span class="nf">.insert</span><span class="p">(</span><span class="nn">Role</span><span class="p">::</span><span class="n">Moderator</span><span class="p">,</span> <span class="nd">vec!</span><span class="p">[</span>
            <span class="n">Permission</span> <span class="p">{</span>
                <span class="n">action</span><span class="p">:</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Read</span><span class="p">,</span>
                <span class="n">resource_pattern</span><span class="p">:</span> <span class="s">"*"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="n">conditions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span><span class="nn">Condition</span><span class="p">::</span><span class="n">SameTeam</span><span class="p">,</span> <span class="nn">Condition</span><span class="p">::</span><span class="n">SameTenant</span><span class="p">],</span>
            <span class="p">},</span>
            <span class="n">Permission</span> <span class="p">{</span>
                <span class="n">action</span><span class="p">:</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Write</span><span class="p">,</span>
                <span class="n">resource_pattern</span><span class="p">:</span> <span class="s">"*"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="n">conditions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span><span class="nn">Condition</span><span class="p">::</span><span class="n">SameTeam</span><span class="p">,</span> <span class="nn">Condition</span><span class="p">::</span><span class="n">SameTenant</span><span class="p">],</span>
            <span class="p">},</span>
            <span class="n">Permission</span> <span class="p">{</span>
                <span class="n">action</span><span class="p">:</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Delete</span><span class="p">,</span>
                <span class="n">resource_pattern</span><span class="p">:</span> <span class="s">"document"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="n">conditions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span><span class="nn">Condition</span><span class="p">::</span><span class="n">SameTeam</span><span class="p">,</span> <span class="nn">Condition</span><span class="p">::</span><span class="n">SameTenant</span><span class="p">],</span>
            <span class="p">},</span>
        <span class="p">]);</span>

        <span class="c1">// Admin role permissions</span>
        <span class="n">role_permissions</span><span class="nf">.insert</span><span class="p">(</span><span class="nn">Role</span><span class="p">::</span><span class="n">Admin</span><span class="p">,</span> <span class="nd">vec!</span><span class="p">[</span>
            <span class="n">Permission</span> <span class="p">{</span>
                <span class="n">action</span><span class="p">:</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Read</span><span class="p">,</span>
                <span class="n">resource_pattern</span><span class="p">:</span> <span class="s">"*"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="n">conditions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span><span class="nn">Condition</span><span class="p">::</span><span class="n">SameTenant</span><span class="p">],</span>
            <span class="p">},</span>
            <span class="n">Permission</span> <span class="p">{</span>
                <span class="n">action</span><span class="p">:</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Write</span><span class="p">,</span>
                <span class="n">resource_pattern</span><span class="p">:</span> <span class="s">"*"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="n">conditions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span><span class="nn">Condition</span><span class="p">::</span><span class="n">SameTenant</span><span class="p">],</span>
            <span class="p">},</span>
            <span class="n">Permission</span> <span class="p">{</span>
                <span class="n">action</span><span class="p">:</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Delete</span><span class="p">,</span>
                <span class="n">resource_pattern</span><span class="p">:</span> <span class="s">"*"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="n">conditions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span><span class="nn">Condition</span><span class="p">::</span><span class="n">SameTenant</span><span class="p">],</span>
            <span class="p">},</span>
        <span class="p">]);</span>

        <span class="c1">// System role permissions (no restrictions)</span>
        <span class="n">role_permissions</span><span class="nf">.insert</span><span class="p">(</span><span class="nn">Role</span><span class="p">::</span><span class="n">System</span><span class="p">,</span> <span class="nd">vec!</span><span class="p">[</span>
            <span class="n">Permission</span> <span class="p">{</span>
                <span class="n">action</span><span class="p">:</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Read</span><span class="p">,</span>
                <span class="n">resource_pattern</span><span class="p">:</span> <span class="s">"*"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="n">conditions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[],</span>
            <span class="p">},</span>
            <span class="n">Permission</span> <span class="p">{</span>
                <span class="n">action</span><span class="p">:</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Write</span><span class="p">,</span>
                <span class="n">resource_pattern</span><span class="p">:</span> <span class="s">"*"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="n">conditions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[],</span>
            <span class="p">},</span>
            <span class="n">Permission</span> <span class="p">{</span>
                <span class="n">action</span><span class="p">:</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Delete</span><span class="p">,</span>
                <span class="n">resource_pattern</span><span class="p">:</span> <span class="s">"*"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="n">conditions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[],</span>
            <span class="p">},</span>
            <span class="n">Permission</span> <span class="p">{</span>
                <span class="n">action</span><span class="p">:</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Admin</span><span class="p">,</span>
                <span class="n">resource_pattern</span><span class="p">:</span> <span class="s">"*"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="n">conditions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[],</span>
            <span class="p">},</span>
        <span class="p">]);</span>

        <span class="k">Self</span> <span class="p">{</span> <span class="n">role_permissions</span> <span class="p">}</span>
    <span class="p">}</span>

    <span class="cd">/// Check if user has permission to perform action on resource</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">check_permission</span><span class="p">(</span>
        <span class="o">&amp;</span><span class="k">self</span><span class="p">,</span>
        <span class="n">user_claims</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">UserClaims</span><span class="p">,</span>
        <span class="n">resource</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Resource</span><span class="p">,</span>
        <span class="n">action</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Action</span><span class="p">,</span>
    <span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nb">bool</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="c1">// Check each role the user has</span>
        <span class="k">for</span> <span class="n">role</span> <span class="k">in</span> <span class="o">&amp;</span><span class="n">user_claims</span><span class="py">.roles</span> <span class="p">{</span>
            <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">permissions</span><span class="p">)</span> <span class="o">=</span> <span class="k">self</span><span class="py">.role_permissions</span><span class="nf">.get</span><span class="p">(</span><span class="n">role</span><span class="p">)</span> <span class="p">{</span>
                <span class="k">for</span> <span class="n">permission</span> <span class="k">in</span> <span class="n">permissions</span> <span class="p">{</span>
                    <span class="k">if</span> <span class="k">self</span><span class="nf">.permission_matches</span><span class="p">(</span><span class="n">permission</span><span class="p">,</span> <span class="n">resource</span><span class="p">,</span> <span class="n">action</span><span class="p">,</span> <span class="n">user_claims</span><span class="p">)</span><span class="o">?</span> <span class="p">{</span>
                        <span class="k">return</span> <span class="nf">Ok</span><span class="p">(</span><span class="k">true</span><span class="p">);</span>
                    <span class="p">}</span>
                <span class="p">}</span>
            <span class="p">}</span>
        <span class="p">}</span>
        
        <span class="nf">Ok</span><span class="p">(</span><span class="k">false</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Check if permission matches the requested action and resource</span>
    <span class="k">fn</span> <span class="nf">permission_matches</span><span class="p">(</span>
        <span class="o">&amp;</span><span class="k">self</span><span class="p">,</span>
        <span class="n">permission</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Permission</span><span class="p">,</span>
        <span class="n">resource</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Resource</span><span class="p">,</span>
        <span class="n">action</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Action</span><span class="p">,</span>
        <span class="n">user_claims</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">UserClaims</span><span class="p">,</span>
    <span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nb">bool</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="c1">// Check action match</span>
        <span class="k">if</span> <span class="o">!</span><span class="k">self</span><span class="nf">.action_matches</span><span class="p">(</span><span class="o">&amp;</span><span class="n">permission</span><span class="py">.action</span><span class="p">,</span> <span class="n">action</span><span class="p">)</span> <span class="p">{</span>
            <span class="k">return</span> <span class="nf">Ok</span><span class="p">(</span><span class="k">false</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="c1">// Check resource pattern match</span>
        <span class="k">if</span> <span class="o">!</span><span class="k">self</span><span class="nf">.resource_pattern_matches</span><span class="p">(</span><span class="o">&amp;</span><span class="n">permission</span><span class="py">.resource_pattern</span><span class="p">,</span> <span class="n">resource</span><span class="p">)</span> <span class="p">{</span>
            <span class="k">return</span> <span class="nf">Ok</span><span class="p">(</span><span class="k">false</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="c1">// Check all conditions</span>
        <span class="k">for</span> <span class="n">condition</span> <span class="k">in</span> <span class="o">&amp;</span><span class="n">permission</span><span class="py">.conditions</span> <span class="p">{</span>
            <span class="k">if</span> <span class="o">!</span><span class="k">self</span><span class="nf">.condition_matches</span><span class="p">(</span><span class="n">condition</span><span class="p">,</span> <span class="n">resource</span><span class="p">,</span> <span class="n">user_claims</span><span class="p">)</span><span class="o">?</span> <span class="p">{</span>
                <span class="k">return</span> <span class="nf">Ok</span><span class="p">(</span><span class="k">false</span><span class="p">);</span>
            <span class="p">}</span>
        <span class="p">}</span>

        <span class="nf">Ok</span><span class="p">(</span><span class="k">true</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Check if action matches (including hierarchical permissions)</span>
    <span class="k">fn</span> <span class="nf">action_matches</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">permission_action</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Action</span><span class="p">,</span> <span class="n">requested_action</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Action</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">bool</span> <span class="p">{</span>
        <span class="k">match</span> <span class="p">(</span><span class="n">permission_action</span><span class="p">,</span> <span class="n">requested_action</span><span class="p">)</span> <span class="p">{</span>
            <span class="c1">// Exact match</span>
            <span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">)</span> <span class="k">if</span> <span class="n">a</span> <span class="o">==</span> <span class="n">b</span> <span class="k">=&gt;</span> <span class="k">true</span><span class="p">,</span>
            <span class="c1">// Admin action grants all permissions</span>
            <span class="p">(</span><span class="nn">Action</span><span class="p">::</span><span class="n">Admin</span><span class="p">,</span> <span class="n">_</span><span class="p">)</span> <span class="k">=&gt;</span> <span class="k">true</span><span class="p">,</span>
            <span class="c1">// Write action grants read permission</span>
            <span class="p">(</span><span class="nn">Action</span><span class="p">::</span><span class="n">Write</span><span class="p">,</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Read</span><span class="p">)</span> <span class="k">=&gt;</span> <span class="k">true</span><span class="p">,</span>
            <span class="c1">// Delete action grants read and write permissions</span>
            <span class="p">(</span><span class="nn">Action</span><span class="p">::</span><span class="n">Delete</span><span class="p">,</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Read</span><span class="p">)</span> <span class="p">|</span> <span class="p">(</span><span class="nn">Action</span><span class="p">::</span><span class="n">Delete</span><span class="p">,</span> <span class="nn">Action</span><span class="p">::</span><span class="n">Write</span><span class="p">)</span> <span class="k">=&gt;</span> <span class="k">true</span><span class="p">,</span>
            <span class="n">_</span> <span class="k">=&gt;</span> <span class="k">false</span><span class="p">,</span>
        <span class="p">}</span>
    <span class="p">}</span>

    <span class="cd">/// Check if resource pattern matches</span>
    <span class="k">fn</span> <span class="nf">resource_pattern_matches</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">pattern</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">,</span> <span class="n">resource</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Resource</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">bool</span> <span class="p">{</span>
        <span class="k">if</span> <span class="n">pattern</span> <span class="o">==</span> <span class="s">"*"</span> <span class="p">{</span>
            <span class="k">return</span> <span class="k">true</span><span class="p">;</span>
        <span class="p">}</span>

        <span class="k">let</span> <span class="n">resource_type_str</span> <span class="o">=</span> <span class="k">match</span> <span class="n">resource</span><span class="py">.resource_type</span> <span class="p">{</span>
            <span class="nn">ResourceType</span><span class="p">::</span><span class="n">User</span> <span class="k">=&gt;</span> <span class="s">"user"</span><span class="p">,</span>
            <span class="nn">ResourceType</span><span class="p">::</span><span class="n">Project</span> <span class="k">=&gt;</span> <span class="s">"project"</span><span class="p">,</span>
            <span class="nn">ResourceType</span><span class="p">::</span><span class="n">Document</span> <span class="k">=&gt;</span> <span class="s">"document"</span><span class="p">,</span>
            <span class="nn">ResourceType</span><span class="p">::</span><span class="n">System</span> <span class="k">=&gt;</span> <span class="s">"system"</span><span class="p">,</span>
            <span class="nn">ResourceType</span><span class="p">::</span><span class="n">Configuration</span> <span class="k">=&gt;</span> <span class="s">"configuration"</span><span class="p">,</span>
        <span class="p">};</span>

        <span class="n">pattern</span> <span class="o">==</span> <span class="n">resource_type_str</span>
    <span class="p">}</span>

    <span class="cd">/// Check if condition is satisfied</span>
    <span class="k">fn</span> <span class="nf">condition_matches</span><span class="p">(</span>
        <span class="o">&amp;</span><span class="k">self</span><span class="p">,</span>
        <span class="n">condition</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Condition</span><span class="p">,</span>
        <span class="n">resource</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Resource</span><span class="p">,</span>
        <span class="n">user_claims</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">UserClaims</span><span class="p">,</span>
    <span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nb">bool</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">match</span> <span class="n">condition</span> <span class="p">{</span>
            <span class="nn">Condition</span><span class="p">::</span><span class="n">OwnerOnly</span> <span class="k">=&gt;</span> <span class="p">{</span>
                <span class="nf">Ok</span><span class="p">(</span><span class="n">resource</span><span class="py">.owner_id</span> <span class="o">==</span> <span class="nf">Some</span><span class="p">(</span><span class="n">user_claims</span><span class="py">.user_id</span><span class="p">))</span>
            <span class="p">},</span>
            <span class="nn">Condition</span><span class="p">::</span><span class="n">SameTenant</span> <span class="k">=&gt;</span> <span class="p">{</span>
                <span class="nf">Ok</span><span class="p">(</span><span class="n">resource</span><span class="py">.tenant_id</span> <span class="o">==</span> <span class="n">user_claims</span><span class="py">.tenant_id</span><span class="p">)</span>
            <span class="p">},</span>
            <span class="nn">Condition</span><span class="p">::</span><span class="n">SameTeam</span> <span class="k">=&gt;</span> <span class="p">{</span>
                <span class="k">match</span> <span class="p">(</span><span class="n">resource</span><span class="py">.team_id</span><span class="p">,</span> <span class="o">&amp;</span><span class="n">user_claims</span><span class="py">.roles</span><span class="p">)</span> <span class="p">{</span>
                    <span class="p">(</span><span class="nf">Some</span><span class="p">(</span><span class="n">resource_team</span><span class="p">),</span> <span class="n">_</span><span class="p">)</span> <span class="k">=&gt;</span> <span class="p">{</span>
                        <span class="c1">// For now, check if user has moderator role in same tenant</span>
                        <span class="c1">// In a real implementation, you'd check team membership</span>
                        <span class="nf">Ok</span><span class="p">(</span><span class="n">resource</span><span class="py">.tenant_id</span> <span class="o">==</span> <span class="n">user_claims</span><span class="py">.tenant_id</span><span class="p">)</span>
                    <span class="p">},</span>
                    <span class="p">(</span><span class="nb">None</span><span class="p">,</span> <span class="n">_</span><span class="p">)</span> <span class="k">=&gt;</span> <span class="nf">Ok</span><span class="p">(</span><span class="k">true</span><span class="p">),</span> <span class="c1">// Resource not tied to team</span>
                <span class="p">}</span>
            <span class="p">},</span>
            <span class="nn">Condition</span><span class="p">::</span><span class="n">BusinessHours</span> <span class="k">=&gt;</span> <span class="p">{</span>
                <span class="k">use</span> <span class="nn">chrono</span><span class="p">::{</span><span class="n">Local</span><span class="p">,</span> <span class="n">Timelike</span><span class="p">};</span>
                <span class="k">let</span> <span class="n">now</span> <span class="o">=</span> <span class="nn">Local</span><span class="p">::</span><span class="nf">now</span><span class="p">();</span>
                <span class="k">let</span> <span class="n">hour</span> <span class="o">=</span> <span class="n">now</span><span class="nf">.hour</span><span class="p">();</span>
                <span class="nf">Ok</span><span class="p">(</span><span class="n">hour</span> <span class="o">&gt;=</span> <span class="mi">9</span> <span class="o">&amp;&amp;</span> <span class="n">hour</span> <span class="o">&lt;=</span> <span class="mi">17</span><span class="p">)</span> <span class="c1">// 9 AM to 5 PM</span>
            <span class="p">},</span>
        <span class="p">}</span>
    <span class="p">}</span>

    <span class="cd">/// Get effective permissions for user</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">get_effective_permissions</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">user_claims</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">UserClaims</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">permissions</span> <span class="o">=</span> <span class="nn">HashSet</span><span class="p">::</span><span class="nf">new</span><span class="p">();</span>
        
        <span class="k">for</span> <span class="n">role</span> <span class="k">in</span> <span class="o">&amp;</span><span class="n">user_claims</span><span class="py">.roles</span> <span class="p">{</span>
            <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">role_permissions</span><span class="p">)</span> <span class="o">=</span> <span class="k">self</span><span class="py">.role_permissions</span><span class="nf">.get</span><span class="p">(</span><span class="n">role</span><span class="p">)</span> <span class="p">{</span>
                <span class="k">for</span> <span class="n">permission</span> <span class="k">in</span> <span class="n">role_permissions</span> <span class="p">{</span>
                    <span class="k">let</span> <span class="n">perm_str</span> <span class="o">=</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"{}:{}"</span><span class="p">,</span> 
                        <span class="nf">action_to_string</span><span class="p">(</span><span class="o">&amp;</span><span class="n">permission</span><span class="py">.action</span><span class="p">),</span>
                        <span class="n">permission</span><span class="py">.resource_pattern</span>
                    <span class="p">);</span>
                    <span class="n">permissions</span><span class="nf">.insert</span><span class="p">(</span><span class="n">perm_str</span><span class="p">);</span>
                <span class="p">}</span>
            <span class="p">}</span>
        <span class="p">}</span>
        
        <span class="n">permissions</span><span class="nf">.into_iter</span><span class="p">()</span><span class="nf">.collect</span><span class="p">()</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="k">fn</span> <span class="nf">action_to_string</span><span class="p">(</span><span class="n">action</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Action</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="o">&amp;</span><span class="k">'static</span> <span class="nb">str</span> <span class="p">{</span>
    <span class="k">match</span> <span class="n">action</span> <span class="p">{</span>
        <span class="nn">Action</span><span class="p">::</span><span class="n">Read</span> <span class="k">=&gt;</span> <span class="s">"read"</span><span class="p">,</span>
        <span class="nn">Action</span><span class="p">::</span><span class="n">Write</span> <span class="k">=&gt;</span> <span class="s">"write"</span><span class="p">,</span> 
        <span class="nn">Action</span><span class="p">::</span><span class="n">Delete</span> <span class="k">=&gt;</span> <span class="s">"delete"</span><span class="p">,</span>
        <span class="nn">Action</span><span class="p">::</span><span class="n">Admin</span> <span class="k">=&gt;</span> <span class="s">"admin"</span><span class="p">,</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="cd">/// Authorization middleware</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">AuthorizationMiddleware</span> <span class="p">{</span>
    <span class="n">rbac_engine</span><span class="p">:</span> <span class="n">RbacEngine</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">AuthorizationMiddleware</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">new</span><span class="p">()</span> <span class="k">-&gt;</span> <span class="k">Self</span> <span class="p">{</span>
        <span class="k">Self</span> <span class="p">{</span>
            <span class="n">rbac_engine</span><span class="p">:</span> <span class="nn">RbacEngine</span><span class="p">::</span><span class="nf">new</span><span class="p">(),</span>
        <span class="p">}</span>
    <span class="p">}</span>

    <span class="cd">/// Authorize request for specific resource and action</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">authorize</span><span class="p">(</span>
        <span class="o">&amp;</span><span class="k">self</span><span class="p">,</span>
        <span class="n">user_claims</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">UserClaims</span><span class="p">,</span>
        <span class="n">resource</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Resource</span><span class="p">,</span>
        <span class="n">action</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Action</span><span class="p">,</span>
    <span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">if</span> <span class="k">self</span><span class="py">.rbac_engine</span><span class="nf">.check_permission</span><span class="p">(</span><span class="n">user_claims</span><span class="p">,</span> <span class="n">resource</span><span class="p">,</span> <span class="n">action</span><span class="p">)</span><span class="o">?</span> <span class="p">{</span>
            <span class="nf">Ok</span><span class="p">(())</span>
        <span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
            <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span>
                <span class="s">"Access denied: user {} lacks permission to {:?} resource {}"</span><span class="p">,</span>
                <span class="n">user_claims</span><span class="py">.user_id</span><span class="p">,</span>
                <span class="n">action</span><span class="p">,</span>
                <span class="n">resource</span><span class="py">.id</span>
            <span class="p">)</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="nd">#[cfg(test)]</span>
<span class="k">mod</span> <span class="n">tests</span> <span class="p">{</span>
    <span class="k">use</span> <span class="k">super</span><span class="p">::</span><span class="o">*</span><span class="p">;</span>

    <span class="nd">#[test]</span>
    <span class="k">fn</span> <span class="nf">test_rbac_permissions</span><span class="p">()</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">rbac</span> <span class="o">=</span> <span class="nn">RbacEngine</span><span class="p">::</span><span class="nf">new</span><span class="p">();</span>
        
        <span class="k">let</span> <span class="n">user_claims</span> <span class="o">=</span> <span class="n">UserClaims</span> <span class="p">{</span>
            <span class="n">user_id</span><span class="p">:</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">(),</span>
            <span class="n">tenant_id</span><span class="p">:</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">(),</span>
            <span class="n">roles</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span><span class="nn">Role</span><span class="p">::</span><span class="n">User</span><span class="p">],</span>
            <span class="n">permissions</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[],</span>
            <span class="n">token_type</span><span class="p">:</span> <span class="nn">TokenType</span><span class="p">::</span><span class="n">Access</span><span class="p">,</span>
            <span class="n">session_id</span><span class="p">:</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">(),</span>
        <span class="p">};</span>

        <span class="k">let</span> <span class="n">resource</span> <span class="o">=</span> <span class="n">Resource</span> <span class="p">{</span>
            <span class="n">id</span><span class="p">:</span> <span class="s">"test-doc"</span><span class="nf">.to_string</span><span class="p">(),</span>
            <span class="n">resource_type</span><span class="p">:</span> <span class="nn">ResourceType</span><span class="p">::</span><span class="n">Document</span><span class="p">,</span>
            <span class="n">owner_id</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="n">user_claims</span><span class="py">.user_id</span><span class="p">),</span>
            <span class="n">tenant_id</span><span class="p">:</span> <span class="n">user_claims</span><span class="py">.tenant_id</span><span class="p">,</span>
            <span class="n">team_id</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
        <span class="p">};</span>

        <span class="c1">// User should be able to read their own document</span>
        <span class="nd">assert!</span><span class="p">(</span><span class="n">rbac</span><span class="nf">.check_permission</span><span class="p">(</span><span class="o">&amp;</span><span class="n">user_claims</span><span class="p">,</span> <span class="o">&amp;</span><span class="n">resource</span><span class="p">,</span> <span class="o">&amp;</span><span class="nn">Action</span><span class="p">::</span><span class="n">Read</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">());</span>
        
        <span class="c1">// User should be able to write their own document</span>
        <span class="nd">assert!</span><span class="p">(</span><span class="n">rbac</span><span class="nf">.check_permission</span><span class="p">(</span><span class="o">&amp;</span><span class="n">user_claims</span><span class="p">,</span> <span class="o">&amp;</span><span class="n">resource</span><span class="p">,</span> <span class="o">&amp;</span><span class="nn">Action</span><span class="p">::</span><span class="n">Write</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">());</span>
        
        <span class="c1">// User should NOT be able to delete their own document (requires moderator+)</span>
        <span class="nd">assert!</span><span class="p">(</span><span class="o">!</span><span class="n">rbac</span><span class="nf">.check_permission</span><span class="p">(</span><span class="o">&amp;</span><span class="n">user_claims</span><span class="p">,</span> <span class="o">&amp;</span><span class="n">resource</span><span class="p">,</span> <span class="o">&amp;</span><span class="nn">Action</span><span class="p">::</span><span class="n">Delete</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">());</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h2 id="4-security-audit-implementation">4. Security Audit Implementation</h2>

<h3 id="41-structured-audit-logging">4.1 Structured Audit Logging</h3>

<p><strong>Complete Audit Service:</strong></p>
<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// audit_service.rs</span>
<span class="k">use</span> <span class="nn">serde</span><span class="p">::{</span><span class="n">Deserialize</span><span class="p">,</span> <span class="n">Serialize</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">uuid</span><span class="p">::</span><span class="n">Uuid</span><span class="p">;</span>
<span class="k">use</span> <span class="nn">chrono</span><span class="p">::{</span><span class="n">DateTime</span><span class="p">,</span> <span class="n">Utc</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">anyhow</span><span class="p">::{</span><span class="nb">Result</span><span class="p">,</span> <span class="n">Context</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">tracing</span><span class="p">::{</span><span class="n">info</span><span class="p">,</span> <span class="n">warn</span><span class="p">,</span> <span class="n">error</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">collections</span><span class="p">::</span><span class="n">HashMap</span><span class="p">;</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">enum</span> <span class="n">SecurityEventType</span> <span class="p">{</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"authentication_success"</span><span class="nd">)]</span>
    <span class="n">AuthenticationSuccess</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"authentication_failure"</span><span class="nd">)]</span>
    <span class="n">AuthenticationFailure</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"authorization_granted"</span><span class="nd">)]</span>
    <span class="n">AuthorizationGranted</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"authorization_denied"</span><span class="nd">)]</span>
    <span class="n">AuthorizationDenied</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"token_issued"</span><span class="nd">)]</span>
    <span class="n">TokenIssued</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"token_expired"</span><span class="nd">)]</span>
    <span class="n">TokenExpired</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"token_revoked"</span><span class="nd">)]</span>
    <span class="n">TokenRevoked</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"rate_limit_exceeded"</span><span class="nd">)]</span>
    <span class="n">RateLimitExceeded</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"suspicious_activity"</span><span class="nd">)]</span>
    <span class="n">SuspiciousActivity</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"certificate_rotation"</span><span class="nd">)]</span>
    <span class="n">CertificateRotation</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"configuration_change"</span><span class="nd">)]</span>
    <span class="n">ConfigurationChange</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"privilege_escalation"</span><span class="nd">)]</span>
    <span class="n">PrivilegeEscalation</span><span class="p">,</span>
<span class="p">}</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">enum</span> <span class="n">Severity</span> <span class="p">{</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"info"</span><span class="nd">)]</span>
    <span class="n">Info</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"warning"</span><span class="nd">)]</span>
    <span class="n">Warning</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"error"</span><span class="nd">)]</span>
    <span class="n">Error</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"critical"</span><span class="nd">)]</span>
    <span class="n">Critical</span><span class="p">,</span>
<span class="p">}</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">SecurityEvent</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="n">event_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">timestamp</span><span class="p">:</span> <span class="n">DateTime</span><span class="o">&lt;</span><span class="n">Utc</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">event_type</span><span class="p">:</span> <span class="n">SecurityEventType</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">severity</span><span class="p">:</span> <span class="n">Severity</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">user_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">tenant_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">session_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">source_ip</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">user_agent</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">resource_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">resource_type</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">action</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">outcome</span><span class="p">:</span> <span class="n">SecurityOutcome</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">details</span><span class="p">:</span> <span class="n">HashMap</span><span class="o">&lt;</span><span class="nb">String</span><span class="p">,</span> <span class="nn">serde_json</span><span class="p">::</span><span class="n">Value</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">correlation_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">enum</span> <span class="n">SecurityOutcome</span> <span class="p">{</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"success"</span><span class="nd">)]</span>
    <span class="n">Success</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"failure"</span><span class="nd">)]</span>
    <span class="n">Failure</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"blocked"</span><span class="nd">)]</span>
    <span class="n">Blocked</span><span class="p">,</span>
    <span class="nd">#[serde(rename</span> <span class="nd">=</span> <span class="s">"warning"</span><span class="nd">)]</span>
    <span class="n">Warning</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">pub</span> <span class="k">struct</span> <span class="n">AuditService</span> <span class="p">{</span>
    <span class="c1">// In production, this would write to a secure audit log storage</span>
    <span class="n">events</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">SecurityEvent</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="n">correlation_tracker</span><span class="p">:</span> <span class="n">HashMap</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="p">,</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">AuditService</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">new</span><span class="p">()</span> <span class="k">-&gt;</span> <span class="k">Self</span> <span class="p">{</span>
        <span class="k">Self</span> <span class="p">{</span>
            <span class="n">events</span><span class="p">:</span> <span class="nn">Vec</span><span class="p">::</span><span class="nf">new</span><span class="p">(),</span>
            <span class="n">correlation_tracker</span><span class="p">:</span> <span class="nn">HashMap</span><span class="p">::</span><span class="nf">new</span><span class="p">(),</span>
        <span class="p">}</span>
    <span class="p">}</span>

    <span class="cd">/// Log authentication attempt</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">log_authentication</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span> 
        <span class="n">user_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">tenant_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">source_ip</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">user_agent</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">success</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
        <span class="n">failure_reason</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="p">)</span> <span class="k">-&gt;</span> <span class="n">Uuid</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">event_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>
        <span class="k">let</span> <span class="n">correlation_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>
        
        <span class="k">let</span> <span class="k">mut</span> <span class="n">details</span> <span class="o">=</span> <span class="nn">HashMap</span><span class="p">::</span><span class="nf">new</span><span class="p">();</span>
        <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">reason</span><span class="p">)</span> <span class="o">=</span> <span class="n">failure_reason</span> <span class="p">{</span>
            <span class="n">details</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"failure_reason"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nn">Value</span><span class="p">::</span><span class="nf">String</span><span class="p">(</span><span class="n">reason</span><span class="p">));</span>
        <span class="p">}</span>

        <span class="k">let</span> <span class="n">event</span> <span class="o">=</span> <span class="n">SecurityEvent</span> <span class="p">{</span>
            <span class="n">event_id</span><span class="p">,</span>
            <span class="n">timestamp</span><span class="p">:</span> <span class="nn">Utc</span><span class="p">::</span><span class="nf">now</span><span class="p">(),</span>
            <span class="n">event_type</span><span class="p">:</span> <span class="k">if</span> <span class="n">success</span> <span class="p">{</span> 
                <span class="nn">SecurityEventType</span><span class="p">::</span><span class="n">AuthenticationSuccess</span> 
            <span class="p">}</span> <span class="k">else</span> <span class="p">{</span> 
                <span class="nn">SecurityEventType</span><span class="p">::</span><span class="n">AuthenticationFailure</span> 
            <span class="p">},</span>
            <span class="n">severity</span><span class="p">:</span> <span class="k">if</span> <span class="n">success</span> <span class="p">{</span> <span class="nn">Severity</span><span class="p">::</span><span class="n">Info</span> <span class="p">}</span> <span class="k">else</span> <span class="p">{</span> <span class="nn">Severity</span><span class="p">::</span><span class="n">Warning</span> <span class="p">},</span>
            <span class="n">user_id</span><span class="p">,</span>
            <span class="n">tenant_id</span><span class="p">,</span>
            <span class="n">session_id</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">source_ip</span><span class="p">,</span>
            <span class="n">user_agent</span><span class="p">,</span>
            <span class="n">resource_id</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">resource_type</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">action</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="s">"authenticate"</span><span class="nf">.to_string</span><span class="p">()),</span>
            <span class="n">outcome</span><span class="p">:</span> <span class="k">if</span> <span class="n">success</span> <span class="p">{</span> <span class="nn">SecurityOutcome</span><span class="p">::</span><span class="n">Success</span> <span class="p">}</span> <span class="k">else</span> <span class="p">{</span> <span class="nn">SecurityOutcome</span><span class="p">::</span><span class="n">Failure</span> <span class="p">},</span>
            <span class="n">details</span><span class="p">,</span>
            <span class="n">correlation_id</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="n">correlation_id</span><span class="p">),</span>
        <span class="p">};</span>

        <span class="k">self</span><span class="py">.events</span><span class="nf">.push</span><span class="p">(</span><span class="n">event</span><span class="p">);</span>
        <span class="nd">info!</span><span class="p">(</span><span class="s">"Logged authentication event: {}"</span><span class="p">,</span> <span class="n">event_id</span><span class="p">);</span>
        <span class="n">event_id</span>
    <span class="p">}</span>

    <span class="cd">/// Log authorization decision</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">log_authorization</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span>
        <span class="n">user_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
        <span class="n">tenant_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
        <span class="n">session_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">resource_id</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
        <span class="n">resource_type</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
        <span class="n">action</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
        <span class="n">granted</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
        <span class="n">roles</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">correlation_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="p">)</span> <span class="k">-&gt;</span> <span class="n">Uuid</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">event_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>
        
        <span class="k">let</span> <span class="k">mut</span> <span class="n">details</span> <span class="o">=</span> <span class="nn">HashMap</span><span class="p">::</span><span class="nf">new</span><span class="p">();</span>
        <span class="n">details</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"roles"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nn">Value</span><span class="p">::</span><span class="nf">Array</span><span class="p">(</span>
            <span class="n">roles</span><span class="nf">.into_iter</span><span class="p">()</span><span class="nf">.map</span><span class="p">(</span><span class="nn">serde_json</span><span class="p">::</span><span class="nn">Value</span><span class="p">::</span><span class="nb">String</span><span class="p">)</span><span class="nf">.collect</span><span class="p">()</span>
        <span class="p">));</span>

        <span class="k">let</span> <span class="n">event</span> <span class="o">=</span> <span class="n">SecurityEvent</span> <span class="p">{</span>
            <span class="n">event_id</span><span class="p">,</span>
            <span class="n">timestamp</span><span class="p">:</span> <span class="nn">Utc</span><span class="p">::</span><span class="nf">now</span><span class="p">(),</span>
            <span class="n">event_type</span><span class="p">:</span> <span class="k">if</span> <span class="n">granted</span> <span class="p">{</span> 
                <span class="nn">SecurityEventType</span><span class="p">::</span><span class="n">AuthorizationGranted</span> 
            <span class="p">}</span> <span class="k">else</span> <span class="p">{</span> 
                <span class="nn">SecurityEventType</span><span class="p">::</span><span class="n">AuthorizationDenied</span> 
            <span class="p">},</span>
            <span class="n">severity</span><span class="p">:</span> <span class="k">if</span> <span class="n">granted</span> <span class="p">{</span> <span class="nn">Severity</span><span class="p">::</span><span class="n">Info</span> <span class="p">}</span> <span class="k">else</span> <span class="p">{</span> <span class="nn">Severity</span><span class="p">::</span><span class="n">Warning</span> <span class="p">},</span>
            <span class="n">user_id</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="n">user_id</span><span class="p">),</span>
            <span class="n">tenant_id</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="n">tenant_id</span><span class="p">),</span>
            <span class="n">session_id</span><span class="p">,</span>
            <span class="n">source_ip</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">user_agent</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">resource_id</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="n">resource_id</span><span class="p">),</span>
            <span class="n">resource_type</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="n">resource_type</span><span class="p">),</span>
            <span class="n">action</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="n">action</span><span class="p">),</span>
            <span class="n">outcome</span><span class="p">:</span> <span class="k">if</span> <span class="n">granted</span> <span class="p">{</span> <span class="nn">SecurityOutcome</span><span class="p">::</span><span class="n">Success</span> <span class="p">}</span> <span class="k">else</span> <span class="p">{</span> <span class="nn">SecurityOutcome</span><span class="p">::</span><span class="n">Blocked</span> <span class="p">},</span>
            <span class="n">details</span><span class="p">,</span>
            <span class="n">correlation_id</span><span class="p">,</span>
        <span class="p">};</span>

        <span class="k">self</span><span class="py">.events</span><span class="nf">.push</span><span class="p">(</span><span class="n">event</span><span class="p">);</span>
        <span class="nd">info!</span><span class="p">(</span><span class="s">"Logged authorization event: {}"</span><span class="p">,</span> <span class="n">event_id</span><span class="p">);</span>
        <span class="n">event_id</span>
    <span class="p">}</span>

    <span class="cd">/// Log token issuance</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">log_token_issued</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span>
        <span class="n">user_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
        <span class="n">tenant_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
        <span class="n">token_type</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
        <span class="n">expires_at</span><span class="p">:</span> <span class="n">DateTime</span><span class="o">&lt;</span><span class="n">Utc</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">correlation_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="p">)</span> <span class="k">-&gt;</span> <span class="n">Uuid</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">event_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>
        
        <span class="k">let</span> <span class="k">mut</span> <span class="n">details</span> <span class="o">=</span> <span class="nn">HashMap</span><span class="p">::</span><span class="nf">new</span><span class="p">();</span>
        <span class="n">details</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"token_type"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nn">Value</span><span class="p">::</span><span class="nf">String</span><span class="p">(</span><span class="n">token_type</span><span class="p">));</span>
        <span class="n">details</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"expires_at"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nn">Value</span><span class="p">::</span><span class="nf">String</span><span class="p">(</span><span class="n">expires_at</span><span class="nf">.to_rfc3339</span><span class="p">()));</span>

        <span class="k">let</span> <span class="n">event</span> <span class="o">=</span> <span class="n">SecurityEvent</span> <span class="p">{</span>
            <span class="n">event_id</span><span class="p">,</span>
            <span class="n">timestamp</span><span class="p">:</span> <span class="nn">Utc</span><span class="p">::</span><span class="nf">now</span><span class="p">(),</span>
            <span class="n">event_type</span><span class="p">:</span> <span class="nn">SecurityEventType</span><span class="p">::</span><span class="n">TokenIssued</span><span class="p">,</span>
            <span class="n">severity</span><span class="p">:</span> <span class="nn">Severity</span><span class="p">::</span><span class="n">Info</span><span class="p">,</span>
            <span class="n">user_id</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="n">user_id</span><span class="p">),</span>
            <span class="n">tenant_id</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="n">tenant_id</span><span class="p">),</span>
            <span class="n">session_id</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">source_ip</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">user_agent</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">resource_id</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">resource_type</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">action</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="s">"issue_token"</span><span class="nf">.to_string</span><span class="p">()),</span>
            <span class="n">outcome</span><span class="p">:</span> <span class="nn">SecurityOutcome</span><span class="p">::</span><span class="n">Success</span><span class="p">,</span>
            <span class="n">details</span><span class="p">,</span>
            <span class="n">correlation_id</span><span class="p">,</span>
        <span class="p">};</span>

        <span class="k">self</span><span class="py">.events</span><span class="nf">.push</span><span class="p">(</span><span class="n">event</span><span class="p">);</span>
        <span class="nd">info!</span><span class="p">(</span><span class="s">"Logged token issuance: {}"</span><span class="p">,</span> <span class="n">event_id</span><span class="p">);</span>
        <span class="n">event_id</span>
    <span class="p">}</span>

    <span class="cd">/// Log suspicious activity</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">log_suspicious_activity</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span>
        <span class="n">user_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">tenant_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">source_ip</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">activity_type</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
        <span class="n">details</span><span class="p">:</span> <span class="n">HashMap</span><span class="o">&lt;</span><span class="nb">String</span><span class="p">,</span> <span class="nn">serde_json</span><span class="p">::</span><span class="n">Value</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">correlation_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="p">)</span> <span class="k">-&gt;</span> <span class="n">Uuid</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">event_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>
        
        <span class="k">let</span> <span class="k">mut</span> <span class="n">event_details</span> <span class="o">=</span> <span class="n">details</span><span class="p">;</span>
        <span class="n">event_details</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"activity_type"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nn">Value</span><span class="p">::</span><span class="nf">String</span><span class="p">(</span><span class="n">activity_type</span><span class="p">));</span>

        <span class="k">let</span> <span class="n">event</span> <span class="o">=</span> <span class="n">SecurityEvent</span> <span class="p">{</span>
            <span class="n">event_id</span><span class="p">,</span>
            <span class="n">timestamp</span><span class="p">:</span> <span class="nn">Utc</span><span class="p">::</span><span class="nf">now</span><span class="p">(),</span>
            <span class="n">event_type</span><span class="p">:</span> <span class="nn">SecurityEventType</span><span class="p">::</span><span class="n">SuspiciousActivity</span><span class="p">,</span>
            <span class="n">severity</span><span class="p">:</span> <span class="nn">Severity</span><span class="p">::</span><span class="n">Error</span><span class="p">,</span>
            <span class="n">user_id</span><span class="p">,</span>
            <span class="n">tenant_id</span><span class="p">,</span>
            <span class="n">session_id</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">source_ip</span><span class="p">,</span>
            <span class="n">user_agent</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">resource_id</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">resource_type</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">action</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="s">"suspicious_activity"</span><span class="nf">.to_string</span><span class="p">()),</span>
            <span class="n">outcome</span><span class="p">:</span> <span class="nn">SecurityOutcome</span><span class="p">::</span><span class="n">Warning</span><span class="p">,</span>
            <span class="n">details</span><span class="p">:</span> <span class="n">event_details</span><span class="p">,</span>
            <span class="n">correlation_id</span><span class="p">,</span>
        <span class="p">};</span>

        <span class="k">self</span><span class="py">.events</span><span class="nf">.push</span><span class="p">(</span><span class="n">event</span><span class="p">);</span>
        <span class="nd">error!</span><span class="p">(</span><span class="s">"Logged suspicious activity: {}"</span><span class="p">,</span> <span class="n">event_id</span><span class="p">);</span>
        <span class="n">event_id</span>
    <span class="p">}</span>

    <span class="cd">/// Log certificate rotation</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">log_certificate_rotation</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span>
        <span class="n">certificate_type</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
        <span class="n">old_expiry</span><span class="p">:</span> <span class="n">DateTime</span><span class="o">&lt;</span><span class="n">Utc</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">new_expiry</span><span class="p">:</span> <span class="n">DateTime</span><span class="o">&lt;</span><span class="n">Utc</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="p">)</span> <span class="k">-&gt;</span> <span class="n">Uuid</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">event_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>
        
        <span class="k">let</span> <span class="k">mut</span> <span class="n">details</span> <span class="o">=</span> <span class="nn">HashMap</span><span class="p">::</span><span class="nf">new</span><span class="p">();</span>
        <span class="n">details</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"certificate_type"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nn">Value</span><span class="p">::</span><span class="nf">String</span><span class="p">(</span><span class="n">certificate_type</span><span class="p">));</span>
        <span class="n">details</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"old_expiry"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nn">Value</span><span class="p">::</span><span class="nf">String</span><span class="p">(</span><span class="n">old_expiry</span><span class="nf">.to_rfc3339</span><span class="p">()));</span>
        <span class="n">details</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"new_expiry"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nn">Value</span><span class="p">::</span><span class="nf">String</span><span class="p">(</span><span class="n">new_expiry</span><span class="nf">.to_rfc3339</span><span class="p">()));</span>

        <span class="k">let</span> <span class="n">event</span> <span class="o">=</span> <span class="n">SecurityEvent</span> <span class="p">{</span>
            <span class="n">event_id</span><span class="p">,</span>
            <span class="n">timestamp</span><span class="p">:</span> <span class="nn">Utc</span><span class="p">::</span><span class="nf">now</span><span class="p">(),</span>
            <span class="n">event_type</span><span class="p">:</span> <span class="nn">SecurityEventType</span><span class="p">::</span><span class="n">CertificateRotation</span><span class="p">,</span>
            <span class="n">severity</span><span class="p">:</span> <span class="nn">Severity</span><span class="p">::</span><span class="n">Info</span><span class="p">,</span>
            <span class="n">user_id</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">tenant_id</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">session_id</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">source_ip</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">user_agent</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">resource_id</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">resource_type</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">action</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="s">"rotate_certificate"</span><span class="nf">.to_string</span><span class="p">()),</span>
            <span class="n">outcome</span><span class="p">:</span> <span class="nn">SecurityOutcome</span><span class="p">::</span><span class="n">Success</span><span class="p">,</span>
            <span class="n">details</span><span class="p">,</span>
            <span class="n">correlation_id</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
        <span class="p">};</span>

        <span class="k">self</span><span class="py">.events</span><span class="nf">.push</span><span class="p">(</span><span class="n">event</span><span class="p">);</span>
        <span class="nd">info!</span><span class="p">(</span><span class="s">"Logged certificate rotation: {}"</span><span class="p">,</span> <span class="n">event_id</span><span class="p">);</span>
        <span class="n">event_id</span>
    <span class="p">}</span>

    <span class="cd">/// Search audit events</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">search_events</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> 
        <span class="n">event_type</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">SecurityEventType</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">severity</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Severity</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">user_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">tenant_id</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">since</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">DateTime</span><span class="o">&lt;</span><span class="n">Utc</span><span class="o">&gt;&gt;</span><span class="p">,</span>
        <span class="n">limit</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">usize</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Vec</span><span class="o">&lt;&amp;</span><span class="n">SecurityEvent</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">filtered</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;&amp;</span><span class="n">SecurityEvent</span><span class="o">&gt;</span> <span class="o">=</span> <span class="k">self</span><span class="py">.events</span>
            <span class="nf">.iter</span><span class="p">()</span>
            <span class="nf">.filter</span><span class="p">(|</span><span class="n">event</span><span class="p">|</span> <span class="p">{</span>
                <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="k">ref</span> <span class="n">et</span><span class="p">)</span> <span class="o">=</span> <span class="n">event_type</span> <span class="p">{</span>
                    <span class="k">if</span> <span class="nn">std</span><span class="p">::</span><span class="nn">mem</span><span class="p">::</span><span class="nf">discriminant</span><span class="p">(</span><span class="o">&amp;</span><span class="n">event</span><span class="py">.event_type</span><span class="p">)</span> <span class="o">!=</span> <span class="nn">std</span><span class="p">::</span><span class="nn">mem</span><span class="p">::</span><span class="nf">discriminant</span><span class="p">(</span><span class="n">et</span><span class="p">)</span> <span class="p">{</span>
                        <span class="k">return</span> <span class="k">false</span><span class="p">;</span>
                    <span class="p">}</span>
                <span class="p">}</span>
                <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="k">ref</span> <span class="n">sev</span><span class="p">)</span> <span class="o">=</span> <span class="n">severity</span> <span class="p">{</span>
                    <span class="k">if</span> <span class="nn">std</span><span class="p">::</span><span class="nn">mem</span><span class="p">::</span><span class="nf">discriminant</span><span class="p">(</span><span class="o">&amp;</span><span class="n">event</span><span class="py">.severity</span><span class="p">)</span> <span class="o">!=</span> <span class="nn">std</span><span class="p">::</span><span class="nn">mem</span><span class="p">::</span><span class="nf">discriminant</span><span class="p">(</span><span class="n">sev</span><span class="p">)</span> <span class="p">{</span>
                        <span class="k">return</span> <span class="k">false</span><span class="p">;</span>
                    <span class="p">}</span>
                <span class="p">}</span>
                <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">uid</span><span class="p">)</span> <span class="o">=</span> <span class="n">user_id</span> <span class="p">{</span>
                    <span class="k">if</span> <span class="n">event</span><span class="py">.user_id</span> <span class="o">!=</span> <span class="nf">Some</span><span class="p">(</span><span class="n">uid</span><span class="p">)</span> <span class="p">{</span>
                        <span class="k">return</span> <span class="k">false</span><span class="p">;</span>
                    <span class="p">}</span>
                <span class="p">}</span>
                <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">tid</span><span class="p">)</span> <span class="o">=</span> <span class="n">tenant_id</span> <span class="p">{</span>
                    <span class="k">if</span> <span class="n">event</span><span class="py">.tenant_id</span> <span class="o">!=</span> <span class="nf">Some</span><span class="p">(</span><span class="n">tid</span><span class="p">)</span> <span class="p">{</span>
                        <span class="k">return</span> <span class="k">false</span><span class="p">;</span>
                    <span class="p">}</span>
                <span class="p">}</span>
                <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">since_time</span><span class="p">)</span> <span class="o">=</span> <span class="n">since</span> <span class="p">{</span>
                    <span class="k">if</span> <span class="n">event</span><span class="py">.timestamp</span> <span class="o">&lt;</span> <span class="n">since_time</span> <span class="p">{</span>
                        <span class="k">return</span> <span class="k">false</span><span class="p">;</span>
                    <span class="p">}</span>
                <span class="p">}</span>
                <span class="k">true</span>
            <span class="p">})</span>
            <span class="nf">.collect</span><span class="p">();</span>

        <span class="c1">// Sort by timestamp, newest first</span>
        <span class="n">filtered</span><span class="nf">.sort_by</span><span class="p">(|</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">|</span> <span class="n">b</span><span class="py">.timestamp</span><span class="nf">.cmp</span><span class="p">(</span><span class="o">&amp;</span><span class="n">a</span><span class="py">.timestamp</span><span class="p">));</span>

        <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">limit</span><span class="p">)</span> <span class="o">=</span> <span class="n">limit</span> <span class="p">{</span>
            <span class="n">filtered</span><span class="nf">.truncate</span><span class="p">(</span><span class="n">limit</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="n">filtered</span>
    <span class="p">}</span>

    <span class="cd">/// Generate compliance report</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">generate_compliance_report</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> 
        <span class="n">start_date</span><span class="p">:</span> <span class="n">DateTime</span><span class="o">&lt;</span><span class="n">Utc</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">end_date</span><span class="p">:</span> <span class="n">DateTime</span><span class="o">&lt;</span><span class="n">Utc</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="p">)</span> <span class="k">-&gt;</span> <span class="n">ComplianceReport</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">events_in_range</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;&amp;</span><span class="n">SecurityEvent</span><span class="o">&gt;</span> <span class="o">=</span> <span class="k">self</span><span class="py">.events</span>
            <span class="nf">.iter</span><span class="p">()</span>
            <span class="nf">.filter</span><span class="p">(|</span><span class="n">event</span><span class="p">|</span> <span class="n">event</span><span class="py">.timestamp</span> <span class="o">&gt;=</span> <span class="n">start_date</span> <span class="o">&amp;&amp;</span> <span class="n">event</span><span class="py">.timestamp</span> <span class="o">&lt;=</span> <span class="n">end_date</span><span class="p">)</span>
            <span class="nf">.collect</span><span class="p">();</span>

        <span class="k">let</span> <span class="k">mut</span> <span class="n">authentication_attempts</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">authentication_failures</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">authorization_denials</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">suspicious_activities</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">certificate_rotations</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>

        <span class="k">for</span> <span class="n">event</span> <span class="k">in</span> <span class="o">&amp;</span><span class="n">events_in_range</span> <span class="p">{</span>
            <span class="k">match</span> <span class="n">event</span><span class="py">.event_type</span> <span class="p">{</span>
                <span class="nn">SecurityEventType</span><span class="p">::</span><span class="n">AuthenticationSuccess</span> <span class="k">=&gt;</span> <span class="n">authentication_attempts</span> <span class="o">+=</span> <span class="mi">1</span><span class="p">,</span>
                <span class="nn">SecurityEventType</span><span class="p">::</span><span class="n">AuthenticationFailure</span> <span class="k">=&gt;</span> <span class="p">{</span>
                    <span class="n">authentication_attempts</span> <span class="o">+=</span> <span class="mi">1</span><span class="p">;</span>
                    <span class="n">authentication_failures</span> <span class="o">+=</span> <span class="mi">1</span><span class="p">;</span>
                <span class="p">},</span>
                <span class="nn">SecurityEventType</span><span class="p">::</span><span class="n">AuthorizationDenied</span> <span class="k">=&gt;</span> <span class="n">authorization_denials</span> <span class="o">+=</span> <span class="mi">1</span><span class="p">,</span>
                <span class="nn">SecurityEventType</span><span class="p">::</span><span class="n">SuspiciousActivity</span> <span class="k">=&gt;</span> <span class="n">suspicious_activities</span> <span class="o">+=</span> <span class="mi">1</span><span class="p">,</span>
                <span class="nn">SecurityEventType</span><span class="p">::</span><span class="n">CertificateRotation</span> <span class="k">=&gt;</span> <span class="n">certificate_rotations</span> <span class="o">+=</span> <span class="mi">1</span><span class="p">,</span>
                <span class="n">_</span> <span class="k">=&gt;</span> <span class="p">{},</span>
            <span class="p">}</span>
        <span class="p">}</span>

        <span class="n">ComplianceReport</span> <span class="p">{</span>
            <span class="n">period_start</span><span class="p">:</span> <span class="n">start_date</span><span class="p">,</span>
            <span class="n">period_end</span><span class="p">:</span> <span class="n">end_date</span><span class="p">,</span>
            <span class="n">total_events</span><span class="p">:</span> <span class="n">events_in_range</span><span class="nf">.len</span><span class="p">(),</span>
            <span class="n">authentication_attempts</span><span class="p">,</span>
            <span class="n">authentication_failures</span><span class="p">,</span>
            <span class="n">authorization_denials</span><span class="p">,</span>
            <span class="n">suspicious_activities</span><span class="p">,</span>
            <span class="n">certificate_rotations</span><span class="p">,</span>
            <span class="n">failure_rate</span><span class="p">:</span> <span class="k">if</span> <span class="n">authentication_attempts</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="p">{</span>
                <span class="p">(</span><span class="n">authentication_failures</span> <span class="k">as</span> <span class="nb">f64</span> <span class="o">/</span> <span class="n">authentication_attempts</span> <span class="k">as</span> <span class="nb">f64</span><span class="p">)</span> <span class="o">*</span> <span class="mf">100.0</span>
            <span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
                <span class="mf">0.0</span>
            <span class="p">},</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Serialize)]</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">ComplianceReport</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="n">period_start</span><span class="p">:</span> <span class="n">DateTime</span><span class="o">&lt;</span><span class="n">Utc</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">period_end</span><span class="p">:</span> <span class="n">DateTime</span><span class="o">&lt;</span><span class="n">Utc</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">total_events</span><span class="p">:</span> <span class="nb">usize</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">authentication_attempts</span><span class="p">:</span> <span class="nb">usize</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">authentication_failures</span><span class="p">:</span> <span class="nb">usize</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">authorization_denials</span><span class="p">:</span> <span class="nb">usize</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">suspicious_activities</span><span class="p">:</span> <span class="nb">usize</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">certificate_rotations</span><span class="p">:</span> <span class="nb">usize</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">failure_rate</span><span class="p">:</span> <span class="nb">f64</span><span class="p">,</span> <span class="c1">// Percentage</span>
<span class="p">}</span>

<span class="cd">/// Audit middleware for HTTP requests</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">AuditMiddleware</span> <span class="p">{</span>
    <span class="n">audit_service</span><span class="p">:</span> <span class="nn">std</span><span class="p">::</span><span class="nn">sync</span><span class="p">::</span><span class="nb">Arc</span><span class="o">&lt;</span><span class="nn">std</span><span class="p">::</span><span class="nn">sync</span><span class="p">::</span><span class="n">Mutex</span><span class="o">&lt;</span><span class="n">AuditService</span><span class="o">&gt;&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">AuditMiddleware</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">new</span><span class="p">(</span><span class="n">audit_service</span><span class="p">:</span> <span class="nn">std</span><span class="p">::</span><span class="nn">sync</span><span class="p">::</span><span class="nb">Arc</span><span class="o">&lt;</span><span class="nn">std</span><span class="p">::</span><span class="nn">sync</span><span class="p">::</span><span class="n">Mutex</span><span class="o">&lt;</span><span class="n">AuditService</span><span class="o">&gt;&gt;</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="k">Self</span> <span class="p">{</span>
        <span class="k">Self</span> <span class="p">{</span> <span class="n">audit_service</span> <span class="p">}</span>
    <span class="p">}</span>

    <span class="cd">/// Log HTTP request for audit</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">log_request</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> 
        <span class="n">method</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">,</span>
        <span class="n">path</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">,</span>
        <span class="n">user_claims</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;&amp;</span><span class="n">UserClaims</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">source_ip</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">user_agent</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">response_status</span><span class="p">:</span> <span class="nb">u16</span><span class="p">,</span>
    <span class="p">)</span> <span class="p">{</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">audit</span> <span class="o">=</span> <span class="k">self</span><span class="py">.audit_service</span><span class="nf">.lock</span><span class="p">()</span><span class="nf">.unwrap</span><span class="p">();</span>
        
        <span class="c1">// Log based on response status</span>
        <span class="k">if</span> <span class="n">response_status</span> <span class="o">==</span> <span class="mi">401</span> <span class="p">{</span>
            <span class="n">audit</span><span class="nf">.log_authentication</span><span class="p">(</span>
                <span class="n">user_claims</span><span class="nf">.map</span><span class="p">(|</span><span class="n">c</span><span class="p">|</span> <span class="n">c</span><span class="py">.user_id</span><span class="p">),</span>
                <span class="n">user_claims</span><span class="nf">.map</span><span class="p">(|</span><span class="n">c</span><span class="p">|</span> <span class="n">c</span><span class="py">.tenant_id</span><span class="p">),</span>
                <span class="n">source_ip</span><span class="p">,</span>
                <span class="n">user_agent</span><span class="p">,</span>
                <span class="k">false</span><span class="p">,</span>
                <span class="nf">Some</span><span class="p">(</span><span class="s">"Unauthorized request"</span><span class="nf">.to_string</span><span class="p">()),</span>
            <span class="p">);</span>
        <span class="p">}</span> <span class="k">else</span> <span class="k">if</span> <span class="n">response_status</span> <span class="o">==</span> <span class="mi">403</span> <span class="p">{</span>
            <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">claims</span><span class="p">)</span> <span class="o">=</span> <span class="n">user_claims</span> <span class="p">{</span>
                <span class="n">audit</span><span class="nf">.log_authorization</span><span class="p">(</span>
                    <span class="n">claims</span><span class="py">.user_id</span><span class="p">,</span>
                    <span class="n">claims</span><span class="py">.tenant_id</span><span class="p">,</span>
                    <span class="nf">Some</span><span class="p">(</span><span class="n">claims</span><span class="py">.session_id</span><span class="p">),</span>
                    <span class="n">path</span><span class="nf">.to_string</span><span class="p">(),</span>
                    <span class="s">"http_endpoint"</span><span class="nf">.to_string</span><span class="p">(),</span>
                    <span class="n">method</span><span class="nf">.to_string</span><span class="p">(),</span>
                    <span class="k">false</span><span class="p">,</span>
                    <span class="n">claims</span><span class="py">.roles</span><span class="nf">.iter</span><span class="p">()</span><span class="nf">.map</span><span class="p">(|</span><span class="n">r</span><span class="p">|</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"{:?}"</span><span class="p">,</span> <span class="n">r</span><span class="p">))</span><span class="nf">.collect</span><span class="p">(),</span>
                    <span class="nb">None</span><span class="p">,</span>
                <span class="p">);</span>
            <span class="p">}</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="nd">#[cfg(test)]</span>
<span class="k">mod</span> <span class="n">tests</span> <span class="p">{</span>
    <span class="k">use</span> <span class="k">super</span><span class="p">::</span><span class="o">*</span><span class="p">;</span>

    <span class="nd">#[test]</span>
    <span class="k">fn</span> <span class="nf">test_audit_service</span><span class="p">()</span> <span class="p">{</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">audit</span> <span class="o">=</span> <span class="nn">AuditService</span><span class="p">::</span><span class="nf">new</span><span class="p">();</span>
        <span class="k">let</span> <span class="n">user_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>
        <span class="k">let</span> <span class="n">tenant_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>

        <span class="c1">// Test authentication logging</span>
        <span class="k">let</span> <span class="n">auth_event_id</span> <span class="o">=</span> <span class="n">audit</span><span class="nf">.log_authentication</span><span class="p">(</span>
            <span class="nf">Some</span><span class="p">(</span><span class="n">user_id</span><span class="p">),</span>
            <span class="nf">Some</span><span class="p">(</span><span class="n">tenant_id</span><span class="p">),</span>
            <span class="nf">Some</span><span class="p">(</span><span class="s">"192.168.1.1"</span><span class="nf">.to_string</span><span class="p">()),</span>
            <span class="nf">Some</span><span class="p">(</span><span class="s">"test-agent"</span><span class="nf">.to_string</span><span class="p">()),</span>
            <span class="k">true</span><span class="p">,</span>
            <span class="nb">None</span><span class="p">,</span>
        <span class="p">);</span>

        <span class="nd">assert!</span><span class="p">(</span><span class="n">audit</span><span class="py">.events</span><span class="nf">.iter</span><span class="p">()</span><span class="nf">.any</span><span class="p">(|</span><span class="n">e</span><span class="p">|</span> <span class="n">e</span><span class="py">.event_id</span> <span class="o">==</span> <span class="n">auth_event_id</span><span class="p">));</span>

        <span class="c1">// Test searching events</span>
        <span class="k">let</span> <span class="n">auth_events</span> <span class="o">=</span> <span class="n">audit</span><span class="nf">.search_events</span><span class="p">(</span>
            <span class="nf">Some</span><span class="p">(</span><span class="nn">SecurityEventType</span><span class="p">::</span><span class="n">AuthenticationSuccess</span><span class="p">),</span>
            <span class="nb">None</span><span class="p">,</span>
            <span class="nf">Some</span><span class="p">(</span><span class="n">user_id</span><span class="p">),</span>
            <span class="nb">None</span><span class="p">,</span>
            <span class="nb">None</span><span class="p">,</span>
            <span class="nb">None</span><span class="p">,</span>
        <span class="p">);</span>

        <span class="nd">assert_eq!</span><span class="p">(</span><span class="n">auth_events</span><span class="nf">.len</span><span class="p">(),</span> <span class="mi">1</span><span class="p">);</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h3 id="42-security-monitoring-configuration">4.2 Security Monitoring Configuration</h3>

<p><strong>Prometheus Metrics Configuration:</strong></p>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># prometheus_security_metrics.yml</span>
<span class="na">groups</span><span class="pi">:</span>
  <span class="pi">-</span> <span class="na">name</span><span class="pi">:</span> <span class="s">security_alerts</span>
    <span class="na">rules</span><span class="pi">:</span>
      <span class="c1"># Authentication failure rate</span>
      <span class="pi">-</span> <span class="na">alert</span><span class="pi">:</span> <span class="s">HighAuthenticationFailureRate</span>
        <span class="na">expr</span><span class="pi">:</span> <span class="s">rate(authentication_failures_total[5m]) &gt; </span><span class="m">0.1</span>
        <span class="na">for</span><span class="pi">:</span> <span class="s">2m</span>
        <span class="na">labels</span><span class="pi">:</span>
          <span class="na">severity</span><span class="pi">:</span> <span class="s">warning</span>
        <span class="na">annotations</span><span class="pi">:</span>
          <span class="na">summary</span><span class="pi">:</span> <span class="s2">"</span><span class="s">High</span><span class="nv"> </span><span class="s">authentication</span><span class="nv"> </span><span class="s">failure</span><span class="nv"> </span><span class="s">rate</span><span class="nv"> </span><span class="s">detected"</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Authentication</span><span class="nv"> </span><span class="s">failure</span><span class="nv"> </span><span class="s">rate</span><span class="nv"> </span><span class="s">is</span><span class="nv">  </span><span class="s">failures/second"</span>

      <span class="c1"># Suspicious activity detection</span>
      <span class="pi">-</span> <span class="na">alert</span><span class="pi">:</span> <span class="s">SuspiciousActivity</span>
        <span class="na">expr</span><span class="pi">:</span> <span class="s">rate(suspicious_activity_total[1m]) &gt; </span><span class="m">0</span>
        <span class="na">for</span><span class="pi">:</span> <span class="s">0m</span>
        <span class="na">labels</span><span class="pi">:</span>
          <span class="na">severity</span><span class="pi">:</span> <span class="s">critical</span>
        <span class="na">annotations</span><span class="pi">:</span>
          <span class="na">summary</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Suspicious</span><span class="nv"> </span><span class="s">activity</span><span class="nv"> </span><span class="s">detected"</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="nv"> </span><span class="s">suspicious</span><span class="nv"> </span><span class="s">activities</span><span class="nv"> </span><span class="s">detected"</span>

      <span class="c1"># Certificate expiration warning</span>
      <span class="pi">-</span> <span class="na">alert</span><span class="pi">:</span> <span class="s">CertificateExpiringSoon</span>
        <span class="na">expr</span><span class="pi">:</span> <span class="s">certificate_expiry_days &lt; </span><span class="m">30</span>
        <span class="na">for</span><span class="pi">:</span> <span class="s">0m</span>
        <span class="na">labels</span><span class="pi">:</span>
          <span class="na">severity</span><span class="pi">:</span> <span class="s">warning</span>
        <span class="na">annotations</span><span class="pi">:</span>
          <span class="na">summary</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Certificate</span><span class="nv"> </span><span class="s">expiring</span><span class="nv"> </span><span class="s">soon"</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Certificate</span><span class="nv">  </span><span class="s">expires</span><span class="nv"> </span><span class="s">in</span><span class="nv">  </span><span class="s">days"</span>

      <span class="c1"># Token issuance rate anomaly</span>
      <span class="pi">-</span> <span class="na">alert</span><span class="pi">:</span> <span class="s">UnusualTokenIssuanceRate</span>
        <span class="na">expr</span><span class="pi">:</span> <span class="s">rate(tokens_issued_total[10m]) &gt; </span><span class="m">10</span>
        <span class="na">for</span><span class="pi">:</span> <span class="s">5m</span>
        <span class="na">labels</span><span class="pi">:</span>
          <span class="na">severity</span><span class="pi">:</span> <span class="s">warning</span>
        <span class="na">annotations</span><span class="pi">:</span>
          <span class="na">summary</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Unusual</span><span class="nv"> </span><span class="s">token</span><span class="nv"> </span><span class="s">issuance</span><span class="nv"> </span><span class="s">rate"</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Token</span><span class="nv"> </span><span class="s">issuance</span><span class="nv"> </span><span class="s">rate</span><span class="nv"> </span><span class="s">is</span><span class="nv">  </span><span class="s">tokens/second"</span>

      <span class="c1"># Failed authorization attempts</span>
      <span class="pi">-</span> <span class="na">alert</span><span class="pi">:</span> <span class="s">RepeatedAuthorizationDenials</span>
        <span class="na">expr</span><span class="pi">:</span> <span class="s">rate(authorization_denied_total[5m]) by (user_id) &gt; </span><span class="m">0.5</span>
        <span class="na">for</span><span class="pi">:</span> <span class="s">1m</span>
        <span class="na">labels</span><span class="pi">:</span>
          <span class="na">severity</span><span class="pi">:</span> <span class="s">warning</span>
        <span class="na">annotations</span><span class="pi">:</span>
          <span class="na">summary</span><span class="pi">:</span> <span class="s2">"</span><span class="s">Repeated</span><span class="nv"> </span><span class="s">authorization</span><span class="nv"> </span><span class="s">denials</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">user"</span>
          <span class="na">description</span><span class="pi">:</span> <span class="s2">"</span><span class="s">User</span><span class="nv">  </span><span class="s">has</span><span class="nv">  </span><span class="s">authorization</span><span class="nv"> </span><span class="s">denials/second"</span>
</code></pre></div></div>

<p><strong>Grafana Dashboard Configuration:</strong></p>
<div class="language-json highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"dashboard"</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">"id"</span><span class="p">:</span><span class="w"> </span><span class="kc">null</span><span class="p">,</span><span class="w">
    </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Mister Smith Security Dashboard"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"tags"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"security"</span><span class="p">,</span><span class="w"> </span><span class="s2">"audit"</span><span class="p">],</span><span class="w">
    </span><span class="nl">"timezone"</span><span class="p">:</span><span class="w"> </span><span class="s2">"UTC"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"panels"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Authentication Events"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"stat"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"targets"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"rate(authentication_success_total[1h])"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"legendFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Success Rate"</span><span class="w">
          </span><span class="p">},</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"rate(authentication_failure_total[1h])"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"legendFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Failure Rate"</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">]</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Security Events Timeline"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"graph"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"targets"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"rate(security_events_total[5m]) by (event_type)"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"legendFormat"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">]</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Certificate Expiry Status"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"table"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"targets"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"certificate_expiry_days"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"table"</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">]</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">"title"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Top Failed Authorization Attempts"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"table"</span><span class="p">,</span><span class="w">
        </span><span class="nl">"targets"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
          </span><span class="p">{</span><span class="w">
            </span><span class="nl">"expr"</span><span class="p">:</span><span class="w"> </span><span class="s2">"topk(10, rate(authorization_denied_total[1h]) by (user_id, resource_type))"</span><span class="p">,</span><span class="w">
            </span><span class="nl">"format"</span><span class="p">:</span><span class="w"> </span><span class="s2">"table"</span><span class="w">
          </span><span class="p">}</span><span class="w">
        </span><span class="p">]</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">]</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h2 id="5-nats-security-implementation">5. NATS Security Implementation</h2>

<h3 id="51-nats-server-configuration-with-mtls">5.1 NATS Server Configuration with mTLS</h3>

<p><strong>Complete NATS Server Configuration:</strong></p>
<div class="language-hocon highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># nats_server_secure.conf</span><span class="w">
</span><span class="c1"># NATS Server Security Configuration for Mister Smith Framework</span><span class="w">

</span><span class="c1"># Server identity</span><span class="w">
</span><span class="nl">server_name</span><span class="p">:</span><span class="w"> </span><span class="s2">"mister-smith-nats"</span><span class="w">

</span><span class="c1"># Network configuration</span><span class="w">
</span><span class="nl">host</span><span class="p">:</span><span class="w"> </span><span class="s2">"0.0.0.0"</span><span class="w">
</span><span class="nl">port</span><span class="p">:</span><span class="w"> </span><span class="mi">4222</span><span class="w">
</span><span class="nl">http_port</span><span class="p">:</span><span class="w"> </span><span class="mi">8222</span><span class="w">

</span><span class="c1"># TLS Configuration with mTLS enforcement</span><span class="w">
</span><span class="nl">tls</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">cert_file</span><span class="p">:</span><span class="w"> </span><span class="s2">"/etc/mister-smith/certs/server/server-cert.pem"</span><span class="w">
  </span><span class="nl">key_file</span><span class="p">:</span><span class="w"> </span><span class="s2">"/etc/mister-smith/certs/server/server-key.pem"</span><span class="w">
  </span><span class="nl">ca_file</span><span class="p">:</span><span class="w"> </span><span class="s2">"/etc/mister-smith/certs/ca/ca-cert.pem"</span><span class="w">
  </span><span class="nl">verify</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
  </span><span class="nl">verify_and_map</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
  </span><span class="nl">timeout</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="w">
  
  </span><span class="c1"># Force all connections to use TLS</span><span class="w">
  </span><span class="nl">insecure</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="c1"># JetStream configuration with security</span><span class="w">
</span><span class="nl">jetstream</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">enabled</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
  </span><span class="nl">store_dir</span><span class="p">:</span><span class="w"> </span><span class="s2">"/data/nats/jetstream"</span><span class="w">
  </span><span class="nl">max_memory_store</span><span class="p">:</span><span class="w"> </span><span class="mi">4</span><span class="l">GB</span><span class="w">
  </span><span class="nl">max_file_store</span><span class="p">:</span><span class="w"> </span><span class="mi">20</span><span class="l">GB</span><span class="w">
  
  </span><span class="c1"># Domain isolation</span><span class="w">
  </span><span class="nl">domain</span><span class="p">:</span><span class="w"> </span><span class="s2">"mister-smith"</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="c1"># Account-based multi-tenancy</span><span class="w">
</span><span class="nl">accounts</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="c1"># System account for internal operations</span><span class="w">
  </span><span class="nl">SYS</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">users</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">user</span><span class="p">:</span><span class="w"> </span><span class="s2">"system"</span><span class="p">,</span><span class="w">
        </span><span class="nl">password</span><span class="p">:</span><span class="w"> </span><span class="s2">"$2a$11$..."</span><span class="w">  </span><span class="c1"># bcrypt hash</span><span class="w">
        </span><span class="nl">permissions</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">publish</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"&gt;"</span><span class="p">]</span><span class="w">
          </span><span class="nl">subscribe</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"&gt;"</span><span class="p">]</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">]</span><span class="w">
  </span><span class="p">}</span><span class="w">
  
  </span><span class="c1"># Template for tenant accounts</span><span class="w">
  </span><span class="nl">TENANT_A</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">users</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">user</span><span class="p">:</span><span class="w"> </span><span class="s2">"tenant_a_admin"</span><span class="p">,</span><span class="w">
        </span><span class="nl">password</span><span class="p">:</span><span class="w"> </span><span class="s2">"$2a$11$..."</span><span class="w">
        </span><span class="nl">permissions</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">publish</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"tenantA.&gt;"</span><span class="p">]</span><span class="w">
          </span><span class="nl">subscribe</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"tenantA.&gt;"</span><span class="p">,</span><span class="w"> </span><span class="s2">"_INBOX.&gt;"</span><span class="p">]</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="p">{</span><span class="w">
        </span><span class="nl">user</span><span class="p">:</span><span class="w"> </span><span class="s2">"tenant_a_service"</span><span class="p">,</span><span class="w">
        </span><span class="nl">password</span><span class="p">:</span><span class="w"> </span><span class="s2">"$2a$11$..."</span><span class="w">
        </span><span class="nl">permissions</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
          </span><span class="nl">publish</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"tenantA.services.&gt;"</span><span class="p">]</span><span class="w">
          </span><span class="nl">subscribe</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">"tenantA.services.&gt;"</span><span class="p">,</span><span class="w"> </span><span class="s2">"_INBOX.&gt;"</span><span class="p">]</span><span class="w">
        </span><span class="p">}</span><span class="w">
      </span><span class="p">}</span><span class="w">
    </span><span class="p">]</span><span class="w">
    
    </span><span class="c1"># JetStream limits per tenant</span><span class="w">
    </span><span class="nl">jetstream</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">max_memory</span><span class="p">:</span><span class="w"> </span><span class="mi">512</span><span class="l">MB</span><span class="w">
      </span><span class="nl">max_disk</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="l">GB</span><span class="w">
      </span><span class="nl">max_streams</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="w">
      </span><span class="nl">max_consumers</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="w">
    </span><span class="p">}</span><span class="w">
    
    </span><span class="c1"># Connection limits</span><span class="w">
    </span><span class="nl">limits</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w">
      </span><span class="nl">max_connections</span><span class="p">:</span><span class="w"> </span><span class="mi">50</span><span class="w">
      </span><span class="nl">max_subscriptions</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span><span class="w">
      </span><span class="nl">max_payload</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="l">MB</span><span class="w">
      </span><span class="nl">max_data</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="l">MB</span><span class="w">
      </span><span class="nl">max_ack_pending</span><span class="p">:</span><span class="w"> </span><span class="mi">65536</span><span class="w">
    </span><span class="p">}</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="c1"># System account designation</span><span class="w">
</span><span class="nl">system_account</span><span class="p">:</span><span class="w"> </span><span class="s2">"SYS"</span><span class="w">

</span><span class="c1"># Connection limits</span><span class="w">
</span><span class="nl">max_connections</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span><span class="w">
</span><span class="nl">max_control_line</span><span class="p">:</span><span class="w"> </span><span class="mi">4096</span><span class="w">
</span><span class="nl">max_payload</span><span class="p">:</span><span class="w"> </span><span class="mi">1048576</span><span class="w">
</span><span class="nl">ping_interval</span><span class="p">:</span><span class="w"> </span><span class="s2">"2m"</span><span class="w">
</span><span class="nl">ping_max</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="w">
</span><span class="nl">write_deadline</span><span class="p">:</span><span class="w"> </span><span class="s2">"10s"</span><span class="w">

</span><span class="c1"># Clustering for high availability</span><span class="w">
</span><span class="nl">cluster</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">name</span><span class="p">:</span><span class="w"> </span><span class="s2">"mister-smith-cluster"</span><span class="w">
  </span><span class="nl">host</span><span class="p">:</span><span class="w"> </span><span class="s2">"0.0.0.0"</span><span class="w">
  </span><span class="nl">port</span><span class="p">:</span><span class="w"> </span><span class="mi">6222</span><span class="w">
  
  </span><span class="c1"># Cluster TLS</span><span class="w">
  </span><span class="nl">tls</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">cert_file</span><span class="p">:</span><span class="w"> </span><span class="s2">"/etc/mister-smith/certs/server/server-cert.pem"</span><span class="w">
    </span><span class="nl">key_file</span><span class="p">:</span><span class="w"> </span><span class="s2">"/etc/mister-smith/certs/server/server-key.pem"</span><span class="w">
    </span><span class="nl">ca_file</span><span class="p">:</span><span class="w"> </span><span class="s2">"/etc/mister-smith/certs/ca/ca-cert.pem"</span><span class="w">
    </span><span class="nl">verify</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
    </span><span class="nl">timeout</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="w">
  </span><span class="p">}</span><span class="w">
  
  </span><span class="c1"># Cluster routes</span><span class="w">
  </span><span class="nl">routes</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
    </span><span class="s2">"nats://nats-1.mister-smith.local:6222"</span><span class="w">
    </span><span class="s2">"nats://nats-2.mister-smith.local:6222"</span><span class="w">
    </span><span class="s2">"nats://nats-3.mister-smith.local:6222"</span><span class="w">
  </span><span class="p">]</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="c1"># Gateway configuration for multi-region</span><span class="w">
</span><span class="nl">gateway</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">name</span><span class="p">:</span><span class="w"> </span><span class="s2">"mister-smith-gateway"</span><span class="w">
  </span><span class="nl">host</span><span class="p">:</span><span class="w"> </span><span class="s2">"0.0.0.0"</span><span class="w">
  </span><span class="nl">port</span><span class="p">:</span><span class="w"> </span><span class="mi">7222</span><span class="w">
  
  </span><span class="c1"># Gateway TLS</span><span class="w">
  </span><span class="nl">tls</span><span class="w"> </span><span class="p">{</span><span class="w">
    </span><span class="nl">cert_file</span><span class="p">:</span><span class="w"> </span><span class="s2">"/etc/mister-smith/certs/server/server-cert.pem"</span><span class="w">
    </span><span class="nl">key_file</span><span class="p">:</span><span class="w"> </span><span class="s2">"/etc/mister-smith/certs/server/server-key.pem"</span><span class="w">
    </span><span class="nl">ca_file</span><span class="p">:</span><span class="w"> </span><span class="s2">"/etc/mister-smith/certs/ca/ca-cert.pem"</span><span class="w">
    </span><span class="nl">verify</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="c1"># Monitoring and debugging</span><span class="w">
</span><span class="nl">debug</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w">
</span><span class="nl">trace</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="w">
</span><span class="nl">logtime</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
</span><span class="nl">log_file</span><span class="p">:</span><span class="w"> </span><span class="s2">"/var/log/nats/nats-server.log"</span><span class="w">
</span><span class="nl">pid_file</span><span class="p">:</span><span class="w"> </span><span class="s2">"/var/run/nats/nats-server.pid"</span><span class="w">

</span><span class="c1"># Disable non-TLS connections completely</span><span class="w">
</span><span class="nl">no_auth_user</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="w">
</span><span class="nl">authorization</span><span class="w"> </span><span class="p">{</span><span class="w">
  </span><span class="nl">timeout</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div></div>

<h3 id="52-nats-client-implementation-with-mtls">5.2 NATS Client Implementation with mTLS</h3>

<p><strong>Secure NATS Client:</strong></p>
<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// nats_client.rs</span>
<span class="k">use</span> <span class="nn">nats</span><span class="p">::</span><span class="nn">asynk</span><span class="p">::{</span><span class="n">Connection</span><span class="p">,</span> <span class="n">Options</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">anyhow</span><span class="p">::{</span><span class="nb">Result</span><span class="p">,</span> <span class="n">Context</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">uuid</span><span class="p">::</span><span class="n">Uuid</span><span class="p">;</span>
<span class="k">use</span> <span class="nn">serde</span><span class="p">::{</span><span class="n">Serialize</span><span class="p">,</span> <span class="n">Deserialize</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">tracing</span><span class="p">::{</span><span class="n">info</span><span class="p">,</span> <span class="n">warn</span><span class="p">,</span> <span class="n">error</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">time</span><span class="p">::</span><span class="n">Duration</span><span class="p">;</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone)]</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">NatsSecureClient</span> <span class="p">{</span>
    <span class="n">connection</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Connection</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="n">tenant_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
    <span class="n">client_cert_path</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="n">client_key_path</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="n">ca_cert_path</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="n">nats_urls</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">NatsSecureClient</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">new</span><span class="p">(</span><span class="n">tenant_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="k">Self</span> <span class="p">{</span>
        <span class="k">Self</span> <span class="p">{</span>
            <span class="n">connection</span><span class="p">:</span> <span class="nb">None</span><span class="p">,</span>
            <span class="n">tenant_id</span><span class="p">,</span>
            <span class="n">client_cert_path</span><span class="p">:</span> <span class="s">"/etc/mister-smith/certs/client/client-cert.pem"</span><span class="nf">.to_string</span><span class="p">(),</span>
            <span class="n">client_key_path</span><span class="p">:</span> <span class="s">"/etc/mister-smith/certs/client/client-key.pem"</span><span class="nf">.to_string</span><span class="p">(),</span>
            <span class="n">ca_cert_path</span><span class="p">:</span> <span class="s">"/etc/mister-smith/certs/ca/ca-cert.pem"</span><span class="nf">.to_string</span><span class="p">(),</span>
            <span class="n">nats_urls</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span>
                <span class="s">"tls://nats-1.mister-smith.local:4222"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="s">"tls://nats-2.mister-smith.local:4222"</span><span class="nf">.to_string</span><span class="p">(),</span>
                <span class="s">"tls://nats-3.mister-smith.local:4222"</span><span class="nf">.to_string</span><span class="p">(),</span>
            <span class="p">],</span>
        <span class="p">}</span>
    <span class="p">}</span>

    <span class="cd">/// Connect to NATS with mTLS</span>
    <span class="k">pub</span> <span class="k">async</span> <span class="k">fn</span> <span class="nf">connect</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span> <span class="n">username</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">,</span> <span class="n">password</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">options</span> <span class="o">=</span> <span class="nn">Options</span><span class="p">::</span><span class="nf">new</span><span class="p">()</span>
            <span class="nf">.with_name</span><span class="p">(</span><span class="o">&amp;</span><span class="nd">format!</span><span class="p">(</span><span class="s">"mister-smith-client-{}"</span><span class="p">,</span> <span class="k">self</span><span class="py">.tenant_id</span><span class="p">))</span>
            <span class="nf">.with_user_and_password</span><span class="p">(</span><span class="n">username</span><span class="p">,</span> <span class="n">password</span><span class="p">)</span>
            <span class="nf">.with_client_cert</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.client_cert_path</span><span class="p">,</span> <span class="o">&amp;</span><span class="k">self</span><span class="py">.client_key_path</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to load client certificate"</span><span class="p">)</span><span class="o">?</span>
            <span class="nf">.with_root_certificates</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.ca_cert_path</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to load CA certificate"</span><span class="p">)</span><span class="o">?</span>
            <span class="nf">.require_tls</span><span class="p">(</span><span class="k">true</span><span class="p">)</span>
            <span class="nf">.with_connection_timeout</span><span class="p">(</span><span class="nn">Duration</span><span class="p">::</span><span class="nf">from_secs</span><span class="p">(</span><span class="mi">10</span><span class="p">))</span>
            <span class="nf">.with_reconnect_buffer_size</span><span class="p">(</span><span class="mi">8</span> <span class="o">*</span> <span class="mi">1024</span> <span class="o">*</span> <span class="mi">1024</span><span class="p">)</span> <span class="c1">// 8MB</span>
            <span class="nf">.with_max_reconnects</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>
            <span class="nf">.with_ping_interval</span><span class="p">(</span><span class="nn">Duration</span><span class="p">::</span><span class="nf">from_secs</span><span class="p">(</span><span class="mi">30</span><span class="p">));</span>

        <span class="k">let</span> <span class="n">connection</span> <span class="o">=</span> <span class="n">options</span>
            <span class="nf">.connect</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.nats_urls</span><span class="p">)</span>
            <span class="k">.await</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to connect to NATS server"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="k">self</span><span class="py">.connection</span> <span class="o">=</span> <span class="nf">Some</span><span class="p">(</span><span class="n">connection</span><span class="p">);</span>
        <span class="nd">info!</span><span class="p">(</span><span class="s">"Connected to NATS with mTLS for tenant: {}"</span><span class="p">,</span> <span class="k">self</span><span class="py">.tenant_id</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>

    <span class="cd">/// Publish message to tenant-scoped subject</span>
    <span class="k">pub</span> <span class="k">async</span> <span class="k">fn</span> <span class="n">publish</span><span class="o">&lt;</span><span class="n">T</span><span class="p">:</span> <span class="n">Serialize</span><span class="o">&gt;</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">subject</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">T</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">connection</span> <span class="o">=</span> <span class="k">self</span><span class="py">.connection</span><span class="nf">.as_ref</span><span class="p">()</span>
            <span class="nf">.ok_or_else</span><span class="p">(||</span> <span class="nn">anyhow</span><span class="p">::</span><span class="nd">anyhow!</span><span class="p">(</span><span class="s">"Not connected to NATS"</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>

        <span class="k">let</span> <span class="n">tenant_subject</span> <span class="o">=</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"tenant{}.{}"</span><span class="p">,</span> <span class="k">self</span><span class="py">.tenant_id</span><span class="p">,</span> <span class="n">subject</span><span class="p">);</span>
        <span class="k">let</span> <span class="n">payload</span> <span class="o">=</span> <span class="nn">serde_json</span><span class="p">::</span><span class="nf">to_vec</span><span class="p">(</span><span class="n">message</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to serialize message"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Check payload size (1MB limit)</span>
        <span class="k">if</span> <span class="n">payload</span><span class="nf">.len</span><span class="p">()</span> <span class="o">&gt;</span> <span class="mi">1024</span> <span class="o">*</span> <span class="mi">1024</span> <span class="p">{</span>
            <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span><span class="s">"Message payload exceeds 1MB limit"</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="n">connection</span><span class="nf">.publish</span><span class="p">(</span><span class="o">&amp;</span><span class="n">tenant_subject</span><span class="p">,</span> <span class="n">payload</span><span class="p">)</span>
            <span class="k">.await</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"Failed to publish to subject: {}"</span><span class="p">,</span> <span class="n">tenant_subject</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Published message to subject: {}"</span><span class="p">,</span> <span class="n">tenant_subject</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>

    <span class="cd">/// Subscribe to tenant-scoped subject</span>
    <span class="k">pub</span> <span class="k">async</span> <span class="k">fn</span> <span class="nf">subscribe</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">subject</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="nn">nats</span><span class="p">::</span><span class="nn">asynk</span><span class="p">::</span><span class="n">Subscription</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">connection</span> <span class="o">=</span> <span class="k">self</span><span class="py">.connection</span><span class="nf">.as_ref</span><span class="p">()</span>
            <span class="nf">.ok_or_else</span><span class="p">(||</span> <span class="nn">anyhow</span><span class="p">::</span><span class="nd">anyhow!</span><span class="p">(</span><span class="s">"Not connected to NATS"</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>

        <span class="k">let</span> <span class="n">tenant_subject</span> <span class="o">=</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"tenant{}.{}"</span><span class="p">,</span> <span class="k">self</span><span class="py">.tenant_id</span><span class="p">,</span> <span class="n">subject</span><span class="p">);</span>
        
        <span class="k">let</span> <span class="n">subscription</span> <span class="o">=</span> <span class="n">connection</span><span class="nf">.subscribe</span><span class="p">(</span><span class="o">&amp;</span><span class="n">tenant_subject</span><span class="p">)</span>
            <span class="k">.await</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"Failed to subscribe to subject: {}"</span><span class="p">,</span> <span class="n">tenant_subject</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Subscribed to subject: {}"</span><span class="p">,</span> <span class="n">tenant_subject</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(</span><span class="n">subscription</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Create JetStream consumer with security constraints</span>
    <span class="k">pub</span> <span class="k">async</span> <span class="k">fn</span> <span class="nf">create_consumer</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">stream_name</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">,</span> <span class="n">consumer_config</span><span class="p">:</span> <span class="n">ConsumerConfig</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">connection</span> <span class="o">=</span> <span class="k">self</span><span class="py">.connection</span><span class="nf">.as_ref</span><span class="p">()</span>
            <span class="nf">.ok_or_else</span><span class="p">(||</span> <span class="nn">anyhow</span><span class="p">::</span><span class="nd">anyhow!</span><span class="p">(</span><span class="s">"Not connected to NATS"</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>

        <span class="k">let</span> <span class="n">js</span> <span class="o">=</span> <span class="nn">nats</span><span class="p">::</span><span class="nn">jetstream</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">connection</span><span class="nf">.clone</span><span class="p">());</span>
        <span class="k">let</span> <span class="n">tenant_stream</span> <span class="o">=</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"tenant{}.{}"</span><span class="p">,</span> <span class="k">self</span><span class="py">.tenant_id</span><span class="p">,</span> <span class="n">stream_name</span><span class="p">);</span>

        <span class="c1">// Enforce security constraints</span>
        <span class="k">let</span> <span class="n">secure_config</span> <span class="o">=</span> <span class="n">ConsumerConfig</span> <span class="p">{</span>
            <span class="n">deliver_subject</span><span class="p">:</span> <span class="n">consumer_config</span><span class="py">.deliver_subject</span><span class="nf">.map</span><span class="p">(|</span><span class="n">s</span><span class="p">|</span> 
                <span class="nd">format!</span><span class="p">(</span><span class="s">"tenant{}.{}"</span><span class="p">,</span> <span class="k">self</span><span class="py">.tenant_id</span><span class="p">,</span> <span class="n">s</span><span class="p">)</span>
            <span class="p">),</span>
            <span class="n">max_deliver</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="n">consumer_config</span><span class="py">.max_deliver</span><span class="nf">.unwrap_or</span><span class="p">(</span><span class="mi">5</span><span class="p">)),</span>
            <span class="n">max_ack_pending</span><span class="p">:</span> <span class="nf">Some</span><span class="p">(</span><span class="n">consumer_config</span><span class="py">.max_ack_pending</span><span class="nf">.unwrap_or</span><span class="p">(</span><span class="mi">1000</span><span class="p">)),</span>
            <span class="o">..</span><span class="n">consumer_config</span>
        <span class="p">};</span>

        <span class="n">js</span><span class="nf">.add_consumer</span><span class="p">(</span><span class="o">&amp;</span><span class="n">tenant_stream</span><span class="p">,</span> <span class="o">&amp;</span><span class="n">secure_config</span><span class="p">)</span>
            <span class="k">.await</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"Failed to create consumer for stream: {}"</span><span class="p">,</span> <span class="n">tenant_stream</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Created consumer for stream: {}"</span><span class="p">,</span> <span class="n">tenant_stream</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>

    <span class="cd">/// Health check for connection</span>
    <span class="k">pub</span> <span class="k">async</span> <span class="k">fn</span> <span class="nf">health_check</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">connection</span> <span class="o">=</span> <span class="k">self</span><span class="py">.connection</span><span class="nf">.as_ref</span><span class="p">()</span>
            <span class="nf">.ok_or_else</span><span class="p">(||</span> <span class="nn">anyhow</span><span class="p">::</span><span class="nd">anyhow!</span><span class="p">(</span><span class="s">"Not connected to NATS"</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Send a ping and wait for pong</span>
        <span class="n">connection</span><span class="nf">.flush</span><span class="p">()</span><span class="k">.await</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Health check failed"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>

    <span class="cd">/// Disconnect from NATS</span>
    <span class="k">pub</span> <span class="k">async</span> <span class="k">fn</span> <span class="nf">disconnect</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">connection</span><span class="p">)</span> <span class="o">=</span> <span class="k">self</span><span class="py">.connection</span><span class="nf">.take</span><span class="p">()</span> <span class="p">{</span>
            <span class="n">connection</span><span class="nf">.close</span><span class="p">()</span><span class="k">.await</span><span class="p">;</span>
            <span class="nd">info!</span><span class="p">(</span><span class="s">"Disconnected from NATS for tenant: {}"</span><span class="p">,</span> <span class="k">self</span><span class="py">.tenant_id</span><span class="p">);</span>
        <span class="p">}</span>
        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="c1">// JetStream configuration types</span>
<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">ConsumerConfig</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="n">durable_name</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">deliver_subject</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">deliver_policy</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">opt_start_seq</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">u64</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">opt_start_time</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">ack_policy</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">ack_wait</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="n">Duration</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">max_deliver</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">i32</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">max_ack_pending</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">i32</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">replay_policy</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="cd">/// NATS connection pool for high-performance applications</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">NatsConnectionPool</span> <span class="p">{</span>
    <span class="n">pools</span><span class="p">:</span> <span class="nn">std</span><span class="p">::</span><span class="nn">collections</span><span class="p">::</span><span class="n">HashMap</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="p">,</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">NatsSecureClient</span><span class="o">&gt;&gt;</span><span class="p">,</span>
    <span class="n">pool_size</span><span class="p">:</span> <span class="nb">usize</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">NatsConnectionPool</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">new</span><span class="p">(</span><span class="n">pool_size</span><span class="p">:</span> <span class="nb">usize</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="k">Self</span> <span class="p">{</span>
        <span class="k">Self</span> <span class="p">{</span>
            <span class="n">pools</span><span class="p">:</span> <span class="nn">std</span><span class="p">::</span><span class="nn">collections</span><span class="p">::</span><span class="nn">HashMap</span><span class="p">::</span><span class="nf">new</span><span class="p">(),</span>
            <span class="n">pool_size</span><span class="p">,</span>
        <span class="p">}</span>
    <span class="p">}</span>

    <span class="cd">/// Get or create connection pool for tenant</span>
    <span class="k">pub</span> <span class="k">async</span> <span class="k">fn</span> <span class="nf">get_connection</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span> <span class="n">tenant_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span> <span class="n">username</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">,</span> <span class="n">password</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;&amp;</span><span class="k">mut</span> <span class="n">NatsSecureClient</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">if</span> <span class="o">!</span><span class="k">self</span><span class="py">.pools</span><span class="nf">.contains_key</span><span class="p">(</span><span class="o">&amp;</span><span class="n">tenant_id</span><span class="p">)</span> <span class="p">{</span>
            <span class="k">let</span> <span class="k">mut</span> <span class="n">pool</span> <span class="o">=</span> <span class="nn">Vec</span><span class="p">::</span><span class="nf">new</span><span class="p">();</span>
            <span class="k">for</span> <span class="n">_</span> <span class="k">in</span> <span class="mi">0</span><span class="o">..</span><span class="k">self</span><span class="py">.pool_size</span> <span class="p">{</span>
                <span class="k">let</span> <span class="k">mut</span> <span class="n">client</span> <span class="o">=</span> <span class="nn">NatsSecureClient</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">tenant_id</span><span class="p">);</span>
                <span class="n">client</span><span class="nf">.connect</span><span class="p">(</span><span class="n">username</span><span class="p">,</span> <span class="n">password</span><span class="p">)</span><span class="k">.await</span><span class="o">?</span><span class="p">;</span>
                <span class="n">pool</span><span class="nf">.push</span><span class="p">(</span><span class="n">client</span><span class="p">);</span>
            <span class="p">}</span>
            <span class="k">self</span><span class="py">.pools</span><span class="nf">.insert</span><span class="p">(</span><span class="n">tenant_id</span><span class="p">,</span> <span class="n">pool</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="k">let</span> <span class="n">pool</span> <span class="o">=</span> <span class="k">self</span><span class="py">.pools</span><span class="nf">.get_mut</span><span class="p">(</span><span class="o">&amp;</span><span class="n">tenant_id</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">();</span>
        <span class="c1">// Simple round-robin selection (in production, use more sophisticated load balancing)</span>
        <span class="nf">Ok</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="n">pool</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="nd">#[cfg(test)]</span>
<span class="k">mod</span> <span class="n">tests</span> <span class="p">{</span>
    <span class="k">use</span> <span class="k">super</span><span class="p">::</span><span class="o">*</span><span class="p">;</span>

    <span class="nd">#[tokio::test]</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">test_nats_secure_client</span><span class="p">()</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">tenant_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">client</span> <span class="o">=</span> <span class="nn">NatsSecureClient</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">tenant_id</span><span class="p">);</span>

        <span class="c1">// This would require a running NATS server with proper certificates</span>
        <span class="c1">// assert!(client.connect("test_user", "test_password").await.is_ok());</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h2 id="6-hook-security-implementation">6. Hook Security Implementation</h2>

<h3 id="61-secure-hook-execution-environment">6.1 Secure Hook Execution Environment</h3>

<p><strong>Complete Hook Security Manager:</strong></p>
<div class="language-rust highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// hook_security.rs</span>
<span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">process</span><span class="p">::{</span><span class="n">Command</span><span class="p">,</span> <span class="n">Stdio</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">os</span><span class="p">::</span><span class="nn">unix</span><span class="p">::</span><span class="nn">process</span><span class="p">::</span><span class="n">CommandExt</span><span class="p">;</span>
<span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">path</span><span class="p">::{</span><span class="n">Path</span><span class="p">,</span> <span class="n">PathBuf</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="n">fs</span><span class="p">;</span>
<span class="k">use</span> <span class="nn">uuid</span><span class="p">::</span><span class="n">Uuid</span><span class="p">;</span>
<span class="k">use</span> <span class="nn">anyhow</span><span class="p">::{</span><span class="nb">Result</span><span class="p">,</span> <span class="n">Context</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">tracing</span><span class="p">::{</span><span class="n">info</span><span class="p">,</span> <span class="n">warn</span><span class="p">,</span> <span class="n">error</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">serde</span><span class="p">::{</span><span class="n">Serialize</span><span class="p">,</span> <span class="n">Deserialize</span><span class="p">};</span>
<span class="k">use</span> <span class="nn">tempfile</span><span class="p">::</span><span class="n">TempDir</span><span class="p">;</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize,</span> <span class="nd">Deserialize)]</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">HookSecurityConfig</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="n">execution_user</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">execution_group</span><span class="p">:</span> <span class="nb">String</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">sandbox_base_dir</span><span class="p">:</span> <span class="n">PathBuf</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">max_execution_time_seconds</span><span class="p">:</span> <span class="nb">u64</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">max_memory_mb</span><span class="p">:</span> <span class="nb">u64</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">max_file_descriptors</span><span class="p">:</span> <span class="nb">u32</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">max_processes</span><span class="p">:</span> <span class="nb">u32</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">allowed_read_paths</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">PathBuf</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">allowed_write_paths</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">PathBuf</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">blocked_paths</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="n">PathBuf</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">allowed_network</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">allowed_localhost</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">blocked_ports</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="nb">u16</span><span class="o">&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="nb">Default</span> <span class="k">for</span> <span class="n">HookSecurityConfig</span> <span class="p">{</span>
    <span class="k">fn</span> <span class="nf">default</span><span class="p">()</span> <span class="k">-&gt;</span> <span class="k">Self</span> <span class="p">{</span>
        <span class="k">Self</span> <span class="p">{</span>
            <span class="n">execution_user</span><span class="p">:</span> <span class="s">"claude-hook-runner"</span><span class="nf">.to_string</span><span class="p">(),</span>
            <span class="n">execution_group</span><span class="p">:</span> <span class="s">"claude-hooks"</span><span class="nf">.to_string</span><span class="p">(),</span>
            <span class="n">sandbox_base_dir</span><span class="p">:</span> <span class="nn">PathBuf</span><span class="p">::</span><span class="nf">from</span><span class="p">(</span><span class="s">"/tmp/mister-smith-hooks"</span><span class="p">),</span>
            <span class="n">max_execution_time_seconds</span><span class="p">:</span> <span class="mi">30</span><span class="p">,</span>
            <span class="n">max_memory_mb</span><span class="p">:</span> <span class="mi">128</span><span class="p">,</span>
            <span class="n">max_file_descriptors</span><span class="p">:</span> <span class="mi">64</span><span class="p">,</span>
            <span class="n">max_processes</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span>
            <span class="n">allowed_read_paths</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span>
                <span class="nn">PathBuf</span><span class="p">::</span><span class="nf">from</span><span class="p">(</span><span class="s">"/usr"</span><span class="p">),</span>
                <span class="nn">PathBuf</span><span class="p">::</span><span class="nf">from</span><span class="p">(</span><span class="s">"/lib"</span><span class="p">),</span>
                <span class="nn">PathBuf</span><span class="p">::</span><span class="nf">from</span><span class="p">(</span><span class="s">"/bin"</span><span class="p">),</span>
                <span class="nn">PathBuf</span><span class="p">::</span><span class="nf">from</span><span class="p">(</span><span class="s">"/etc/passwd"</span><span class="p">),</span>
                <span class="nn">PathBuf</span><span class="p">::</span><span class="nf">from</span><span class="p">(</span><span class="s">"/etc/group"</span><span class="p">),</span>
            <span class="p">],</span>
            <span class="n">allowed_write_paths</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[],</span>
            <span class="n">blocked_paths</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span>
                <span class="nn">PathBuf</span><span class="p">::</span><span class="nf">from</span><span class="p">(</span><span class="s">"/etc"</span><span class="p">),</span>
                <span class="nn">PathBuf</span><span class="p">::</span><span class="nf">from</span><span class="p">(</span><span class="s">"/root"</span><span class="p">),</span>
                <span class="nn">PathBuf</span><span class="p">::</span><span class="nf">from</span><span class="p">(</span><span class="s">"/home"</span><span class="p">),</span>
                <span class="nn">PathBuf</span><span class="p">::</span><span class="nf">from</span><span class="p">(</span><span class="s">"/var/lib/claude-hooks/.ssh"</span><span class="p">),</span>
                <span class="nn">PathBuf</span><span class="p">::</span><span class="nf">from</span><span class="p">(</span><span class="s">"/proc"</span><span class="p">),</span>
                <span class="nn">PathBuf</span><span class="p">::</span><span class="nf">from</span><span class="p">(</span><span class="s">"/sys"</span><span class="p">),</span>
            <span class="p">],</span>
            <span class="n">allowed_network</span><span class="p">:</span> <span class="k">false</span><span class="p">,</span>
            <span class="n">allowed_localhost</span><span class="p">:</span> <span class="k">true</span><span class="p">,</span>
            <span class="n">blocked_ports</span><span class="p">:</span> <span class="nd">vec!</span><span class="p">[</span><span class="mi">22</span><span class="p">,</span> <span class="mi">3389</span><span class="p">,</span> <span class="mi">5432</span><span class="p">,</span> <span class="mi">27017</span><span class="p">],</span>
        <span class="p">}</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone)]</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">HookExecutionContext</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="n">hook_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">tenant_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">user_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">session_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">sandbox_dir</span><span class="p">:</span> <span class="n">TempDir</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">environment</span><span class="p">:</span> <span class="nn">std</span><span class="p">::</span><span class="nn">collections</span><span class="p">::</span><span class="n">HashMap</span><span class="o">&lt;</span><span class="nb">String</span><span class="p">,</span> <span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">pub</span> <span class="k">struct</span> <span class="n">HookSecurityManager</span> <span class="p">{</span>
    <span class="n">config</span><span class="p">:</span> <span class="n">HookSecurityConfig</span><span class="p">,</span>
    <span class="n">active_executions</span><span class="p">:</span> <span class="nn">std</span><span class="p">::</span><span class="nn">collections</span><span class="p">::</span><span class="n">HashMap</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="p">,</span> <span class="n">HookExecutionContext</span><span class="o">&gt;</span><span class="p">,</span>
<span class="p">}</span>

<span class="k">impl</span> <span class="n">HookSecurityManager</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">new</span><span class="p">(</span><span class="n">config</span><span class="p">:</span> <span class="n">HookSecurityConfig</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="k">Self</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="c1">// Ensure sandbox base directory exists</span>
        <span class="nn">fs</span><span class="p">::</span><span class="nf">create_dir_all</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="py">.sandbox_base_dir</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to create sandbox base directory"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Verify execution user exists</span>
        <span class="k">Self</span><span class="p">::</span><span class="nf">verify_execution_user</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="py">.execution_user</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="nf">Ok</span><span class="p">(</span><span class="k">Self</span> <span class="p">{</span>
            <span class="n">config</span><span class="p">,</span>
            <span class="n">active_executions</span><span class="p">:</span> <span class="nn">std</span><span class="p">::</span><span class="nn">collections</span><span class="p">::</span><span class="nn">HashMap</span><span class="p">::</span><span class="nf">new</span><span class="p">(),</span>
        <span class="p">})</span>
    <span class="p">}</span>

    <span class="cd">/// Verify that the execution user exists and is properly configured</span>
    <span class="k">fn</span> <span class="nf">verify_execution_user</span><span class="p">(</span><span class="n">username</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">output</span> <span class="o">=</span> <span class="nn">Command</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="s">"id"</span><span class="p">)</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="n">username</span><span class="p">)</span>
            <span class="nf">.output</span><span class="p">()</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"Failed to check user: {}"</span><span class="p">,</span> <span class="n">username</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>

        <span class="k">if</span> <span class="o">!</span><span class="n">output</span><span class="py">.status</span><span class="nf">.success</span><span class="p">()</span> <span class="p">{</span>
            <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span><span class="s">"Execution user '{}' does not exist"</span><span class="p">,</span> <span class="n">username</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Verified execution user: {}"</span><span class="p">,</span> <span class="n">username</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>

    <span class="cd">/// Validate hook script before execution</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">validate_hook_script</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">script_path</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Path</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="c1">// Check if file exists</span>
        <span class="k">if</span> <span class="o">!</span><span class="n">script_path</span><span class="nf">.exists</span><span class="p">()</span> <span class="p">{</span>
            <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span><span class="s">"Hook script does not exist: {:?}"</span><span class="p">,</span> <span class="n">script_path</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="c1">// Check file permissions</span>
        <span class="k">let</span> <span class="n">metadata</span> <span class="o">=</span> <span class="nn">fs</span><span class="p">::</span><span class="nf">metadata</span><span class="p">(</span><span class="n">script_path</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to read script metadata"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Check if world-writable</span>
        <span class="nd">#[cfg(unix)]</span>
        <span class="p">{</span>
            <span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">os</span><span class="p">::</span><span class="nn">unix</span><span class="p">::</span><span class="nn">fs</span><span class="p">::</span><span class="n">PermissionsExt</span><span class="p">;</span>
            <span class="k">let</span> <span class="n">permissions</span> <span class="o">=</span> <span class="n">metadata</span><span class="nf">.permissions</span><span class="p">();</span>
            <span class="k">if</span> <span class="n">permissions</span><span class="nf">.mode</span><span class="p">()</span> <span class="o">&amp;</span> <span class="mi">0o002</span> <span class="o">!=</span> <span class="mi">0</span> <span class="p">{</span>
                <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span><span class="s">"Hook script cannot be world-writable: {:?}"</span><span class="p">,</span> <span class="n">script_path</span><span class="p">);</span>
            <span class="p">}</span>
        <span class="p">}</span>

        <span class="c1">// Read and validate script content</span>
        <span class="k">let</span> <span class="n">content</span> <span class="o">=</span> <span class="nn">fs</span><span class="p">::</span><span class="nf">read_to_string</span><span class="p">(</span><span class="n">script_path</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to read script content"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="k">self</span><span class="nf">.validate_script_content</span><span class="p">(</span><span class="o">&amp;</span><span class="n">content</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Hook script validation passed: {:?}"</span><span class="p">,</span> <span class="n">script_path</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>

    <span class="cd">/// Validate script content for dangerous patterns</span>
    <span class="k">fn</span> <span class="nf">validate_script_content</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">,</span> <span class="n">content</span><span class="p">:</span> <span class="o">&amp;</span><span class="nb">str</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">dangerous_patterns</span> <span class="o">=</span> <span class="p">[</span>
            <span class="s">r"sudo\s"</span><span class="p">,</span>
            <span class="s">r"su\s+-"</span><span class="p">,</span>
            <span class="s">r"chmod\s+777"</span><span class="p">,</span>
            <span class="s">r"rm\s+-rf\s+/"</span><span class="p">,</span>
            <span class="s">r"curl.*\|.*sh"</span><span class="p">,</span>
            <span class="s">r"wget.*\|.*sh"</span><span class="p">,</span>
            <span class="s">r"eval\s*\("</span><span class="p">,</span>
            <span class="s">r"/etc/passwd"</span><span class="p">,</span>
            <span class="s">r"/etc/shadow"</span><span class="p">,</span>
            <span class="s">r"nc\s+-l"</span><span class="p">,</span>
            <span class="s">r"netcat\s+-l"</span><span class="p">,</span>
            <span class="s">r"python.*-c.*exec"</span><span class="p">,</span>
            <span class="s">r"perl.*-e"</span><span class="p">,</span>
            <span class="s">r"\$\(.*\)"</span><span class="p">,</span>  <span class="c1">// Command substitution</span>
        <span class="p">];</span>

        <span class="k">for</span> <span class="n">pattern</span> <span class="k">in</span> <span class="o">&amp;</span><span class="n">dangerous_patterns</span> <span class="p">{</span>
            <span class="k">let</span> <span class="n">regex</span> <span class="o">=</span> <span class="nn">regex</span><span class="p">::</span><span class="nn">Regex</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">pattern</span><span class="p">)</span>
                <span class="nf">.with_context</span><span class="p">(||</span> <span class="nd">format!</span><span class="p">(</span><span class="s">"Invalid regex pattern: {}"</span><span class="p">,</span> <span class="n">pattern</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>
            
            <span class="k">if</span> <span class="n">regex</span><span class="nf">.is_match</span><span class="p">(</span><span class="n">content</span><span class="p">)</span> <span class="p">{</span>
                <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span><span class="s">"Hook script contains dangerous pattern: {}"</span><span class="p">,</span> <span class="n">pattern</span><span class="p">);</span>
            <span class="p">}</span>
        <span class="p">}</span>

        <span class="c1">// Validate shebang</span>
        <span class="k">if</span> <span class="o">!</span><span class="n">content</span><span class="nf">.starts_with</span><span class="p">(</span><span class="s">"#!"</span><span class="p">)</span> <span class="p">{</span>
            <span class="nn">anyhow</span><span class="p">::</span><span class="nd">bail!</span><span class="p">(</span><span class="s">"Hook script must have valid shebang"</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>

    <span class="cd">/// Create secure execution context</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">create_execution_context</span><span class="p">(</span>
        <span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span>
        <span class="n">tenant_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
        <span class="n">user_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
        <span class="n">session_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
    <span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">Uuid</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">hook_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>
        
        <span class="c1">// Create isolated sandbox directory</span>
        <span class="k">let</span> <span class="n">sandbox_dir</span> <span class="o">=</span> <span class="nn">TempDir</span><span class="p">::</span><span class="nf">new_in</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="py">.config.sandbox_base_dir</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to create sandbox directory"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Set up environment variables (filtered for security)</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">environment</span> <span class="o">=</span> <span class="nn">std</span><span class="p">::</span><span class="nn">collections</span><span class="p">::</span><span class="nn">HashMap</span><span class="p">::</span><span class="nf">new</span><span class="p">();</span>
        <span class="n">environment</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"PATH"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="s">"/usr/bin:/bin"</span><span class="nf">.to_string</span><span class="p">());</span>
        <span class="n">environment</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"HOME"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="n">sandbox_dir</span><span class="nf">.path</span><span class="p">()</span><span class="nf">.to_string_lossy</span><span class="p">()</span><span class="nf">.to_string</span><span class="p">());</span>
        <span class="n">environment</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"USER"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="k">self</span><span class="py">.config.execution_user</span><span class="nf">.clone</span><span class="p">());</span>
        <span class="n">environment</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"SHELL"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="s">"/bin/bash"</span><span class="nf">.to_string</span><span class="p">());</span>
        <span class="n">environment</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"TMPDIR"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="n">sandbox_dir</span><span class="nf">.path</span><span class="p">()</span><span class="nf">.to_string_lossy</span><span class="p">()</span><span class="nf">.to_string</span><span class="p">());</span>
        
        <span class="c1">// Add hook-specific variables</span>
        <span class="n">environment</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"HOOK_ID"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="n">hook_id</span><span class="nf">.to_string</span><span class="p">());</span>
        <span class="n">environment</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"TENANT_ID"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="n">tenant_id</span><span class="nf">.to_string</span><span class="p">());</span>
        <span class="n">environment</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"USER_ID"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="n">user_id</span><span class="nf">.to_string</span><span class="p">());</span>
        <span class="n">environment</span><span class="nf">.insert</span><span class="p">(</span><span class="s">"SESSION_ID"</span><span class="nf">.to_string</span><span class="p">(),</span> <span class="n">session_id</span><span class="nf">.to_string</span><span class="p">());</span>

        <span class="k">let</span> <span class="n">context</span> <span class="o">=</span> <span class="n">HookExecutionContext</span> <span class="p">{</span>
            <span class="n">hook_id</span><span class="p">,</span>
            <span class="n">tenant_id</span><span class="p">,</span>
            <span class="n">user_id</span><span class="p">,</span>
            <span class="n">session_id</span><span class="p">,</span>
            <span class="n">sandbox_dir</span><span class="p">,</span>
            <span class="n">environment</span><span class="p">,</span>
        <span class="p">};</span>

        <span class="k">self</span><span class="py">.active_executions</span><span class="nf">.insert</span><span class="p">(</span><span class="n">hook_id</span><span class="p">,</span> <span class="n">context</span><span class="p">);</span>
        <span class="nd">info!</span><span class="p">(</span><span class="s">"Created execution context: {}"</span><span class="p">,</span> <span class="n">hook_id</span><span class="p">);</span>
        <span class="nf">Ok</span><span class="p">(</span><span class="n">hook_id</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Execute hook script with security constraints</span>
    <span class="k">pub</span> <span class="k">async</span> <span class="k">fn</span> <span class="nf">execute_hook</span><span class="p">(</span>
        <span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span>
        <span class="n">hook_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
        <span class="n">script_path</span><span class="p">:</span> <span class="o">&amp;</span><span class="n">Path</span><span class="p">,</span>
        <span class="n">args</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="nb">String</span><span class="o">&gt;</span><span class="p">,</span>
        <span class="n">input_data</span><span class="p">:</span> <span class="nb">Option</span><span class="o">&lt;</span><span class="nb">Vec</span><span class="o">&lt;</span><span class="nb">u8</span><span class="o">&gt;&gt;</span><span class="p">,</span>
    <span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="n">HookExecutionResult</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">context</span> <span class="o">=</span> <span class="k">self</span><span class="py">.active_executions</span><span class="nf">.get</span><span class="p">(</span><span class="o">&amp;</span><span class="n">hook_id</span><span class="p">)</span>
            <span class="nf">.ok_or_else</span><span class="p">(||</span> <span class="nn">anyhow</span><span class="p">::</span><span class="nd">anyhow!</span><span class="p">(</span><span class="s">"Invalid hook execution context: {}"</span><span class="p">,</span> <span class="n">hook_id</span><span class="p">))</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Validate script before execution</span>
        <span class="k">self</span><span class="nf">.validate_hook_script</span><span class="p">(</span><span class="n">script_path</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Copy script to sandbox</span>
        <span class="k">let</span> <span class="n">sandbox_script</span> <span class="o">=</span> <span class="n">context</span><span class="py">.sandbox_dir</span><span class="nf">.path</span><span class="p">()</span><span class="nf">.join</span><span class="p">(</span><span class="s">"hook_script"</span><span class="p">);</span>
        <span class="nn">fs</span><span class="p">::</span><span class="nf">copy</span><span class="p">(</span><span class="n">script_path</span><span class="p">,</span> <span class="o">&amp;</span><span class="n">sandbox_script</span><span class="p">)</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to copy script to sandbox"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Make script executable</span>
        <span class="nd">#[cfg(unix)]</span>
        <span class="p">{</span>
            <span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">os</span><span class="p">::</span><span class="nn">unix</span><span class="p">::</span><span class="nn">fs</span><span class="p">::</span><span class="n">PermissionsExt</span><span class="p">;</span>
            <span class="k">let</span> <span class="k">mut</span> <span class="n">permissions</span> <span class="o">=</span> <span class="nn">fs</span><span class="p">::</span><span class="nf">metadata</span><span class="p">(</span><span class="o">&amp;</span><span class="n">sandbox_script</span><span class="p">)</span><span class="o">?</span><span class="nf">.permissions</span><span class="p">();</span>
            <span class="n">permissions</span><span class="nf">.set_mode</span><span class="p">(</span><span class="mi">0o750</span><span class="p">);</span> <span class="c1">// rwxr-x---</span>
            <span class="nn">fs</span><span class="p">::</span><span class="nf">set_permissions</span><span class="p">(</span><span class="o">&amp;</span><span class="n">sandbox_script</span><span class="p">,</span> <span class="n">permissions</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
        <span class="p">}</span>

        <span class="c1">// Prepare command with security constraints</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">command</span> <span class="o">=</span> <span class="nn">Command</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="s">"timeout"</span><span class="p">);</span>
        <span class="n">command</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="nd">format!</span><span class="p">(</span><span class="s">"{}s"</span><span class="p">,</span> <span class="k">self</span><span class="py">.config.max_execution_time_seconds</span><span class="p">))</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="s">"systemd-run"</span><span class="p">)</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="s">"--user"</span><span class="p">)</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="s">"--scope"</span><span class="p">)</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="s">"--slice=claude-hooks.slice"</span><span class="p">)</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="nd">format!</span><span class="p">(</span><span class="s">"--property=MemoryMax={}M"</span><span class="p">,</span> <span class="k">self</span><span class="py">.config.max_memory_mb</span><span class="p">))</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="nd">format!</span><span class="p">(</span><span class="s">"--property=TasksMax={}"</span><span class="p">,</span> <span class="k">self</span><span class="py">.config.max_processes</span><span class="p">))</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="s">"--property=PrivateNetwork=true"</span><span class="p">)</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="s">"--property=NoNewPrivileges=true"</span><span class="p">)</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="s">"--property=ProtectSystem=strict"</span><span class="p">)</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="s">"--property=ProtectHome=true"</span><span class="p">)</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="s">"--property=PrivateTmp=true"</span><span class="p">)</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="s">"--setenv=PATH=/usr/bin:/bin"</span><span class="p">)</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="nd">format!</span><span class="p">(</span><span class="s">"--setenv=HOME={}"</span><span class="p">,</span> <span class="n">context</span><span class="py">.sandbox_dir</span><span class="nf">.path</span><span class="p">()</span><span class="nf">.display</span><span class="p">()))</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="nd">format!</span><span class="p">(</span><span class="s">"--setenv=USER={}"</span><span class="p">,</span> <span class="k">self</span><span class="py">.config.execution_user</span><span class="p">))</span>
            <span class="nf">.arg</span><span class="p">(</span><span class="o">&amp;</span><span class="n">sandbox_script</span><span class="p">)</span>
            <span class="nf">.args</span><span class="p">(</span><span class="o">&amp;</span><span class="n">args</span><span class="p">)</span>
            <span class="nf">.current_dir</span><span class="p">(</span><span class="n">context</span><span class="py">.sandbox_dir</span><span class="nf">.path</span><span class="p">())</span>
            <span class="nf">.stdin</span><span class="p">(</span><span class="nn">Stdio</span><span class="p">::</span><span class="nf">piped</span><span class="p">())</span>
            <span class="nf">.stdout</span><span class="p">(</span><span class="nn">Stdio</span><span class="p">::</span><span class="nf">piped</span><span class="p">())</span>
            <span class="nf">.stderr</span><span class="p">(</span><span class="nn">Stdio</span><span class="p">::</span><span class="nf">piped</span><span class="p">());</span>

        <span class="c1">// Add environment variables</span>
        <span class="k">for</span> <span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span> <span class="k">in</span> <span class="o">&amp;</span><span class="n">context</span><span class="py">.environment</span> <span class="p">{</span>
            <span class="n">command</span><span class="nf">.env</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">);</span>
        <span class="p">}</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Executing hook {} with security constraints"</span><span class="p">,</span> <span class="n">hook_id</span><span class="p">);</span>
        
        <span class="k">let</span> <span class="n">start_time</span> <span class="o">=</span> <span class="nn">std</span><span class="p">::</span><span class="nn">time</span><span class="p">::</span><span class="nn">Instant</span><span class="p">::</span><span class="nf">now</span><span class="p">();</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">child</span> <span class="o">=</span> <span class="n">command</span><span class="nf">.spawn</span><span class="p">()</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to spawn hook process"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="c1">// Send input data if provided</span>
        <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">input</span><span class="p">)</span> <span class="o">=</span> <span class="n">input_data</span> <span class="p">{</span>
            <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">stdin</span><span class="p">)</span> <span class="o">=</span> <span class="n">child</span><span class="py">.stdin</span><span class="nf">.take</span><span class="p">()</span> <span class="p">{</span>
                <span class="k">use</span> <span class="nn">tokio</span><span class="p">::</span><span class="nn">io</span><span class="p">::</span><span class="n">AsyncWriteExt</span><span class="p">;</span>
                <span class="k">let</span> <span class="k">mut</span> <span class="n">stdin</span> <span class="o">=</span> <span class="nn">tokio</span><span class="p">::</span><span class="nn">process</span><span class="p">::</span><span class="nn">ChildStdin</span><span class="p">::</span><span class="nf">from_std</span><span class="p">(</span><span class="n">stdin</span><span class="p">)</span>
                    <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to convert stdin"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
                <span class="n">stdin</span><span class="nf">.write_all</span><span class="p">(</span><span class="o">&amp;</span><span class="n">input</span><span class="p">)</span><span class="k">.await</span>
                    <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to write input data"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
                <span class="n">stdin</span><span class="nf">.shutdown</span><span class="p">()</span><span class="k">.await</span>
                    <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to close stdin"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>
            <span class="p">}</span>
        <span class="p">}</span>

        <span class="c1">// Wait for completion with timeout</span>
        <span class="k">let</span> <span class="n">output</span> <span class="o">=</span> <span class="nn">tokio</span><span class="p">::</span><span class="nn">time</span><span class="p">::</span><span class="nf">timeout</span><span class="p">(</span>
            <span class="nn">std</span><span class="p">::</span><span class="nn">time</span><span class="p">::</span><span class="nn">Duration</span><span class="p">::</span><span class="nf">from_secs</span><span class="p">(</span><span class="k">self</span><span class="py">.config.max_execution_time_seconds</span> <span class="o">+</span> <span class="mi">5</span><span class="p">),</span>
            <span class="n">child</span><span class="nf">.wait_with_output</span><span class="p">()</span>
        <span class="p">)</span><span class="k">.await</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Hook execution timed out"</span><span class="p">)</span><span class="o">?</span>
            <span class="nf">.with_context</span><span class="p">(||</span> <span class="s">"Failed to get hook output"</span><span class="p">)</span><span class="o">?</span><span class="p">;</span>

        <span class="k">let</span> <span class="n">execution_time</span> <span class="o">=</span> <span class="n">start_time</span><span class="nf">.elapsed</span><span class="p">();</span>

        <span class="k">let</span> <span class="n">result</span> <span class="o">=</span> <span class="n">HookExecutionResult</span> <span class="p">{</span>
            <span class="n">hook_id</span><span class="p">,</span>
            <span class="n">exit_code</span><span class="p">:</span> <span class="n">output</span><span class="py">.status</span><span class="nf">.code</span><span class="p">()</span><span class="nf">.unwrap_or</span><span class="p">(</span><span class="o">-</span><span class="mi">1</span><span class="p">),</span>
            <span class="n">stdout</span><span class="p">:</span> <span class="n">output</span><span class="py">.stdout</span><span class="p">,</span>
            <span class="n">stderr</span><span class="p">:</span> <span class="n">output</span><span class="py">.stderr</span><span class="p">,</span>
            <span class="n">execution_time_ms</span><span class="p">:</span> <span class="n">execution_time</span><span class="nf">.as_millis</span><span class="p">()</span> <span class="k">as</span> <span class="nb">u64</span><span class="p">,</span>
            <span class="n">success</span><span class="p">:</span> <span class="n">output</span><span class="py">.status</span><span class="nf">.success</span><span class="p">(),</span>
        <span class="p">};</span>

        <span class="nd">info!</span><span class="p">(</span><span class="s">"Hook execution completed: {} (exit code: {})"</span><span class="p">,</span> 
            <span class="n">hook_id</span><span class="p">,</span> <span class="n">result</span><span class="py">.exit_code</span><span class="p">);</span>

        <span class="nf">Ok</span><span class="p">(</span><span class="n">result</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="cd">/// Clean up execution context</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">cleanup_execution_context</span><span class="p">(</span><span class="o">&amp;</span><span class="k">mut</span> <span class="k">self</span><span class="p">,</span> <span class="n">hook_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">Result</span><span class="o">&lt;</span><span class="p">()</span><span class="o">&gt;</span> <span class="p">{</span>
        <span class="k">if</span> <span class="k">let</span> <span class="nf">Some</span><span class="p">(</span><span class="n">context</span><span class="p">)</span> <span class="o">=</span> <span class="k">self</span><span class="py">.active_executions</span><span class="nf">.remove</span><span class="p">(</span><span class="o">&amp;</span><span class="n">hook_id</span><span class="p">)</span> <span class="p">{</span>
            <span class="c1">// Sandbox directory will be automatically cleaned up when TempDir is dropped</span>
            <span class="nd">info!</span><span class="p">(</span><span class="s">"Cleaned up execution context: {}"</span><span class="p">,</span> <span class="n">hook_id</span><span class="p">);</span>
        <span class="p">}</span>
        <span class="nf">Ok</span><span class="p">(())</span>
    <span class="p">}</span>

    <span class="cd">/// Get active execution count</span>
    <span class="k">pub</span> <span class="k">fn</span> <span class="nf">active_execution_count</span><span class="p">(</span><span class="o">&amp;</span><span class="k">self</span><span class="p">)</span> <span class="k">-&gt;</span> <span class="nb">usize</span> <span class="p">{</span>
        <span class="k">self</span><span class="py">.active_executions</span><span class="nf">.len</span><span class="p">()</span>
    <span class="p">}</span>
<span class="p">}</span>

<span class="nd">#[derive(Debug,</span> <span class="nd">Clone,</span> <span class="nd">Serialize)]</span>
<span class="k">pub</span> <span class="k">struct</span> <span class="n">HookExecutionResult</span> <span class="p">{</span>
    <span class="k">pub</span> <span class="n">hook_id</span><span class="p">:</span> <span class="n">Uuid</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">exit_code</span><span class="p">:</span> <span class="nb">i32</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">stdout</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="nb">u8</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">stderr</span><span class="p">:</span> <span class="nb">Vec</span><span class="o">&lt;</span><span class="nb">u8</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">execution_time_ms</span><span class="p">:</span> <span class="nb">u64</span><span class="p">,</span>
    <span class="k">pub</span> <span class="n">success</span><span class="p">:</span> <span class="nb">bool</span><span class="p">,</span>
<span class="p">}</span>

<span class="nd">#[cfg(test)]</span>
<span class="k">mod</span> <span class="n">tests</span> <span class="p">{</span>
    <span class="k">use</span> <span class="k">super</span><span class="p">::</span><span class="o">*</span><span class="p">;</span>
    <span class="k">use</span> <span class="nn">tempfile</span><span class="p">::</span><span class="n">NamedTempFile</span><span class="p">;</span>
    <span class="k">use</span> <span class="nn">std</span><span class="p">::</span><span class="nn">io</span><span class="p">::</span><span class="n">Write</span><span class="p">;</span>

    <span class="nd">#[test]</span>
    <span class="k">fn</span> <span class="nf">test_script_validation</span><span class="p">()</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">config</span> <span class="o">=</span> <span class="nn">HookSecurityConfig</span><span class="p">::</span><span class="nf">default</span><span class="p">();</span>
        <span class="k">let</span> <span class="n">manager</span> <span class="o">=</span> <span class="nn">HookSecurityManager</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">config</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">();</span>

        <span class="c1">// Test valid script</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">valid_script</span> <span class="o">=</span> <span class="nn">NamedTempFile</span><span class="p">::</span><span class="nf">new</span><span class="p">()</span><span class="nf">.unwrap</span><span class="p">();</span>
        <span class="nd">writeln!</span><span class="p">(</span><span class="n">valid_script</span><span class="p">,</span> <span class="s">"#!/bin/bash</span><span class="se">\n</span><span class="s">echo 'Hello World'"</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">();</span>
        <span class="nd">assert!</span><span class="p">(</span><span class="n">manager</span><span class="nf">.validate_hook_script</span><span class="p">(</span><span class="n">valid_script</span><span class="nf">.path</span><span class="p">())</span><span class="nf">.is_ok</span><span class="p">());</span>

        <span class="c1">// Test dangerous script</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">dangerous_script</span> <span class="o">=</span> <span class="nn">NamedTempFile</span><span class="p">::</span><span class="nf">new</span><span class="p">()</span><span class="nf">.unwrap</span><span class="p">();</span>
        <span class="nd">writeln!</span><span class="p">(</span><span class="n">dangerous_script</span><span class="p">,</span> <span class="s">"#!/bin/bash</span><span class="se">\n</span><span class="s">sudo rm -rf /"</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">();</span>
        <span class="nd">assert!</span><span class="p">(</span><span class="n">manager</span><span class="nf">.validate_hook_script</span><span class="p">(</span><span class="n">dangerous_script</span><span class="nf">.path</span><span class="p">())</span><span class="nf">.is_err</span><span class="p">());</span>
    <span class="p">}</span>

    <span class="nd">#[tokio::test]</span>
    <span class="k">async</span> <span class="k">fn</span> <span class="nf">test_execution_context</span><span class="p">()</span> <span class="p">{</span>
        <span class="k">let</span> <span class="n">config</span> <span class="o">=</span> <span class="nn">HookSecurityConfig</span><span class="p">::</span><span class="nf">default</span><span class="p">();</span>
        <span class="k">let</span> <span class="k">mut</span> <span class="n">manager</span> <span class="o">=</span> <span class="nn">HookSecurityManager</span><span class="p">::</span><span class="nf">new</span><span class="p">(</span><span class="n">config</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">();</span>

        <span class="k">let</span> <span class="n">tenant_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>
        <span class="k">let</span> <span class="n">user_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>
        <span class="k">let</span> <span class="n">session_id</span> <span class="o">=</span> <span class="nn">Uuid</span><span class="p">::</span><span class="nf">new_v4</span><span class="p">();</span>

        <span class="k">let</span> <span class="n">hook_id</span> <span class="o">=</span> <span class="n">manager</span><span class="nf">.create_execution_context</span><span class="p">(</span><span class="n">tenant_id</span><span class="p">,</span> <span class="n">user_id</span><span class="p">,</span> <span class="n">session_id</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">();</span>
        <span class="nd">assert!</span><span class="p">(</span><span class="n">manager</span><span class="py">.active_executions</span><span class="nf">.contains_key</span><span class="p">(</span><span class="o">&amp;</span><span class="n">hook_id</span><span class="p">));</span>

        <span class="n">manager</span><span class="nf">.cleanup_execution_context</span><span class="p">(</span><span class="n">hook_id</span><span class="p">)</span><span class="nf">.unwrap</span><span class="p">();</span>
        <span class="nd">assert!</span><span class="p">(</span><span class="o">!</span><span class="n">manager</span><span class="py">.active_executions</span><span class="nf">.contains_key</span><span class="p">(</span><span class="o">&amp;</span><span class="n">hook_id</span><span class="p">));</span>
    <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h3 id="62-hook-security-configuration">6.2 Hook Security Configuration</h3>

<p><strong>Complete Security Configuration:</strong></p>
<div class="language-yaml highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># hook_security_config.yml</span>
<span class="na">hook_security</span><span class="pi">:</span>
  <span class="c1"># User and group for hook execution</span>
  <span class="na">execution_user</span><span class="pi">:</span> <span class="s">claude-hook-runner</span>
  <span class="na">execution_group</span><span class="pi">:</span> <span class="s">claude-hooks</span>
  
  <span class="c1"># Sandbox configuration</span>
  <span class="na">sandbox_base_dir</span><span class="pi">:</span> <span class="s">/tmp/mister-smith-hooks</span>
  <span class="na">cleanup_interval_minutes</span><span class="pi">:</span> <span class="m">60</span>
  <span class="na">max_concurrent_executions</span><span class="pi">:</span> <span class="m">10</span>
  
  <span class="c1"># Resource limits</span>
  <span class="na">resource_limits</span><span class="pi">:</span>
    <span class="na">max_execution_time_seconds</span><span class="pi">:</span> <span class="m">30</span>
    <span class="na">max_memory_mb</span><span class="pi">:</span> <span class="m">128</span>
    <span class="na">max_cpu_percent</span><span class="pi">:</span> <span class="m">50</span>
    <span class="na">max_file_descriptors</span><span class="pi">:</span> <span class="m">64</span>
    <span class="na">max_processes</span><span class="pi">:</span> <span class="m">1</span>
    <span class="na">max_disk_usage_mb</span><span class="pi">:</span> <span class="m">100</span>
  
  <span class="c1"># Filesystem access control</span>
  <span class="na">filesystem_access</span><span class="pi">:</span>
    <span class="na">allowed_read_paths</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">/usr</span>
      <span class="pi">-</span> <span class="s">/lib</span>
      <span class="pi">-</span> <span class="s">/lib64</span>
      <span class="pi">-</span> <span class="s">/bin</span>
      <span class="pi">-</span> <span class="s">/etc/passwd</span>
      <span class="pi">-</span> <span class="s">/etc/group</span>
      <span class="pi">-</span> <span class="s">/etc/hosts</span>
      <span class="pi">-</span> <span class="s">/etc/resolv.conf</span>
    
    <span class="na">allowed_write_paths</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">/tmp/mister-smith-hooks</span>
    
    <span class="na">blocked_paths</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">/etc</span>
      <span class="pi">-</span> <span class="s">/root</span>
      <span class="pi">-</span> <span class="s">/home</span>
      <span class="pi">-</span> <span class="s">/var/lib/claude-hooks/.ssh</span>
      <span class="pi">-</span> <span class="s">/proc</span>
      <span class="pi">-</span> <span class="s">/sys</span>
      <span class="pi">-</span> <span class="s">/dev</span>
      <span class="pi">-</span> <span class="s">/boot</span>
  
  <span class="c1"># Network restrictions</span>
  <span class="na">network_access</span><span class="pi">:</span>
    <span class="na">allow_outbound</span><span class="pi">:</span> <span class="kc">false</span>
    <span class="na">allow_localhost</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">blocked_ports</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="m">22</span>    <span class="c1"># SSH</span>
      <span class="pi">-</span> <span class="m">23</span>    <span class="c1"># Telnet</span>
      <span class="pi">-</span> <span class="m">3389</span>  <span class="c1"># RDP</span>
      <span class="pi">-</span> <span class="m">5432</span>  <span class="c1"># PostgreSQL</span>
      <span class="pi">-</span> <span class="m">3306</span>  <span class="c1"># MySQL</span>
      <span class="pi">-</span> <span class="m">27017</span> <span class="c1"># MongoDB</span>
      <span class="pi">-</span> <span class="m">6379</span>  <span class="c1"># Redis</span>
      <span class="pi">-</span> <span class="m">9200</span>  <span class="c1"># Elasticsearch</span>
    
    <span class="na">allowed_destinations</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">127.0.0.1</span>
      <span class="pi">-</span> <span class="s">::1</span>
  
  <span class="c1"># Script validation</span>
  <span class="na">script_validation</span><span class="pi">:</span>
    <span class="na">enforce_shebang</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">max_script_size_kb</span><span class="pi">:</span> <span class="m">1024</span>
    
    <span class="na">dangerous_patterns</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">sudo</span><span class="se">\\</span><span class="s">s"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">su</span><span class="se">\\</span><span class="s">s+-"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">chmod</span><span class="se">\\</span><span class="s">s+777"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">rm</span><span class="se">\\</span><span class="s">s+-rf</span><span class="se">\\</span><span class="s">s+/"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">curl.*</span><span class="se">\\</span><span class="s">|.*sh"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">wget.*</span><span class="se">\\</span><span class="s">|.*sh"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">eval</span><span class="se">\\</span><span class="s">s*</span><span class="se">\\</span><span class="s">("</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">/etc/passwd"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">/etc/shadow"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">nc</span><span class="se">\\</span><span class="s">s+-l"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">netcat</span><span class="se">\\</span><span class="s">s+-l"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">python.*-c.*exec"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="s">perl.*-e"</span>
      <span class="pi">-</span> <span class="s2">"</span><span class="se">\\</span><span class="s">$</span><span class="se">\\</span><span class="s">(.*</span><span class="se">\\</span><span class="s">)"</span>
    
    <span class="na">allowed_interpreters</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">/bin/bash</span>
      <span class="pi">-</span> <span class="s">/bin/sh</span>
      <span class="pi">-</span> <span class="s">/usr/bin/python3</span>
      <span class="pi">-</span> <span class="s">/usr/bin/node</span>
    
  <span class="c1"># Environment variables</span>
  <span class="na">environment</span><span class="pi">:</span>
    <span class="c1"># Variables always set</span>
    <span class="na">default_vars</span><span class="pi">:</span>
      <span class="na">PATH</span><span class="pi">:</span> <span class="s2">"</span><span class="s">/usr/bin:/bin"</span>
      <span class="na">SHELL</span><span class="pi">:</span> <span class="s2">"</span><span class="s">/bin/bash"</span>
      <span class="na">TMPDIR</span><span class="pi">:</span> <span class="s2">"</span><span class="s">${SANDBOX_DIR}"</span>
      <span class="na">HOME</span><span class="pi">:</span> <span class="s2">"</span><span class="s">${SANDBOX_DIR}"</span>
      <span class="na">USER</span><span class="pi">:</span> <span class="s2">"</span><span class="s">${EXECUTION_USER}"</span>
    
    <span class="c1"># Variables from user context</span>
    <span class="na">context_vars</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">HOOK_ID</span>
      <span class="pi">-</span> <span class="s">TENANT_ID</span>
      <span class="pi">-</span> <span class="s">USER_ID</span>
      <span class="pi">-</span> <span class="s">SESSION_ID</span>
    
    <span class="c1"># Blocked environment variables</span>
    <span class="na">blocked_vars</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">LD_PRELOAD</span>
      <span class="pi">-</span> <span class="s">LD_LIBRARY_PATH</span>
      <span class="pi">-</span> <span class="s">SSH_AUTH_SOCK</span>
      <span class="pi">-</span> <span class="s">DISPLAY</span>
      <span class="pi">-</span> <span class="s">XAUTHORITY</span>
  
  <span class="c1"># Monitoring and logging</span>
  <span class="na">monitoring</span><span class="pi">:</span>
    <span class="na">log_all_executions</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">log_stdout_stderr</span><span class="pi">:</span> <span class="kc">true</span>
    <span class="na">max_log_size_mb</span><span class="pi">:</span> <span class="m">10</span>
    
    <span class="c1"># Metrics to collect</span>
    <span class="na">metrics</span><span class="pi">:</span>
      <span class="pi">-</span> <span class="s">execution_count</span>
      <span class="pi">-</span> <span class="s">execution_duration</span>
      <span class="pi">-</span> <span class="s">memory_usage</span>
      <span class="pi">-</span> <span class="s">cpu_usage</span>
      <span class="pi">-</span> <span class="s">exit_codes</span>
      <span class="pi">-</span> <span class="s">validation_failures</span>
    
    <span class="c1"># Alerts</span>
    <span class="na">alerts</span><span class="pi">:</span>
      <span class="na">max_failures_per_hour</span><span class="pi">:</span> <span class="m">10</span>
      <span class="na">max_execution_time_violations</span><span class="pi">:</span> <span class="m">5</span>
      <span class="na">suspicious_patterns_detected</span><span class="pi">:</span> <span class="m">1</span>
  
  <span class="c1"># Systemd integration</span>
  <span class="na">systemd</span><span class="pi">:</span>
    <span class="na">slice_name</span><span class="pi">:</span> <span class="s">claude-hooks.slice</span>
    <span class="na">service_properties</span><span class="pi">:</span>
      <span class="na">MemoryAccounting</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">CPUAccounting</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">TasksAccounting</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">IOAccounting</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">PrivateNetwork</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">NoNewPrivileges</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">ProtectSystem</span><span class="pi">:</span> <span class="s">strict</span>
      <span class="na">ProtectHome</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">PrivateTmp</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">ProtectKernelTunables</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">ProtectKernelModules</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">ProtectControlGroups</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">RestrictSUIDSGID</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">RemoveIPC</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">RestrictRealtime</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">RestrictNamespaces</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">LockPersonality</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">ProtectHostname</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">ProtectClock</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">ProtectKernelLogs</span><span class="pi">:</span> <span class="kc">true</span>
      <span class="na">ProtectProc</span><span class="pi">:</span> <span class="s">invisible</span>
      <span class="na">ProcSubset</span><span class="pi">:</span> <span class="s">pid</span>
</code></pre></div></div>

<hr />

<p>This document provides foundational security patterns for agent implementation. For complete system architecture and advanced patterns, refer to the canonical tech-framework.md.</p>

var store = [{
        "title": "Core Architecture",
        "excerpt":"Core Architecture   System architecture and module organization documentation.  ","categories": [],
        "tags": [],
        "url": "/<PERSON><PERSON><PERSON>/core_architecture/",
        "teaser": null
      },{
        "title": "Data Management",
        "excerpt":"Data Management   Agent orchestration and data persistence documentation.  ","categories": [],
        "tags": [],
        "url": "/<PERSON><PERSON>/data_management/",
        "teaser": null
      },{
        "title": "Operations",
        "excerpt":"Operations   Observability, monitoring, and deployment documentation.  ","categories": [],
        "tags": [],
        "url": "/<PERSON><PERSON><PERSON>/operations/",
        "teaser": null
      },{
        "title": "Research",
        "excerpt":"Research   Claude CLI integration and analysis documentation.  ","categories": [],
        "tags": [],
        "url": "/<PERSON><PERSON><PERSON>/research/",
        "teaser": null
      },{
        "title": "Security",
        "excerpt":"Security   Security framework and patterns documentation.  ","categories": [],
        "tags": [],
        "url": "/<PERSON><PERSON><PERSON>/security/",
        "teaser": null
      },{
        "title": "Transport",
        "excerpt":"Transport   Transport layer specifications documentation.  ","categories": [],
        "tags": [],
        "url": "/<PERSON><PERSON><PERSON>/transport/",
        "teaser": null
      }]
